# Main combined .gitignore for the protec project

# --------------------
# Common ignores
# --------------------
# Node modules
node_modules/

# Debug logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# macOS system files
.DS_Store
*.pem

# Local environment files
.env*
.env*.local

# TypeScript build info
*.tsbuildinfo
next-env.d.ts

# Expo / React Native
.expo/
dist/
web-build/
expo-env.d.ts

# Native build artifacts and keys
.kotlin/
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# Metro bundler health check files
.metro-health-check*

# app-example folder (if exists)
app-example

# --------------------
# Android specific ignores (protec-app/android)
# --------------------
/protec-app/android/build/
/protec-app/android/.idea/
/protec-app/android/.gradle/
/protec-app/android/local.properties
/protec-app/android/*.iml
/protec-app/android/*.hprof
/protec-app/android/.cxx/
/protec-app/android/*.jsbundle

# --------------------
# iOS specific ignores (protec-app/ios)
# --------------------
/protec-app/ios/build/
/protec-app/ios/*.pbxuser
!/protec-app/ios/default.pbxuser
/protec-app/ios/*.mode1v3
!/protec-app/ios/default.mode1v3
/protec-app/ios/*.mode2v3
!/protec-app/ios/default.mode2v3
/protec-app/ios/*.perspectivev3
!/protec-app/ios/default.perspectivev3
/protec-app/ios/xcuserdata/
/protec-app/ios/*.xccheckout
/protec-app/ios/*.moved-aside
/protec-app/ios/DerivedData/
/protec-app/ios/*.hmap
/protec-app/ios/*.ipa
/protec-app/ios/*.xcuserstate
/protec-app/ios/project.xcworkspace
/protec-app/ios/.xcode.env.local
/protec-app/ios/*.jsbundle
/protec-app/ios/Pods/

# --------------------
# Web specific ignores (web)
# --------------------
/web/node_modules/
/web/.pnp
/web/.pnp.*
/web/.yarn/*
!/web/.yarn/patches
!/web/.yarn/plugins
!/web/.yarn/releases
!/web/.yarn/versions

/web/coverage/

/web/.next/
/web/out/
/web/build/

# Vercel deployment
/web/.vercel

# Prisma generated files
/web/app/generated/prisma
