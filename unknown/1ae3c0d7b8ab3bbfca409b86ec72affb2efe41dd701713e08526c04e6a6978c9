import { useEffect, useRef } from 'react';
import { Linking } from 'react-native';
import { router } from 'expo-router';
import * as Notifications from 'expo-notifications';

export interface DeepLinkData {
  url: string;
  path: string;
  params: Record<string, string>;
}

export interface MagicLinkData {
  token: string;
  email: string;
  redirect?: string;
}

export function useDeepLinking() {
  const isInitialized = useRef(false);

  useEffect(() => {
    if (isInitialized.current) return;
    isInitialized.current = true;

    // Handle initial URL when app is opened from a deep link
    const handleInitialURL = async () => {
      try {
        const initialUrl = await Linking.getInitialURL();
        if (initialUrl) {
          console.log('Initial URL:', initialUrl);
          handleDeepLink(initialUrl);
        }
      } catch (error) {
        console.error('Error getting initial URL:', error);
      }
    };

    // Handle URLs when app is already running
    const handleUrlChange = (event: { url: string }) => {
      console.log('URL changed:', event.url);
      handleDeepLink(event.url);
    };

    // Handle notification responses (when user taps on notification)
    const handleNotificationResponse = (response: Notifications.NotificationResponse) => {
      const data = response.notification.request.content.data;
      if (data?.url) {
        console.log('Notification URL:', data.url);
        handleDeepLink(data.url as string);
      }
    };

    // Set up listeners
    const urlSubscription = Linking.addEventListener('url', handleUrlChange);
    const notificationSubscription = Notifications.addNotificationResponseReceivedListener(
      handleNotificationResponse
    );

    // Handle initial URL
    handleInitialURL();

    // Cleanup
    return () => {
      urlSubscription?.remove();
      notificationSubscription?.remove();
    };
  }, []);

  /**
   * Parse deep link URL and extract data
   */
  const parseDeepLink = (url: string): DeepLinkData | null => {
    try {
      const urlObj = new URL(url);
      const path = urlObj.pathname;
      const params: Record<string, string> = {};

      // Extract query parameters
      urlObj.searchParams.forEach((value, key) => {
        params[key] = value;
      });

      return {
        url,
        path,
        params,
      };
    } catch (error) {
      console.error('Error parsing deep link:', error);
      return null;
    }
  };

  /**
   * Extract magic link data from URL
   */
  const parseMagicLink = (url: string): MagicLinkData | null => {
    const linkData = parseDeepLink(url);
    if (!linkData) return null;

    const { params } = linkData;
    
    if (!params.token || !params.email) {
      console.error('Missing required magic link parameters');
      return null;
    }

    return {
      token: params.token,
      email: params.email,
      redirect: params.redirect,
    };
  };

  /**
   * Handle different types of deep links
   */
  const handleDeepLink = (url: string) => {
    try {
      const linkData = parseDeepLink(url);
      if (!linkData) return;

      const { path, params } = linkData;

      console.log('Handling deep link:', { path, params });

      // Handle magic link verification
      if (path === '/auth/verify' || path.includes('auth/verify')) {
        const magicLinkData = parseMagicLink(url);
        if (magicLinkData) {
          // Navigate to verification screen with magic link data
          router.push({
            pathname: '/auth/verify',
            params: {
              token: magicLinkData.token,
              email: magicLinkData.email,
              redirect: magicLinkData.redirect || '/(tabs)',
            },
          });
          return;
        }
      }

      // Handle password reset links
      if (path === '/auth/reset-password' || path.includes('reset-password')) {
        if (params.token && params.email) {
          router.push({
            pathname: '/auth/reset-password',
            params: {
              token: params.token,
              email: params.email,
            },
          });
          return;
        }
      }

      // Handle profile sharing links
      if (path.startsWith('/profile/') || path.includes('profile')) {
        const profileId = params.id || path.split('/').pop();
        if (profileId) {
          router.push({
            pathname: '/profile/[id]',
            params: { id: profileId },
          });
          return;
        }
      }

      // Handle event sharing links
      if (path.startsWith('/events/') || path.includes('events')) {
        const eventId = params.id || path.split('/').pop();
        if (eventId) {
          router.push({
            pathname: '/events/[id]',
            params: { id: eventId },
          });
          return;
        }
      }

      // Handle post sharing links
      if (path.startsWith('/posts/') || path.includes('posts')) {
        const postId = params.id || path.split('/').pop();
        if (postId) {
          router.push({
            pathname: '/posts/[id]',
            params: { id: postId },
          });
          return;
        }
      }

      // Handle general navigation
      if (path && path !== '/') {
        // Remove leading slash and convert to expo-router format
        const routePath = path.startsWith('/') ? path.slice(1) : path;
        router.push(routePath as any);
        return;
      }

      // Default: navigate to home
      router.push('/(tabs)');
    } catch (error) {
      console.error('Error handling deep link:', error);
      // Fallback to home screen
      router.push('/(tabs)');
    }
  };

  /**
   * Create a deep link URL for sharing
   */
  const createDeepLink = (path: string, params?: Record<string, string>): string => {
    const baseUrl = 'protec-alumni://';
    const cleanPath = path.startsWith('/') ? path.slice(1) : path;
    
    if (params && Object.keys(params).length > 0) {
      const searchParams = new URLSearchParams(params);
      return `${baseUrl}${cleanPath}?${searchParams.toString()}`;
    }
    
    return `${baseUrl}${cleanPath}`;
  };

  /**
   * Open a deep link programmatically
   */
  const openDeepLink = async (url: string): Promise<boolean> => {
    try {
      const canOpen = await Linking.canOpenURL(url);
      if (canOpen) {
        await Linking.openURL(url);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error opening deep link:', error);
      return false;
    }
  };

  return {
    parseDeepLink,
    parseMagicLink,
    handleDeepLink,
    createDeepLink,
    openDeepLink,
  };
}

export default useDeepLinking;
