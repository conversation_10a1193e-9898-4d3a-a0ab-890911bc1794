import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '../api/trpc';
import { useAuth } from '../providers/AuthProvider';

// Query keys
export const postsKeys = {
  all: ['posts'] as const,
  lists: () => [...postsKeys.all, 'list'] as const,
  list: (filters: any) => [...postsKeys.lists(), { filters }] as const,
  details: () => [...postsKeys.all, 'detail'] as const,
  detail: (id: string) => [...postsKeys.details(), id] as const,
  comments: (postId: string) => [...postsKeys.all, 'comments', postId] as const,
};

// Get all posts with filters
export function usePosts(filters?: {
  authorId?: string;
  tags?: string[];
  limit?: number;
  cursor?: string;
}) {
  const { isAuthenticated } = useAuth();

  return useQuery({
    queryKey: postsKeys.list(filters),
    queryFn: async () => {
      return await api.posts.getAll.query({
        authorId: filters?.authorId,
        tags: filters?.tags,
        limit: filters?.limit || 20,
        cursor: filters?.cursor,
      });
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    enabled: isAuthenticated,
  });
}

// Get single post
export function usePost(id: string) {
  const { isAuthenticated } = useAuth();

  return useQuery({
    queryKey: postsKeys.detail(id),
    queryFn: async () => {
      return await api.posts.getById.query({ id });
    },
    enabled: !!id && isAuthenticated,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Get posts by current user
export function useMyPosts() {
  const { user, isAuthenticated } = useAuth();

  return useQuery({
    queryKey: postsKeys.list({ authorId: user?.id }),
    queryFn: async () => {
      if (!user?.id) return { posts: [], nextCursor: undefined };
      
      return await api.posts.getAll.query({
        authorId: user.id,
        limit: 50,
      });
    },
    enabled: isAuthenticated && !!user?.id,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

// Get posts by tags
export function usePostsByTags(tags: string[]) {
  const { isAuthenticated } = useAuth();

  return useQuery({
    queryKey: postsKeys.list({ tags }),
    queryFn: async () => {
      return await api.posts.getAll.query({
        tags,
        limit: 20,
      });
    },
    enabled: isAuthenticated && tags.length > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Get comments for a post
export function usePostComments(postId: string, options?: {
  limit?: number;
  cursor?: string;
}) {
  const { isAuthenticated } = useAuth();

  return useQuery({
    queryKey: postsKeys.comments(postId),
    queryFn: async () => {
      return await api.posts.getComments.query({
        postId,
        limit: options?.limit || 20,
        cursor: options?.cursor,
      });
    },
    enabled: !!postId && isAuthenticated,
    staleTime: 1 * 60 * 1000, // 1 minute
  });
}

// Create new post
export function useCreatePost() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (postData: {
      content: string;
      tags?: string[];
      mediaUrls?: string[];
    }) => {
      return await api.posts.create.mutate({
        content: postData.content,
        tags: postData.tags || [],
        mediaUrls: postData.mediaUrls || [],
      });
    },
    onSuccess: () => {
      // Invalidate posts queries to refetch
      queryClient.invalidateQueries({ queryKey: postsKeys.all });
    },
  });
}

// Update post
export function useUpdatePost() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (postData: {
      id: string;
      content?: string;
      tags?: string[];
      mediaUrls?: string[];
    }) => {
      return await api.posts.update.mutate({
        id: postData.id,
        content: postData.content,
        tags: postData.tags,
        mediaUrls: postData.mediaUrls,
      });
    },
    onSuccess: (data, variables) => {
      // Invalidate posts queries
      queryClient.invalidateQueries({ queryKey: postsKeys.all });
      
      // Update specific post in cache
      queryClient.setQueryData(postsKeys.detail(variables.id), data);
    },
  });
}

// Delete post
export function useDeletePost() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (postId: string) => {
      return await api.posts.delete.mutate({ id: postId });
    },
    onSuccess: () => {
      // Invalidate posts queries to refetch
      queryClient.invalidateQueries({ queryKey: postsKeys.all });
    },
  });
}

// Toggle like on post
export function useTogglePostLike() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (postId: string) => {
      return await api.posts.toggleLike.mutate({ postId });
    },
    onSuccess: (data, postId) => {
      // Update the specific post in cache
      queryClient.setQueryData(postsKeys.detail(postId), (oldData: any) => {
        if (oldData) {
          return {
            ...oldData,
            likes: data.likes,
          };
        }
        return oldData;
      });

      // Invalidate posts lists to update like counts
      queryClient.invalidateQueries({ queryKey: postsKeys.lists() });
    },
  });
}

// Add comment to post
export function useAddComment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ postId, text }: { postId: string; text: string }) => {
      return await api.posts.addComment.mutate({ postId, text });
    },
    onSuccess: (data, variables) => {
      // Invalidate comments for this post
      queryClient.invalidateQueries({ queryKey: postsKeys.comments(variables.postId) });
      
      // Invalidate posts to update comment counts
      queryClient.invalidateQueries({ queryKey: postsKeys.all });
    },
  });
}

// Delete comment
export function useDeleteComment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ commentId, postId }: { commentId: string; postId: string }) => {
      return await api.posts.deleteComment.mutate({ id: commentId });
    },
    onSuccess: (data, variables) => {
      // Invalidate comments for this post
      queryClient.invalidateQueries({ queryKey: postsKeys.comments(variables.postId) });
      
      // Invalidate posts to update comment counts
      queryClient.invalidateQueries({ queryKey: postsKeys.all });
    },
  });
}

// Get trending posts
export function useTrendingPosts(limit: number = 10) {
  const { isAuthenticated } = useAuth();

  return useQuery({
    queryKey: [...postsKeys.all, 'trending', limit],
    queryFn: async () => {
      // For now, get recent posts with high engagement
      // In the future, this could be a dedicated trending endpoint
      return await api.posts.getAll.query({
        limit,
      });
    },
    enabled: isAuthenticated,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Search posts
export function useSearchPosts(query: string) {
  const { isAuthenticated } = useAuth();

  return useQuery({
    queryKey: [...postsKeys.all, 'search', query],
    queryFn: async () => {
      if (!query.trim()) return { posts: [], nextCursor: undefined };
      
      // For now, search by tags that match the query
      // In the future, this could be full-text search
      const searchTags = [query.toLowerCase().replace(/[^a-z0-9]/g, '')];
      
      return await api.posts.getAll.query({
        tags: searchTags,
        limit: 20,
      });
    },
    enabled: query.length > 2 && isAuthenticated,
    staleTime: 30 * 1000, // 30 seconds
  });
}

export default {
  usePosts,
  usePost,
  useMyPosts,
  usePostsByTags,
  usePostComments,
  useCreatePost,
  useUpdatePost,
  useDeletePost,
  useTogglePostLike,
  useAddComment,
  useDeleteComment,
  useTrendingPosts,
  useSearchPosts,
};
