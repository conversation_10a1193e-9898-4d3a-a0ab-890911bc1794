import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import MagicLinkService from '@/services/magicLinkService';
import { Ionicons } from '@expo/vector-icons';

export default function CheckEmailScreen() {
  const { email } = useLocalSearchParams<{ email: string }>();
  const [isResending, setIsResending] = useState(false);
  const [resendCooldown, setResendCooldown] = useState(0);
  const [canResend, setCanResend] = useState(false);

  useEffect(() => {
    // Start cooldown timer (60 seconds)
    setResendCooldown(60);
    const timer = setInterval(() => {
      setResendCooldown((prev) => {
        if (prev <= 1) {
          setCanResend(true);
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const handleResendMagicLink = async () => {
    if (!email || !canResend || isResending) return;

    setIsResending(true);
    try {
      const result = await MagicLinkService.sendMagicLink({ email });
      
      if (result.success) {
        Alert.alert(
          'Magic Link Sent',
          'A new magic link has been sent to your email address.',
          [{ text: 'OK' }]
        );
        
        // Reset cooldown
        setCanResend(false);
        setResendCooldown(60);
        
        const timer = setInterval(() => {
          setResendCooldown((prev) => {
            if (prev <= 1) {
              setCanResend(true);
              clearInterval(timer);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
      } else {
        Alert.alert(
          'Error',
          result.error || 'Failed to resend magic link. Please try again.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Error resending magic link:', error);
      Alert.alert(
        'Error',
        'Network error. Please check your connection and try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsResending(false);
    }
  };

  const handleBackToLogin = () => {
    router.replace('/auth/login');
  };

  const handleChangeEmail = () => {
    router.replace('/auth/login');
  };

  const handleOpenEmailApp = () => {
    // This would open the default email app on the device
    Alert.alert(
      'Open Email App',
      'Please check your email app for the magic link.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Open Email', onPress: () => {
          // In a real implementation, you might use Linking.openURL with a mailto: scheme
          // or a platform-specific method to open the email app
          console.log('Opening email app...');
        }},
      ]
    );
  };

  return (
    <ThemedView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={handleBackToLogin}
        >
          <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
        </TouchableOpacity>
        <ThemedText style={styles.headerTitle}>Check Your Email</ThemedText>
      </View>

      <View style={styles.content}>
        <View style={styles.iconContainer}>
          <Ionicons name="mail" size={80} color="#012A5B" />
        </View>

        <ThemedText style={styles.title}>Magic Link Sent!</ThemedText>
        
        <ThemedText style={styles.description}>
          We've sent a magic link to:
        </ThemedText>
        
        <View style={styles.emailContainer}>
          <ThemedText style={styles.emailText}>{email}</ThemedText>
        </View>

        <ThemedText style={styles.instructions}>
          Click the link in your email to sign in to your PROTEC Alumni account. 
          The link will expire in 15 minutes for security.
        </ThemedText>

        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={styles.primaryButton}
            onPress={handleOpenEmailApp}
          >
            <Ionicons name="mail-open" size={20} color="#FFFFFF" style={styles.buttonIcon} />
            <ThemedText style={styles.primaryButtonText}>
              Open Email App
            </ThemedText>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.secondaryButton,
              (!canResend || isResending) && styles.disabledButton
            ]}
            onPress={handleResendMagicLink}
            disabled={!canResend || isResending}
          >
            {isResending ? (
              <ActivityIndicator size="small" color="#012A5B" />
            ) : (
              <>
                <Ionicons name="refresh" size={20} color="#012A5B" style={styles.buttonIcon} />
                <ThemedText style={styles.secondaryButtonText}>
                  {canResend ? 'Resend Magic Link' : `Resend in ${resendCooldown}s`}
                </ThemedText>
              </>
            )}
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.tertiaryButton}
            onPress={handleChangeEmail}
          >
            <ThemedText style={styles.tertiaryButtonText}>
              Use Different Email
            </ThemedText>
          </TouchableOpacity>
        </View>

        <View style={styles.helpContainer}>
          <ThemedText style={styles.helpTitle}>Didn't receive the email?</ThemedText>
          <ThemedText style={styles.helpText}>
            • Check your spam or junk folder{'\n'}
            • Make sure you entered the correct email address{'\n'}
            • Wait a few minutes for the email to arrive{'\n'}
            • Try resending the magic link
          </ThemedText>
        </View>
      </View>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    paddingTop: 60,
    paddingHorizontal: 24,
    paddingBottom: 24,
    backgroundColor: '#012A5B',
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    marginRight: 16,
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 40,
    alignItems: 'center',
  },
  iconContainer: {
    marginBottom: 32,
    backgroundColor: '#F1F5F9',
    borderRadius: 50,
    padding: 24,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1E293B',
    textAlign: 'center',
    marginBottom: 16,
  },
  description: {
    fontSize: 16,
    color: '#64748B',
    textAlign: 'center',
    marginBottom: 8,
  },
  emailContainer: {
    backgroundColor: '#F1F5F9',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    marginBottom: 24,
  },
  emailText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#012A5B',
  },
  instructions: {
    fontSize: 14,
    color: '#64748B',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 40,
  },
  buttonContainer: {
    width: '100%',
    gap: 12,
    marginBottom: 40,
  },
  primaryButton: {
    backgroundColor: '#012A5B',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  secondaryButton: {
    backgroundColor: '#FFFFFF',
    borderWidth: 2,
    borderColor: '#012A5B',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  disabledButton: {
    opacity: 0.5,
  },
  tertiaryButton: {
    backgroundColor: 'transparent',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: 'center',
  },
  buttonIcon: {
    marginRight: 8,
  },
  primaryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButtonText: {
    color: '#012A5B',
    fontSize: 16,
    fontWeight: '600',
  },
  tertiaryButtonText: {
    color: '#64748B',
    fontSize: 16,
    fontWeight: '500',
  },
  helpContainer: {
    backgroundColor: '#F8FAFC',
    padding: 20,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E2E8F0',
    width: '100%',
  },
  helpTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1E293B',
    marginBottom: 8,
  },
  helpText: {
    fontSize: 14,
    color: '#64748B',
    lineHeight: 20,
  },
});
