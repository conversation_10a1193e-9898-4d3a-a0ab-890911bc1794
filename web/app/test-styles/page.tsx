"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

export default function TestStylesPage() {
  return (
    <div className="min-h-screen p-8 space-y-8" style={{ backgroundColor: 'hsl(var(--background))', color: 'hsl(var(--foreground))' }}>
      {/* Test Header */}
      <div className="text-center space-y-4">
        <h1 style={{ color: 'hsl(var(--protec-navy))' }} className="text-4xl font-bold">
          PROTEC Styling Test
        </h1>
        <p className="text-lg" style={{ color: 'hsl(var(--muted-foreground))' }}>
          Testing if PROTEC brand colors and styling are working correctly
        </p>
      </div>

      {/* Test Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card style={{ borderLeft: '4px solid hsl(var(--protec-red))' }}>
          <CardHeader>
            <CardTitle style={{ color: 'hsl(var(--protec-navy))' }}>
              PROTEC Navy Title
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p style={{ color: 'hsl(var(--muted-foreground))' }}>
              This card uses PROTEC navy for the title and red for the accent border.
            </p>
            <Button 
              className="mt-4"
              style={{ 
                backgroundColor: 'hsl(var(--protec-red))', 
                color: 'white' 
              }}
            >
              PROTEC Red Button
            </Button>
          </CardContent>
        </Card>

        <Card style={{ backgroundColor: 'hsl(var(--protec-gray))' }}>
          <CardHeader>
            <CardTitle style={{ color: 'hsl(var(--protec-navy))' }}>
              Gray Background
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p style={{ color: 'hsl(var(--protec-navy))' }}>
              This card has a PROTEC gray background.
            </p>
            <div className="flex gap-2 mt-4">
              <Badge style={{ backgroundColor: 'hsl(var(--protec-navy))', color: 'white' }}>
                Navy Badge
              </Badge>
              <Badge style={{ backgroundColor: 'hsl(var(--protec-red))', color: 'white' }}>
                Red Badge
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle style={{ color: 'hsl(var(--protec-navy))' }}>
              Standard Card
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p style={{ color: 'hsl(var(--muted-foreground))' }}>
              This is a standard card with default styling.
            </p>
            <Button 
              variant="outline" 
              className="mt-4"
              style={{ 
                borderColor: 'hsl(var(--protec-navy))', 
                color: 'hsl(var(--protec-navy))' 
              }}
            >
              Outline Button
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Test Gradient */}
      <Card>
        <div 
          className="p-8 text-white text-center rounded-lg"
          style={{ 
            background: 'linear-gradient(135deg, hsl(var(--protec-navy)) 0%, hsl(var(--protec-red)) 100%)' 
          }}
        >
          <h2 className="text-2xl font-bold mb-4">PROTEC Gradient</h2>
          <p className="text-lg opacity-90">
            This section uses the PROTEC brand gradient from navy to red.
          </p>
        </div>
      </Card>

      {/* Color Swatches */}
      <Card>
        <CardHeader>
          <CardTitle style={{ color: 'hsl(var(--protec-navy))' }}>
            Color Swatches
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <div 
                className="w-full h-20 rounded-lg mb-2"
                style={{ backgroundColor: 'hsl(var(--protec-navy))' }}
              ></div>
              <p className="text-sm font-medium">PROTEC Navy</p>
              <p className="text-xs" style={{ color: 'hsl(var(--muted-foreground))' }}>
                hsl(var(--protec-navy))
              </p>
            </div>
            <div className="text-center">
              <div 
                className="w-full h-20 rounded-lg mb-2"
                style={{ backgroundColor: 'hsl(var(--protec-red))' }}
              ></div>
              <p className="text-sm font-medium">PROTEC Red</p>
              <p className="text-xs" style={{ color: 'hsl(var(--muted-foreground))' }}>
                hsl(var(--protec-red))
              </p>
            </div>
            <div className="text-center">
              <div 
                className="w-full h-20 rounded-lg mb-2 border"
                style={{ backgroundColor: 'hsl(var(--protec-gray))' }}
              ></div>
              <p className="text-sm font-medium">PROTEC Gray</p>
              <p className="text-xs" style={{ color: 'hsl(var(--muted-foreground))' }}>
                hsl(var(--protec-gray))
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Test Typography */}
      <Card>
        <CardHeader>
          <CardTitle style={{ color: 'hsl(var(--protec-navy))' }}>
            Typography Test
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <h1 className="text-4xl font-bold" style={{ color: 'hsl(var(--protec-navy))' }}>
            Heading 1 - 4xl Bold
          </h1>
          <h2 className="text-3xl font-semibold" style={{ color: 'hsl(var(--protec-navy))' }}>
            Heading 2 - 3xl Semibold
          </h2>
          <h3 className="text-2xl font-semibold" style={{ color: 'hsl(var(--protec-navy))' }}>
            Heading 3 - 2xl Semibold
          </h3>
          <p className="text-base" style={{ color: 'hsl(var(--foreground))' }}>
            Body text - base regular with proper contrast
          </p>
          <p className="text-sm" style={{ color: 'hsl(var(--muted-foreground))' }}>
            Small text - sm muted for secondary information
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
