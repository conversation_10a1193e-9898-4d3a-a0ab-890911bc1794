"use client"

import { useSession } from "next-auth/react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { MainNav } from "@/components/navigation/main-nav"
import { SubscriptionManager } from "@/components/donations/subscription-manager"
import { api } from "@/components/providers/trpc-provider"
import { 
  Heart, 
  TrendingUp, 
  Calendar, 
  Download,
  Plus,
  DollarSign,
  Clock,
  CheckCircle
} from "lucide-react"
import Link from "next/link"

export default function DonationsDashboardPage() {
  const { data: session } = useSession()

  const { data: donations, isLoading } = api.donations.getMyDonations.useQuery(
    undefined,
    { enabled: !!session }
  )

  const { data: stats } = api.donations.getStats.useQuery()

  // Calculate user's donation stats
  const userStats = donations ? {
    totalDonated: donations
      .filter(d => d.status === 'completed')
      .reduce((sum, d) => sum + d.amountZAR, 0),
    totalDonations: donations.filter(d => d.status === 'completed').length,
    recurringDonations: donations.filter(d => d.isRecurring && d.status !== 'cancelled').length,
    lastDonation: donations
      .filter(d => d.status === 'completed')
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())[0]
  } : null

  if (!session) {
    return (
      <div className="flex min-h-screen flex-col">
        <MainNav />
        <main className="flex-1 flex items-center justify-center">
          <Card className="w-full max-w-md">
            <CardContent className="pt-6 text-center">
              <p className="text-muted-foreground mb-4">Please sign in to view your donation dashboard</p>
              <Button asChild>
                <Link href="/auth/signin">Sign In</Link>
              </Button>
            </CardContent>
          </Card>
        </main>
      </div>
    )
  }

  return (
    <div className="flex min-h-screen flex-col">
      <MainNav />
      
      <main className="flex-1 container mx-auto px-6 py-8">
        <div className="space-y-8">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-protec-navy">Donation Dashboard</h1>
              <p className="text-muted-foreground">
                Manage your donations and track your impact
              </p>
            </div>
            <Button asChild className="bg-protec-red hover:bg-protec-red/90">
              <Link href="/donations">
                <Plus className="w-4 h-4 mr-2" />
                New Donation
              </Link>
            </Button>
          </div>

          {/* Stats Cards */}
          {userStats && (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Donated</CardTitle>
                  <DollarSign className="h-4 w-4 text-protec-red" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-protec-navy">
                    R{userStats.totalDonated.toLocaleString()}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Across {userStats.totalDonations} donations
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Active Subscriptions</CardTitle>
                  <Calendar className="h-4 w-4 text-blue-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-protec-navy">
                    {userStats.recurringDonations}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Recurring donations
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Last Donation</CardTitle>
                  <Clock className="h-4 w-4 text-green-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-protec-navy">
                    {userStats.lastDonation 
                      ? `R${userStats.lastDonation.amountZAR.toLocaleString()}`
                      : 'None'
                    }
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {userStats.lastDonation 
                      ? new Date(userStats.lastDonation.createdAt).toLocaleDateString()
                      : 'No donations yet'
                    }
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Impact Rank</CardTitle>
                  <TrendingUp className="h-4 w-4 text-purple-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-protec-navy">
                    Top 10%
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Among all donors
                  </p>
                </CardContent>
              </Card>
            </div>
          )}

          <div className="grid gap-8 lg:grid-cols-3">
            {/* Subscription Manager */}
            <div className="lg:col-span-2">
              <SubscriptionManager />
            </div>

            {/* Recent Donations */}
            <div>
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Heart className="w-5 h-5" />
                    <span>Recent Donations</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {isLoading ? (
                    <div className="space-y-4">
                      {Array.from({ length: 3 }).map((_, i) => (
                        <div key={i} className="animate-pulse">
                          <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                          <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                        </div>
                      ))}
                    </div>
                  ) : donations && donations.length > 0 ? (
                    <div className="space-y-4">
                      {donations.slice(0, 5).map((donation) => (
                        <div key={donation.id} className="flex items-center justify-between p-3 border rounded-lg">
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-protec-red/10 rounded-full flex items-center justify-center">
                              {donation.status === 'completed' ? (
                                <CheckCircle className="w-4 h-4 text-green-500" />
                              ) : (
                                <Clock className="w-4 h-4 text-yellow-500" />
                              )}
                            </div>
                            <div>
                              <p className="font-medium">R{donation.amountZAR.toLocaleString()}</p>
                              <p className="text-xs text-muted-foreground capitalize">
                                {donation.purpose}
                              </p>
                            </div>
                          </div>
                          <div className="text-right">
                            <Badge 
                              variant={donation.status === 'completed' ? 'default' : 'secondary'}
                              className={donation.status === 'completed' ? 'bg-green-100 text-green-800' : ''}
                            >
                              {donation.status}
                            </Badge>
                            <p className="text-xs text-muted-foreground mt-1">
                              {new Date(donation.createdAt).toLocaleDateString()}
                            </p>
                          </div>
                        </div>
                      ))}
                      
                      {donations.length > 5 && (
                        <Button variant="outline" className="w-full" asChild>
                          <Link href="/dashboard/donations/history">
                            View All Donations
                          </Link>
                        </Button>
                      )}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <Heart className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-medium mb-2">No Donations Yet</h3>
                      <p className="text-muted-foreground mb-4">
                        Start making a difference today!
                      </p>
                      <Button asChild className="bg-protec-red hover:bg-protec-red/90">
                        <Link href="/donations">Make Your First Donation</Link>
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Impact Summary */}
          {userStats && userStats.totalDonated > 0 && (
            <Card className="bg-gradient-to-r from-protec-navy to-protec-red text-white">
              <CardContent className="pt-6">
                <h3 className="text-xl font-bold mb-4">Your Impact</h3>
                <div className="grid md:grid-cols-3 gap-4">
                  <div>
                    <p className="text-blue-100">Students Supported</p>
                    <p className="text-2xl font-bold">
                      {Math.floor(userStats.totalDonated / 500)}
                    </p>
                  </div>
                  <div>
                    <p className="text-blue-100">Months of Support</p>
                    <p className="text-2xl font-bold">
                      {Math.floor(userStats.totalDonated / 200)}
                    </p>
                  </div>
                  <div>
                    <p className="text-blue-100">Programs Funded</p>
                    <p className="text-2xl font-bold">
                      {Math.floor(userStats.totalDonated / 1000)}
                    </p>
                  </div>
                </div>
                <p className="text-blue-100 mt-4">
                  Thank you for your generous support! Your donations have made a real difference 
                  in the lives of PROTEC students and alumni.
                </p>
              </CardContent>
            </Card>
          )}

          {/* Quick Actions */}
          <div className="grid gap-4 md:grid-cols-3">
            <Button asChild variant="outline" className="h-16">
              <Link href="/donations">
                <div className="text-center">
                  <Plus className="w-6 h-6 mx-auto mb-1" />
                  <span>Make a Donation</span>
                </div>
              </Link>
            </Button>
            
            <Button asChild variant="outline" className="h-16">
              <Link href="/dashboard/donations/receipts">
                <div className="text-center">
                  <Download className="w-6 h-6 mx-auto mb-1" />
                  <span>Download Receipts</span>
                </div>
              </Link>
            </Button>
            
            <Button asChild variant="outline" className="h-16">
              <Link href="/impact">
                <div className="text-center">
                  <TrendingUp className="w-6 h-6 mx-auto mb-1" />
                  <span>View Impact Report</span>
                </div>
              </Link>
            </Button>
          </div>
        </div>
      </main>
    </div>
  )
}
