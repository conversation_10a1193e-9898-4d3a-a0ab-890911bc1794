import { NextRequest } from 'next/server'
import { Server as NetServer } from 'http'
import { Server as SocketIOServer } from 'socket.io'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

interface SocketWithIO extends NetServer {
  io?: SocketIOServer
}

interface ExtendedNextApiResponse {
  socket: {
    server: SocketWithIO
  }
}

// Store active users and their socket connections
const activeUsers = new Map<string, { socketId: string; lastSeen: Date }>()
const userSockets = new Map<string, string>() // userId -> socketId
const socketUsers = new Map<string, string>() // socketId -> userId

export async function GET(req: NextRequest) {
  // This endpoint is used to initialize the WebSocket server
  // In a production environment, you might want to use a separate WebSocket server
  
  return new Response(JSON.stringify({ 
    message: 'WebSocket server endpoint',
    status: 'ready'
  }), {
    status: 200,
    headers: { 'Content-Type': 'application/json' }
  })
}

export async function POST(req: NextRequest) {
  try {
    const res = req as any as ExtendedNextApiResponse
    
    if (!res.socket.server.io) {
      console.log('Setting up Socket.IO server...')
      
      const io = new SocketIOServer(res.socket.server, {
        path: '/api/socket',
        addTrailingSlash: false,
        cors: {
          origin: process.env.NEXTAUTH_URL || "http://localhost:3000",
          methods: ["GET", "POST"],
          credentials: true
        }
      })

      // Authentication middleware
      io.use(async (socket, next) => {
        try {
          const token = socket.handshake.auth.token
          if (!token) {
            return next(new Error('Authentication error: No token provided'))
          }

          // Verify the session token
          // In a real implementation, you'd verify the JWT token here
          // For now, we'll assume the token is valid if it exists
          
          const session = await getServerSession(authOptions)
          if (!session?.user?.email) {
            return next(new Error('Authentication error: Invalid session'))
          }

          // Get user from database
          const user = await prisma.alumni.findUnique({
            where: { email: session.user.email },
            select: { id: true, name: true, email: true, photoUrl: true }
          })

          if (!user) {
            return next(new Error('Authentication error: User not found'))
          }

          socket.data.user = user
          next()
        } catch (error) {
          console.error('Socket authentication error:', error)
          next(new Error('Authentication error'))
        }
      })

      io.on('connection', (socket) => {
        const user = socket.data.user
        console.log(`User ${user.name} connected with socket ${socket.id}`)

        // Store user connection
        activeUsers.set(user.id, { socketId: socket.id, lastSeen: new Date() })
        userSockets.set(user.id, socket.id)
        socketUsers.set(socket.id, user.id)

        // Notify others that user is online
        socket.broadcast.emit('user_online', {
          userId: user.id,
          userName: user.name,
          userPhoto: user.photoUrl
        })

        // Join conversation rooms
        socket.on('join_conversation', async (data) => {
          try {
            const { conversationId } = data

            // Verify user is participant in conversation
            const participant = await prisma.conversationParticipant.findFirst({
              where: {
                conversationId,
                alumniId: user.id,
                isActive: true
              }
            })

            if (participant) {
              socket.join(`conversation:${conversationId}`)
              console.log(`User ${user.name} joined conversation ${conversationId}`)
            }
          } catch (error) {
            console.error('Error joining conversation:', error)
          }
        })

        socket.on('leave_conversation', (data) => {
          const { conversationId } = data
          socket.leave(`conversation:${conversationId}`)
          console.log(`User ${user.name} left conversation ${conversationId}`)
        })

        // Handle sending messages
        socket.on('send_message', async (data) => {
          try {
            const { conversationId, content, messageType = 'text' } = data

            // Verify user is participant
            const participant = await prisma.conversationParticipant.findFirst({
              where: {
                conversationId,
                alumniId: user.id,
                isActive: true
              }
            })

            if (!participant) {
              socket.emit('error', { message: 'Not authorized to send message' })
              return
            }

            // Create message in database
            const message = await prisma.message.create({
              data: {
                conversationId,
                senderId: user.id,
                content,
                messageType
              },
              include: {
                sender: {
                  select: {
                    id: true,
                    name: true,
                    photoUrl: true
                  }
                }
              }
            })

            // Update conversation last message time
            await prisma.conversation.update({
              where: { id: conversationId },
              data: { lastMessageAt: new Date() }
            })

            // Emit to all participants in the conversation
            io.to(`conversation:${conversationId}`).emit('new_message', {
              ...message,
              conversationId
            })

            console.log(`Message sent in conversation ${conversationId} by ${user.name}`)
          } catch (error) {
            console.error('Error sending message:', error)
            socket.emit('error', { message: 'Failed to send message' })
          }
        })

        // Handle typing indicators
        socket.on('start_typing', (data) => {
          const { conversationId } = data
          socket.to(`conversation:${conversationId}`).emit('user_typing', {
            conversationId,
            userId: user.id,
            userName: user.name
          })
        })

        socket.on('stop_typing', (data) => {
          const { conversationId } = data
          socket.to(`conversation:${conversationId}`).emit('user_stop_typing', {
            conversationId,
            userId: user.id,
            userName: user.name
          })
        })

        // Handle message read status
        socket.on('mark_messages_read', async (data) => {
          try {
            const { conversationId, messageIds } = data

            // Update read status in database
            await prisma.messageRead.createMany({
              data: messageIds.map((messageId: string) => ({
                messageId,
                alumniId: user.id,
                readAt: new Date()
              })),
              skipDuplicates: true
            })

            // Notify other participants
            socket.to(`conversation:${conversationId}`).emit('messages_read', {
              conversationId,
              messageIds,
              readBy: user.id,
              readAt: new Date()
            })
          } catch (error) {
            console.error('Error marking messages as read:', error)
          }
        })

        // Handle disconnect
        socket.on('disconnect', async (reason) => {
          console.log(`User ${user.name} disconnected: ${reason}`)

          // Remove from active users
          activeUsers.delete(user.id)
          userSockets.delete(user.id)
          socketUsers.delete(socket.id)

          // Update last seen in database
          try {
            await prisma.alumni.update({
              where: { id: user.id },
              data: { lastLoginAt: new Date() }
            })
          } catch (error) {
            console.error('Error updating last seen:', error)
          }

          // Notify others that user is offline
          socket.broadcast.emit('user_offline', {
            userId: user.id,
            lastSeen: new Date()
          })
        })

        // Handle errors
        socket.on('error', (error) => {
          console.error('Socket error:', error)
        })
      })

      res.socket.server.io = io
      console.log('Socket.IO server initialized')
    } else {
      console.log('Socket.IO server already running')
    }

    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    })
  } catch (error) {
    console.error('Socket.IO setup error:', error)
    return new Response(JSON.stringify({ error: 'Failed to setup WebSocket' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    })
  }
}

// Helper function to get online users
export function getOnlineUsers() {
  return Array.from(activeUsers.entries()).map(([userId, data]) => ({
    userId,
    socketId: data.socketId,
    lastSeen: data.lastSeen
  }))
}

// Helper function to send message to specific user
export function sendToUser(userId: string, event: string, data: any) {
  const socketId = userSockets.get(userId)
  if (socketId) {
    // This would need access to the io instance
    // In a real implementation, you'd store the io instance globally
    console.log(`Sending ${event} to user ${userId}`)
  }
}
