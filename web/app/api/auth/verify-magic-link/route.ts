import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { securityService } from '@/lib/services/security-service'
import { sign } from 'jsonwebtoken'
import * as crypto from 'crypto'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { token, email } = body

    if (!token || !email) {
      return NextResponse.json(
        { error: 'Missing token or email' },
        { status: 400 }
      )
    }

    // Hash the token to match what was stored
    const hashedToken = crypto.createHash('sha256').update(token).digest('hex')

    // Find the verification token
    const verificationToken = await prisma.verificationToken.findUnique({
      where: {
        identifier_token: {
          identifier: email,
          token: hashedToken
        }
      }
    })

    if (!verificationToken) {
      await securityService.logSecurityEvent({
        type: 'INVALID_MAGIC_LINK',
        userId: email,
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
        metadata: {
          reason: 'Token not found'
        },
        severity: 'MEDIUM'
      })

      return NextResponse.json(
        { error: 'Invalid or expired magic link' },
        { status: 400 }
      )
    }

    // Check if token has expired
    if (verificationToken.expires < new Date()) {
      // Clean up expired token
      await prisma.verificationToken.delete({
        where: {
          identifier_token: {
            identifier: email,
            token: hashedToken
          }
        }
      })

      await securityService.logSecurityEvent({
        type: 'EXPIRED_MAGIC_LINK',
        userId: email,
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
        metadata: {
          expiredAt: verificationToken.expires.toISOString()
        },
        severity: 'LOW'
      })

      return NextResponse.json(
        { error: 'Magic link has expired' },
        { status: 400 }
      )
    }

    // Find the user
    const alumni = await prisma.alumni.findUnique({
      where: { email },
      include: {
        _count: {
          select: {
            connections: true,
            posts: true,
            donations: true,
            organizedEvents: true
          }
        }
      }
    })

    if (!alumni) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Activate the user if they were pending
    if (!alumni.isActive) {
      await prisma.alumni.update({
        where: { id: alumni.id },
        data: { isActive: true }
      })
    }

    // Delete the used verification token
    await prisma.verificationToken.delete({
      where: {
        identifier_token: {
          identifier: email,
          token: hashedToken
        }
      }
    })

    // Generate JWT token for the mobile app
    const authToken = sign(
      {
        sub: alumni.id,
        email: alumni.email,
        name: alumni.name,
        role: alumni.role,
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60) // 30 days
      },
      process.env.NEXTAUTH_SECRET!
    )

    // Log successful verification
    await securityService.logSecurityEvent({
      type: 'MAGIC_LINK_VERIFIED',
      userId: email,
      ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
      metadata: {
        userId: alumni.id,
        wasActivated: !alumni.isActive
      },
      severity: 'LOW'
    })

    // Log activity
    await prisma.activity.create({
      data: {
        type: 'USER_LOGIN',
        refId: alumni.id,
        alumniId: alumni.id,
        metadata: {
          method: 'magic-link',
          userAgent: request.headers.get('user-agent'),
          ipAddress: request.headers.get('x-forwarded-for')
        }
      }
    })

    return NextResponse.json({
      success: true,
      user: {
        id: alumni.id,
        email: alumni.email,
        name: alumni.name,
        role: alumni.role,
        isActive: alumni.isActive,
        photoUrl: alumni.photoUrl,
        graduationYear: alumni.graduationYear,
        programmes: alumni.programmes,
        currentRole: alumni.currentRole,
        company: alumni.company,
        bio: alumni.bio,
        stats: alumni._count
      },
      authToken
    })

  } catch (error) {
    console.error('Verify magic link error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
