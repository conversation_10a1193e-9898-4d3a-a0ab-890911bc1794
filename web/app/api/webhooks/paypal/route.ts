import { NextRequest, NextResponse } from 'next/server'
import { paymentService } from '@/lib/services/payment-service'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    // Get the raw body and headers
    const body = await request.text()
    const payload = JSON.parse(body)
    
    // Get PayPal headers for verification
    const headers: Record<string, string> = {}
    request.headers.forEach((value, key) => {
      if (key.startsWith('paypal-')) {
        headers[key] = value
      }
    })

    console.log('PayPal webhook received:', {
      event_type: payload.event_type,
      resource_type: payload.resource_type,
      headers: Object.keys(headers)
    })

    // Verify the webhook signature
    const verification = await paymentService.verifyWebhook('paypal', payload, headers)
    
    if (!verification.isValid) {
      console.error('PayPal webhook verification failed:', verification.error)
      return NextResponse.json(
        { error: 'Invalid signature' },
        { status: 400 }
      )
    }

    // Handle different PayPal webhook events
    const { event_type, resource } = payload

    switch (event_type) {
      case 'CHECKOUT.ORDER.APPROVED':
        await handleOrderApproved(resource)
        break
      case 'PAYMENT.CAPTURE.COMPLETED':
        await handlePaymentCompleted(resource)
        break
      case 'PAYMENT.CAPTURE.DENIED':
      case 'PAYMENT.CAPTURE.FAILED':
        await handlePaymentFailed(resource)
        break
      case 'BILLING.SUBSCRIPTION.CREATED':
        await handleSubscriptionCreated(resource)
        break
      case 'BILLING.SUBSCRIPTION.ACTIVATED':
        await handleSubscriptionActivated(resource)
        break
      case 'BILLING.SUBSCRIPTION.CANCELLED':
        await handleSubscriptionCancelled(resource)
        break
      case 'PAYMENT.SALE.REFUNDED':
        await handlePaymentRefunded(resource)
        break
      default:
        console.log('Unhandled PayPal webhook event:', event_type)
    }

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('PayPal webhook error:', error)
    return NextResponse.json(
      { error: 'Webhook processing failed' },
      { status: 500 }
    )
  }
}

async function handleOrderApproved(resource: any) {
  const orderId = resource.id
  const customId = resource.purchase_units?.[0]?.custom_id

  if (!customId) {
    console.error('No custom ID in PayPal order:', orderId)
    return
  }

  // Update donation status to approved (but not yet completed)
  await prisma.donation.update({
    where: { id: customId },
    data: {
      status: 'pending',
      metadata: {
        paypal: {
          order_id: orderId,
          status: 'approved',
          webhook_received_at: new Date().toISOString()
        }
      }
    }
  })

  console.log('PayPal order approved:', { orderId, donationId: customId })
}

async function handlePaymentCompleted(resource: any) {
  const captureId = resource.id
  const customId = resource.custom_id
  const amount = resource.amount?.value
  const currency = resource.amount?.currency_code

  if (!customId) {
    console.error('No custom ID in PayPal capture:', captureId)
    return
  }

  // Find the donation
  const donation = await prisma.donation.findUnique({
    where: { id: customId },
    include: { alumni: true }
  })

  if (!donation) {
    console.error('Donation not found:', customId)
    return
  }

  // Update donation status to completed
  const updatedDonation = await prisma.donation.update({
    where: { id: customId },
    data: {
      status: 'completed',
      metadata: {
        ...donation.metadata as object,
        paypal: {
          capture_id: captureId,
          amount: parseFloat(amount || '0'),
          currency,
          status: 'completed',
          webhook_received_at: new Date().toISOString()
        }
      }
    }
  })

  // Log activity
  await prisma.activity.create({
    data: {
      type: 'DONATION_COMPLETED',
      refId: customId,
      alumniId: donation.alumniId,
      metadata: {
        amount,
        currency,
        gateway: 'paypal',
        capture_id: captureId
      }
    }
  })

  console.log('PayPal payment completed:', {
    captureId,
    donationId: customId,
    amount,
    currency
  })
}

async function handlePaymentFailed(resource: any) {
  const captureId = resource.id
  const customId = resource.custom_id

  if (!customId) {
    console.error('No custom ID in PayPal failed payment:', captureId)
    return
  }

  // Update donation status to failed
  await prisma.donation.update({
    where: { id: customId },
    data: {
      status: 'failed',
      metadata: {
        paypal: {
          capture_id: captureId,
          status: 'failed',
          webhook_received_at: new Date().toISOString()
        }
      }
    }
  })

  console.log('PayPal payment failed:', { captureId, donationId: customId })
}

async function handleSubscriptionCreated(resource: any) {
  // Handle subscription creation for recurring donations
  console.log('PayPal subscription created:', resource.id)
}

async function handleSubscriptionActivated(resource: any) {
  // Handle subscription activation
  console.log('PayPal subscription activated:', resource.id)
}

async function handleSubscriptionCancelled(resource: any) {
  // Handle subscription cancellation
  console.log('PayPal subscription cancelled:', resource.id)
}

async function handlePaymentRefunded(resource: any) {
  const refundId = resource.id
  const parentPayment = resource.parent_payment
  
  console.log('PayPal payment refunded:', { refundId, parentPayment })
  
  // Find donation by PayPal payment ID and update status
  // This would require storing the PayPal payment ID in the donation metadata
}

// Handle GET requests for webhook verification
export async function GET(request: NextRequest) {
  return NextResponse.json({ message: 'PayPal webhook endpoint' })
}
