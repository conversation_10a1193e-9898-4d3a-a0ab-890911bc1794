import { NextRequest, NextResponse } from 'next/server'
import { paymentService } from '@/lib/services/payment-service'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    // Get the raw body for signature verification
    const body = await request.text()
    const payload = new URLSearchParams(body)
    const data: Record<string, string> = {}
    
    // Convert URLSearchParams to object
    for (const [key, value] of payload.entries()) {
      data[key] = value
    }

    console.log('PayFast webhook received:', data)

    // Verify the webhook signature
    const verification = await paymentService.verifyWebhook('payfast', data, {})
    
    if (!verification.isValid) {
      console.error('PayFast webhook verification failed:', verification.error)
      return NextResponse.json(
        { error: 'Invalid signature' },
        { status: 400 }
      )
    }

    // Extract payment information
    const {
      m_payment_id: donationId,
      payment_status,
      amount_gross,
      amount_fee,
      amount_net,
      pf_payment_id,
      item_name,
      item_description,
      name_first,
      name_last,
      email_address,
      signature
    } = data

    if (!donationId) {
      console.error('No donation ID in PayFast webhook')
      return NextResponse.json(
        { error: 'Missing donation ID' },
        { status: 400 }
      )
    }

    // Find the donation
    const donation = await prisma.donation.findUnique({
      where: { id: donationId },
      include: { alumni: true }
    })

    if (!donation) {
      console.error('Donation not found:', donationId)
      return NextResponse.json(
        { error: 'Donation not found' },
        { status: 404 }
      )
    }

    // Update donation status based on payment status
    let newStatus: string
    switch (payment_status) {
      case 'COMPLETE':
        newStatus = 'completed'
        break
      case 'FAILED':
        newStatus = 'failed'
        break
      case 'CANCELLED':
        newStatus = 'cancelled'
        break
      default:
        newStatus = 'pending'
    }

    // Update the donation
    const updatedDonation = await prisma.donation.update({
      where: { id: donationId },
      data: {
        status: newStatus,
        metadata: {
          ...donation.metadata as object,
          payfast: {
            payment_status,
            amount_gross: parseFloat(amount_gross || '0'),
            amount_fee: parseFloat(amount_fee || '0'),
            amount_net: parseFloat(amount_net || '0'),
            pf_payment_id,
            signature,
            webhook_received_at: new Date().toISOString()
          }
        }
      }
    })

    // Log activity
    await prisma.activity.create({
      data: {
        type: newStatus === 'completed' ? 'DONATION_COMPLETED' : 'DONATION_UPDATED',
        refId: donationId,
        alumniId: donation.alumniId,
        metadata: {
          payment_status,
          amount: amount_gross,
          gateway: 'payfast'
        }
      }
    })

    // If payment is complete and it's a recurring donation, set up next payment date
    if (newStatus === 'completed' && donation.isRecurring) {
      const nextPaymentDate = calculateNextPaymentDate(donation.frequency)
      await prisma.donation.update({
        where: { id: donationId },
        data: { nextPaymentDate }
      })
    }

    console.log('PayFast webhook processed successfully:', {
      donationId,
      status: newStatus,
      amount: amount_gross
    })

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('PayFast webhook error:', error)
    return NextResponse.json(
      { error: 'Webhook processing failed' },
      { status: 500 }
    )
  }
}

function calculateNextPaymentDate(frequency: string): Date {
  const now = new Date()
  switch (frequency) {
    case 'MONTHLY':
      return new Date(now.setMonth(now.getMonth() + 1))
    case 'QUARTERLY':
      return new Date(now.setMonth(now.getMonth() + 3))
    case 'ANNUALLY':
      return new Date(now.setFullYear(now.getFullYear() + 1))
    default:
      return new Date(now.setMonth(now.getMonth() + 1))
  }
}

// Handle GET requests for webhook verification
export async function GET(request: NextRequest) {
  return NextResponse.json({ message: 'PayFast webhook endpoint' })
}
