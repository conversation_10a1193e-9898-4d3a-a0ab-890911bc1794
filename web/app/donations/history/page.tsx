"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import { redirect } from "next/navigation"
import { MainNav } from "@/components/navigation/main-nav"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { api } from "@/components/providers/trpc-provider"
import { 
  Heart, 
  Calendar, 
  DollarSign, 
  TrendingUp, 
  Download,
  Filter,
  Search,
  ArrowLeft,
  CheckCircle,
  XCircle,
  Clock,
  Repeat
} from "lucide-react"
import { format } from "date-fns"
import Link from "next/link"

export default function DonationHistoryPage() {
  const { data: session, status } = useSession()
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [gatewayFilter, setGatewayFilter] = useState<string>("all")
  const [dateRange, setDateRange] = useState<string>("all")

  // Get user's donations
  const { data: donations, isLoading } = api.donations.getByUser.useQuery({
    status: statusFilter === "all" ? undefined : statusFilter as any,
    gateway: gatewayFilter === "all" ? undefined : gatewayFilter as any,
    limit: 50
  })

  // Get donation statistics
  const { data: stats } = api.donations.getUserStats.useQuery()

  if (status === "loading" || isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-protec-red"></div>
      </div>
    )
  }

  if (status === "unauthenticated") {
    redirect("/auth/signin")
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-600" />
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-600" />
      default:
        return <Clock className="h-4 w-4 text-gray-600" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-50 text-green-700 border-green-200'
      case 'failed':
        return 'bg-red-50 text-red-700 border-red-200'
      case 'pending':
        return 'bg-yellow-50 text-yellow-700 border-yellow-200'
      default:
        return 'bg-gray-50 text-gray-700 border-gray-200'
    }
  }

  const filteredDonations = donations?.donations?.filter(donation => {
    const matchesSearch = donation.purpose.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         donation.gateway.toLowerCase().includes(searchTerm.toLowerCase())
    return matchesSearch
  }) || []

  return (
    <div className="flex min-h-screen flex-col">
      <MainNav />
      <main className="flex-1 p-6">
        <div className="max-w-7xl mx-auto space-y-6">
          {/* Header */}
          <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
            <div className="flex items-center space-x-4">
              <Link href="/donations">
                <Button variant="outline" size="sm">
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back to Donations
                </Button>
              </Link>
              <div>
                <h1 className="text-3xl font-bold tracking-tight text-protec-navy">
                  Donation History
                </h1>
                <p className="text-muted-foreground">
                  Track your contributions to the PROTEC Alumni community
                </p>
              </div>
            </div>
            
            <Button variant="outline" className="shrink-0">
              <Download className="mr-2 h-4 w-4" />
              Export History
            </Button>
          </div>

          {/* Statistics Cards */}
          {stats && (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Donated</CardTitle>
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-protec-navy">
                    R{stats.totalAmount.toLocaleString()}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Across {stats.totalDonations} donations
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">This Year</CardTitle>
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-protec-navy">
                    R{stats.thisYearAmount.toLocaleString()}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {stats.thisYearDonations} donations
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Recurring</CardTitle>
                  <Repeat className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-protec-navy">
                    {stats.recurringDonations}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Active subscriptions
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Impact Score</CardTitle>
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-protec-navy">
                    {stats.impactScore}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Community impact points
                  </p>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Filters and Search */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Filter className="h-5 w-5" />
                Filter Donations
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-4">
                <div className="space-y-2">
                  <Label htmlFor="search">Search</Label>
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="search"
                      placeholder="Search by purpose or gateway..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Status</Label>
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      <SelectItem value="completed">Completed</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="failed">Failed</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Payment Gateway</Label>
                  <Select value={gatewayFilter} onValueChange={setGatewayFilter}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Gateways</SelectItem>
                      <SelectItem value="payfast">PayFast</SelectItem>
                      <SelectItem value="paypal">PayPal</SelectItem>
                      <SelectItem value="snapscan">SnapScan</SelectItem>
                      <SelectItem value="ozow">Ozow</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Date Range</Label>
                  <Select value={dateRange} onValueChange={setDateRange}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Time</SelectItem>
                      <SelectItem value="thisMonth">This Month</SelectItem>
                      <SelectItem value="lastMonth">Last Month</SelectItem>
                      <SelectItem value="thisYear">This Year</SelectItem>
                      <SelectItem value="lastYear">Last Year</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Donations List */}
          <Card>
            <CardHeader>
              <CardTitle>Your Donations</CardTitle>
              <CardDescription>
                {filteredDonations.length} donation{filteredDonations.length !== 1 ? 's' : ''} found
              </CardDescription>
            </CardHeader>
            <CardContent>
              {filteredDonations.length === 0 ? (
                <div className="text-center py-12">
                  <Heart className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    No donations found
                  </h3>
                  <p className="text-gray-600 mb-4">
                    {searchTerm || statusFilter !== "all" || gatewayFilter !== "all" 
                      ? "Try adjusting your filters to see more results."
                      : "You haven't made any donations yet. Start supporting the PROTEC community today!"
                    }
                  </p>
                  <Link href="/donations">
                    <Button className="bg-protec-red hover:bg-protec-red/90">
                      <Heart className="mr-2 h-4 w-4" />
                      Make a Donation
                    </Button>
                  </Link>
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredDonations.map((donation) => (
                    <div
                      key={donation.id}
                      className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                    >
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-2">
                          {getStatusIcon(donation.status)}
                          <Badge variant="outline" className={getStatusColor(donation.status)}>
                            {donation.status}
                          </Badge>
                        </div>
                        
                        <div>
                          <div className="font-medium">
                            R{donation.amountZAR.toLocaleString()} - {donation.purpose}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {format(new Date(donation.createdAt), "PPP")} via {donation.gateway}
                            {donation.isRecurring && (
                              <Badge variant="outline" className="ml-2">
                                <Repeat className="mr-1 h-3 w-3" />
                                Recurring
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>
                      
                      <div className="text-right">
                        <div className="font-medium text-protec-navy">
                          R{donation.amountZAR.toLocaleString()}
                        </div>
                        {donation.transactionId && (
                          <div className="text-xs text-muted-foreground">
                            ID: {donation.transactionId.slice(-8)}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}
