"use client"

import { useSession } from "next-auth/react"
import { redirect } from "next/navigation"
import { MainNav } from "@/components/navigation/main-nav"
import { EmailTestPanel } from "@/components/admin/email-test-panel"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { 
  Users, 
  BarChart3, 
  Mail, 
  Settings,
  Shield,
  Database,
  Activity
} from "lucide-react"
import Link from "next/link"

export default function AdminPage() {
  const { data: session, status } = useSession()

  if (status === "loading") {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-protec-navy"></div>
      </div>
    )
  }

  if (!session?.user || session.user.role !== "ADMIN") {
    redirect("/unauthorized")
  }

  const adminStats = [
    {
      title: "Total Users",
      value: "1,234",
      description: "Active alumni accounts",
      icon: Users,
      href: "/admin/users"
    },
    {
      title: "Email Status",
      value: "Active",
      description: "Magic links operational",
      icon: Mail,
      href: "#email-testing"
    },
    {
      title: "System Health",
      value: "Good",
      description: "All services running",
      icon: Activity,
      href: "/admin/analytics"
    },
    {
      title: "Database",
      value: "Connected",
      description: "PostgreSQL operational",
      icon: Database,
      href: "/admin/analytics"
    }
  ]

  return (
    <div className="flex min-h-screen flex-col">
      <MainNav />
      <main className="flex-1 space-y-6 p-6">
        {/* Header */}
        <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
          <div>
            <h1 className="text-3xl font-bold tracking-tight text-protec-navy">
              Admin Dashboard
            </h1>
            <p className="text-muted-foreground">
              Manage the PROTEC Alumni Network platform
            </p>
          </div>
          
          <Badge variant="outline" className="border-green-200 bg-green-50 text-green-700 w-fit">
            <Shield className="mr-1 h-3 w-3" />
            Admin Access
          </Badge>
        </div>

        {/* Quick Stats */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {adminStats.map((stat) => {
            const Icon = stat.icon
            return (
              <Card key={stat.title} className="border-0 shadow-lg hover:shadow-xl transition-all duration-300">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    {stat.title}
                  </CardTitle>
                  <Icon className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-protec-navy">{stat.value}</div>
                  <p className="text-xs text-muted-foreground">
                    {stat.description}
                  </p>
                  {stat.href.startsWith('#') ? (
                    <a 
                      href={stat.href}
                      className="text-xs text-protec-red hover:underline mt-2 inline-block"
                    >
                      View details →
                    </a>
                  ) : (
                    <Link 
                      href={stat.href}
                      className="text-xs text-protec-red hover:underline mt-2 inline-block"
                    >
                      View details →
                    </Link>
                  )}
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* Admin Tabs */}
        <Tabs defaultValue="email" className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="email">Email Testing</TabsTrigger>
            <TabsTrigger value="users">User Management</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="email" className="space-y-4" id="email-testing">
            <EmailTestPanel />
          </TabsContent>

          <TabsContent value="users" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  User Management
                </CardTitle>
                <CardDescription>
                  Manage alumni accounts, roles, and permissions
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    User Management
                  </h3>
                  <p className="text-gray-600 mb-4">
                    Access detailed user management tools
                  </p>
                  <Link href="/admin/users">
                    <button className="bg-protec-red text-white px-4 py-2 rounded-lg hover:bg-protec-red/90 transition-colors">
                      Go to User Management
                    </button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Platform Analytics
                </CardTitle>
                <CardDescription>
                  View platform usage, engagement, and performance metrics
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Analytics Dashboard
                  </h3>
                  <p className="text-gray-600 mb-4">
                    Access detailed analytics and reporting
                  </p>
                  <Link href="/admin/analytics">
                    <button className="bg-protec-red text-white px-4 py-2 rounded-lg hover:bg-protec-red/90 transition-colors">
                      Go to Analytics
                    </button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="settings" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Platform Settings
                </CardTitle>
                <CardDescription>
                  Configure platform settings, integrations, and preferences
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="grid gap-4 md:grid-cols-2">
                    <Card className="border border-gray-200">
                      <CardHeader className="pb-3">
                        <CardTitle className="text-base">Email Configuration</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-gray-600 mb-3">
                          Configure SMTP settings and email templates
                        </p>
                        <Badge variant="outline" className="text-green-600 border-green-200">
                          Configured
                        </Badge>
                      </CardContent>
                    </Card>

                    <Card className="border border-gray-200">
                      <CardHeader className="pb-3">
                        <CardTitle className="text-base">OAuth Providers</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-gray-600 mb-3">
                          Manage Google, GitHub, and other OAuth providers
                        </p>
                        <Badge variant="outline" className="text-green-600 border-green-200">
                          Active
                        </Badge>
                      </CardContent>
                    </Card>

                    <Card className="border border-gray-200">
                      <CardHeader className="pb-3">
                        <CardTitle className="text-base">Payment Gateways</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-gray-600 mb-3">
                          Configure PayFast, PayPal, and other payment methods
                        </p>
                        <Badge variant="outline" className="text-yellow-600 border-yellow-200">
                          Pending
                        </Badge>
                      </CardContent>
                    </Card>

                    <Card className="border border-gray-200">
                      <CardHeader className="pb-3">
                        <CardTitle className="text-base">Database</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-gray-600 mb-3">
                          Database connection and migration status
                        </p>
                        <Badge variant="outline" className="text-green-600 border-green-200">
                          Connected
                        </Badge>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </main>
    </div>
  )
}