import { MainNav } from "@/components/navigation/main-nav"
import { LandingFooter } from "@/components/landing/footer"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Shield, Eye, Lock, UserCheck, Database, Mail } from "lucide-react"

export default function PrivacyPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <MainNav />
      <main className="flex-1">
        {/* Hero Section */}
        <section className="relative overflow-hidden bg-gradient-to-br from-protec-navy to-protec-red py-20 sm:py-32">
          <div className="absolute inset-0 bg-grid-white/10 [mask-image:linear-gradient(0deg,rgba(255,255,255,0.1),rgba(255,255,255,0.5))]" />
          
          <div className="relative mx-auto max-w-7xl px-6 lg:px-8">
            <div className="mx-auto max-w-2xl text-center">
              <Badge variant="outline" className="mb-6 border-white/30 bg-white/10 text-white">
                <Shield className="mr-2 h-4 w-4" />
                Privacy Policy
              </Badge>
              
              <h1 className="text-4xl font-bold tracking-tight text-white sm:text-6xl">
                Your Privacy Matters
              </h1>
              
              <p className="mt-6 text-xl leading-8 text-blue-100">
                We are committed to protecting your personal information and being transparent 
                about how we collect, use, and share your data.
              </p>
              
              <p className="mt-4 text-sm text-blue-200">
                Last updated: January 2024
              </p>
            </div>
          </div>
        </section>

        {/* Privacy Content */}
        <section className="py-20 bg-white">
          <div className="mx-auto max-w-4xl px-6 lg:px-8">
            <div className="prose prose-lg max-w-none">
              
              {/* Introduction */}
              <Card className="border-0 shadow-lg mb-12">
                <CardContent className="p-8">
                  <div className="flex items-center gap-3 mb-6">
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-protec-navy/10">
                      <Eye className="h-5 w-5 text-protec-navy" />
                    </div>
                    <h2 className="text-2xl font-bold text-protec-navy m-0">Introduction</h2>
                  </div>
                  <p className="text-gray-600 leading-relaxed">
                    PROTEC Alumni Network ("we," "our," or "us") respects your privacy and is committed to protecting your personal data. 
                    This privacy policy explains how we collect, use, disclose, and safeguard your information when you use our alumni platform.
                  </p>
                </CardContent>
              </Card>

              {/* Information We Collect */}
              <Card className="border-0 shadow-lg mb-12">
                <CardContent className="p-8">
                  <div className="flex items-center gap-3 mb-6">
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-protec-red/10">
                      <Database className="h-5 w-5 text-protec-red" />
                    </div>
                    <h2 className="text-2xl font-bold text-protec-navy m-0">Information We Collect</h2>
                  </div>
                  
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-semibold text-protec-navy mb-3">Personal Information</h3>
                      <ul className="list-disc list-inside text-gray-600 space-y-2">
                        <li>Name, email address, and contact information</li>
                        <li>PROTEC graduation year and programme details</li>
                        <li>Professional information (current job, company, industry)</li>
                        <li>Educational background and qualifications</li>
                        <li>Profile photo and biographical information</li>
                      </ul>
                    </div>
                    
                    <div>
                      <h3 className="text-lg font-semibold text-protec-navy mb-3">Usage Information</h3>
                      <ul className="list-disc list-inside text-gray-600 space-y-2">
                        <li>Platform usage patterns and preferences</li>
                        <li>Event attendance and participation</li>
                        <li>Communication and messaging data</li>
                        <li>Device information and IP addresses</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* How We Use Your Information */}
              <Card className="border-0 shadow-lg mb-12">
                <CardContent className="p-8">
                  <div className="flex items-center gap-3 mb-6">
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-protec-navy/10">
                      <UserCheck className="h-5 w-5 text-protec-navy" />
                    </div>
                    <h2 className="text-2xl font-bold text-protec-navy m-0">How We Use Your Information</h2>
                  </div>
                  
                  <div className="space-y-4 text-gray-600">
                    <p><strong>Platform Services:</strong> To provide and maintain the alumni platform, facilitate connections between alumni, and enable participation in events and activities.</p>
                    
                    <p><strong>Communication:</strong> To send you platform updates, event notifications, newsletters, and other relevant communications.</p>
                    
                    <p><strong>Improvement:</strong> To analyze platform usage and improve our services, features, and user experience.</p>
                    
                    <p><strong>Verification:</strong> To verify your alumni status and maintain the integrity of our community.</p>
                    
                    <p><strong>Legal Compliance:</strong> To comply with applicable laws, regulations, and legal processes.</p>
                  </div>
                </CardContent>
              </Card>

              {/* Information Sharing */}
              <Card className="border-0 shadow-lg mb-12">
                <CardContent className="p-8">
                  <div className="flex items-center gap-3 mb-6">
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-protec-red/10">
                      <Lock className="h-5 w-5 text-protec-red" />
                    </div>
                    <h2 className="text-2xl font-bold text-protec-navy m-0">Information Sharing</h2>
                  </div>
                  
                  <div className="space-y-4 text-gray-600">
                    <p><strong>Alumni Directory:</strong> Basic profile information may be visible to other verified alumni based on your privacy settings.</p>
                    
                    <p><strong>PROTEC Organization:</strong> We may share aggregated, non-personal data with PROTEC for institutional research and development purposes.</p>
                    
                    <p><strong>Service Providers:</strong> We work with trusted third-party service providers who assist in platform operations, always under strict confidentiality agreements.</p>
                    
                    <p><strong>Legal Requirements:</strong> We may disclose information when required by law or to protect our rights and the safety of our users.</p>
                    
                    <p className="font-semibold text-protec-navy">We never sell your personal information to third parties.</p>
                  </div>
                </CardContent>
              </Card>

              {/* Your Privacy Rights */}
              <Card className="border-0 shadow-lg mb-12">
                <CardContent className="p-8">
                  <div className="flex items-center gap-3 mb-6">
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-protec-navy/10">
                      <Shield className="h-5 w-5 text-protec-navy" />
                    </div>
                    <h2 className="text-2xl font-bold text-protec-navy m-0">Your Privacy Rights</h2>
                  </div>
                  
                  <div className="space-y-4 text-gray-600">
                    <p><strong>Access:</strong> You can access and review your personal information through your profile settings.</p>
                    
                    <p><strong>Correction:</strong> You can update or correct your information at any time through the platform.</p>
                    
                    <p><strong>Deletion:</strong> You can request deletion of your account and personal data, subject to certain legal and operational requirements.</p>
                    
                    <p><strong>Privacy Controls:</strong> You can control who can see your profile information and contact you through privacy settings.</p>
                    
                    <p><strong>Communication Preferences:</strong> You can opt out of non-essential communications while maintaining your account.</p>
                  </div>
                </CardContent>
              </Card>

              {/* Data Security */}
              <Card className="border-0 shadow-lg mb-12">
                <CardContent className="p-8">
                  <div className="flex items-center gap-3 mb-6">
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-protec-red/10">
                      <Lock className="h-5 w-5 text-protec-red" />
                    </div>
                    <h2 className="text-2xl font-bold text-protec-navy m-0">Data Security</h2>
                  </div>
                  
                  <div className="space-y-4 text-gray-600">
                    <p>We implement appropriate technical and organizational security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.</p>
                    
                    <p>Our security measures include:</p>
                    <ul className="list-disc list-inside space-y-2">
                      <li>Encryption of data in transit and at rest</li>
                      <li>Regular security assessments and updates</li>
                      <li>Access controls and authentication requirements</li>
                      <li>Employee training on data protection</li>
                      <li>Incident response procedures</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>

              {/* Contact Information */}
              <Card className="border-0 shadow-lg">
                <CardContent className="p-8">
                  <div className="flex items-center gap-3 mb-6">
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-protec-navy/10">
                      <Mail className="h-5 w-5 text-protec-navy" />
                    </div>
                    <h2 className="text-2xl font-bold text-protec-navy m-0">Contact Us</h2>
                  </div>
                  
                  <div className="space-y-4 text-gray-600">
                    <p>If you have any questions about this Privacy Policy or our data practices, please contact us:</p>
                    
                    <div className="space-y-2">
                      <p><strong>Email:</strong> <EMAIL></p>
                      <p><strong>Phone:</strong> +27 11 403 6861</p>
                      <p><strong>Address:</strong> PROTEC Alumni Network, 123 PROTEC House, Braamfontein, Johannesburg 2001</p>
                    </div>
                    
                    <p className="text-sm text-gray-500 mt-6">
                      This Privacy Policy may be updated from time to time. We will notify you of any material changes 
                      through the platform or via email. Your continued use of the platform after such changes constitutes 
                      acceptance of the updated policy.
                    </p>
                  </div>
                </CardContent>
              </Card>

            </div>
          </div>
        </section>
      </main>
      <LandingFooter />
    </div>
  )
}