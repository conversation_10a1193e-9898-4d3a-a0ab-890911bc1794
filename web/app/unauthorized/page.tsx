"use client"

import Link from "next/link"
import { useSession } from "next-auth/react"
import { MainNav } from "@/components/navigation/main-nav"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Shield, ArrowLeft, Home } from "lucide-react"

export default function UnauthorizedPage() {
  const { data: session } = useSession()

  return (
    <div className="flex min-h-screen flex-col">
      <MainNav />
      <main className="flex-1 flex items-center justify-center p-6">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-red-100">
              <Shield className="h-8 w-8 text-red-600" />
            </div>
            <CardTitle className="text-xl font-semibold text-protec-navy">
              Access Denied
            </CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <p className="text-muted-foreground">
              You don't have permission to access this page. This area is restricted to users with specific roles.
            </p>
            
            {session && (
              <div className="p-3 bg-gray-50 rounded-md">
                <p className="text-sm text-muted-foreground">
                  Signed in as: <span className="font-medium">{session.user?.name}</span>
                </p>
                <p className="text-sm text-muted-foreground">
                  Role: <span className="font-medium">{session.user?.role || 'Alumni'}</span>
                </p>
              </div>
            )}
            
            <div className="flex flex-col space-y-2">
              <Button asChild className="bg-protec-red hover:bg-protec-red/90">
                <Link href="/dashboard">
                  <Home className="mr-2 h-4 w-4" />
                  Go to Dashboard
                </Link>
              </Button>
              
              <Button variant="outline" onClick={() => window.history.back()}>
                <ArrowLeft className="mr-2 h-4 w-4" />
                Go Back
              </Button>
            </div>
            
            <div className="text-sm text-muted-foreground">
              <p>
                If you believe you should have access to this page, please contact an administrator.
              </p>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  )
}
