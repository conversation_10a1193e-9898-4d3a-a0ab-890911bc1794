import { MainNav } from "@/components/navigation/main-nav"
import { LandingFooter } from "@/components/landing/footer"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { FileText, Users, Shield, AlertTriangle, Scale, Mail } from "lucide-react"

export default function TermsPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <MainNav />
      <main className="flex-1">
        {/* Hero Section */}
        <section className="relative overflow-hidden bg-gradient-to-br from-protec-navy to-protec-red py-20 sm:py-32">
          <div className="absolute inset-0 bg-grid-white/10 [mask-image:linear-gradient(0deg,rgba(255,255,255,0.1),rgba(255,255,255,0.5))]" />
          
          <div className="relative mx-auto max-w-7xl px-6 lg:px-8">
            <div className="mx-auto max-w-2xl text-center">
              <Badge variant="outline" className="mb-6 border-white/30 bg-white/10 text-white">
                <FileText className="mr-2 h-4 w-4" />
                Terms of Service
              </Badge>
              
              <h1 className="text-4xl font-bold tracking-tight text-white sm:text-6xl">
                Terms of Service
              </h1>
              
              <p className="mt-6 text-xl leading-8 text-blue-100">
                Please read these terms carefully before using the PROTEC Alumni Network platform. 
                By accessing our services, you agree to be bound by these terms.
              </p>
              
              <p className="mt-4 text-sm text-blue-200">
                Last updated: January 2024
              </p>
            </div>
          </div>
        </section>

        {/* Terms Content */}
        <section className="py-20 bg-white">
          <div className="mx-auto max-w-4xl px-6 lg:px-8">
            <div className="prose prose-lg max-w-none">
              
              {/* Acceptance of Terms */}
              <Card className="border-0 shadow-lg mb-12">
                <CardContent className="p-8">
                  <div className="flex items-center gap-3 mb-6">
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-protec-navy/10">
                      <Scale className="h-5 w-5 text-protec-navy" />
                    </div>
                    <h2 className="text-2xl font-bold text-protec-navy m-0">Acceptance of Terms</h2>
                  </div>
                  <p className="text-gray-600 leading-relaxed">
                    By accessing and using the PROTEC Alumni Network platform, you accept and agree to be bound by the terms 
                    and provision of this agreement. If you do not agree to abide by the above, please do not use this service.
                  </p>
                </CardContent>
              </Card>

              {/* Eligibility */}
              <Card className="border-0 shadow-lg mb-12">
                <CardContent className="p-8">
                  <div className="flex items-center gap-3 mb-6">
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-protec-red/10">
                      <Users className="h-5 w-5 text-protec-red" />
                    </div>
                    <h2 className="text-2xl font-bold text-protec-navy m-0">Eligibility</h2>
                  </div>
                  
                  <div className="space-y-4 text-gray-600">
                    <p>The PROTEC Alumni Network is exclusively for:</p>
                    <ul className="list-disc list-inside space-y-2">
                      <li>Graduates of PROTEC programmes</li>
                      <li>Current PROTEC students (with limited access)</li>
                      <li>PROTEC staff and faculty members</li>
                      <li>Authorized partners and sponsors</li>
                    </ul>
                    <p>You must provide accurate information during registration and maintain the accuracy of your profile information.</p>
                  </div>
                </CardContent>
              </Card>

              {/* User Responsibilities */}
              <Card className="border-0 shadow-lg mb-12">
                <CardContent className="p-8">
                  <div className="flex items-center gap-3 mb-6">
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-protec-navy/10">
                      <Shield className="h-5 w-5 text-protec-navy" />
                    </div>
                    <h2 className="text-2xl font-bold text-protec-navy m-0">User Responsibilities</h2>
                  </div>
                  
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-semibold text-protec-navy mb-3">Account Security</h3>
                      <ul className="list-disc list-inside text-gray-600 space-y-2">
                        <li>Maintain the confidentiality of your login credentials</li>
                        <li>Notify us immediately of any unauthorized access</li>
                        <li>Use strong passwords and enable two-factor authentication when available</li>
                      </ul>
                    </div>
                    
                    <div>
                      <h3 className="text-lg font-semibold text-protec-navy mb-3">Content Guidelines</h3>
                      <ul className="list-disc list-inside text-gray-600 space-y-2">
                        <li>Post only appropriate, professional content</li>
                        <li>Respect intellectual property rights</li>
                        <li>Do not share false, misleading, or defamatory information</li>
                        <li>Maintain professional standards in all communications</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Prohibited Activities */}
              <Card className="border-0 shadow-lg mb-12">
                <CardContent className="p-8">
                  <div className="flex items-center gap-3 mb-6">
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-red-100">
                      <AlertTriangle className="h-5 w-5 text-red-600" />
                    </div>
                    <h2 className="text-2xl font-bold text-protec-navy m-0">Prohibited Activities</h2>
                  </div>
                  
                  <div className="space-y-4 text-gray-600">
                    <p>The following activities are strictly prohibited:</p>
                    <ul className="list-disc list-inside space-y-2">
                      <li>Harassment, discrimination, or inappropriate behavior</li>
                      <li>Spam, unsolicited commercial communications, or excessive self-promotion</li>
                      <li>Sharing personal information of other users without consent</li>
                      <li>Attempting to gain unauthorized access to the platform or other users' accounts</li>
                      <li>Using the platform for illegal activities or purposes</li>
                      <li>Creating fake profiles or impersonating others</li>
                      <li>Distributing malware, viruses, or other harmful code</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>

              {/* Platform Usage */}
              <Card className="border-0 shadow-lg mb-12">
                <CardContent className="p-8">
                  <div className="flex items-center gap-3 mb-6">
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-protec-red/10">
                      <FileText className="h-5 w-5 text-protec-red" />
                    </div>
                    <h2 className="text-2xl font-bold text-protec-navy m-0">Platform Usage</h2>
                  </div>
                  
                  <div className="space-y-4 text-gray-600">
                    <p><strong>License:</strong> We grant you a limited, non-exclusive, non-transferable license to use the platform for its intended purpose.</p>
                    
                    <p><strong>Availability:</strong> While we strive for continuous availability, we do not guarantee uninterrupted access to the platform.</p>
                    
                    <p><strong>Updates:</strong> We may update, modify, or discontinue features of the platform at any time with reasonable notice.</p>
                    
                    <p><strong>Data Backup:</strong> You are responsible for backing up any important data or content you share on the platform.</p>
                  </div>
                </CardContent>
              </Card>

              {/* Intellectual Property */}
              <Card className="border-0 shadow-lg mb-12">
                <CardContent className="p-8">
                  <div className="flex items-center gap-3 mb-6">
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-protec-navy/10">
                      <Shield className="h-5 w-5 text-protec-navy" />
                    </div>
                    <h2 className="text-2xl font-bold text-protec-navy m-0">Intellectual Property</h2>
                  </div>
                  
                  <div className="space-y-4 text-gray-600">
                    <p><strong>Platform Content:</strong> All platform features, design, and functionality are owned by PROTEC and protected by intellectual property laws.</p>
                    
                    <p><strong>User Content:</strong> You retain ownership of content you post, but grant us a license to use, display, and distribute it within the platform.</p>
                    
                    <p><strong>PROTEC Branding:</strong> The PROTEC name, logo, and branding elements are trademarks and may not be used without permission.</p>
                    
                    <p><strong>Respect for Rights:</strong> Users must respect the intellectual property rights of others and not post copyrighted material without permission.</p>
                  </div>
                </CardContent>
              </Card>

              {/* Privacy and Data */}
              <Card className="border-0 shadow-lg mb-12">
                <CardContent className="p-8">
                  <div className="flex items-center gap-3 mb-6">
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-protec-red/10">
                      <Shield className="h-5 w-5 text-protec-red" />
                    </div>
                    <h2 className="text-2xl font-bold text-protec-navy m-0">Privacy and Data</h2>
                  </div>
                  
                  <div className="space-y-4 text-gray-600">
                    <p>Your privacy is important to us. Our collection and use of personal information is governed by our Privacy Policy, which is incorporated into these terms by reference.</p>
                    
                    <p>By using the platform, you consent to:</p>
                    <ul className="list-disc list-inside space-y-2">
                      <li>The collection and use of your information as described in our Privacy Policy</li>
                      <li>The display of your profile information to other verified alumni</li>
                      <li>Receiving platform-related communications and notifications</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>

              {/* Termination */}
              <Card className="border-0 shadow-lg mb-12">
                <CardContent className="p-8">
                  <div className="flex items-center gap-3 mb-6">
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-red-100">
                      <AlertTriangle className="h-5 w-5 text-red-600" />
                    </div>
                    <h2 className="text-2xl font-bold text-protec-navy m-0">Termination</h2>
                  </div>
                  
                  <div className="space-y-4 text-gray-600">
                    <p><strong>Your Right to Terminate:</strong> You may terminate your account at any time by contacting us or using the account deletion feature.</p>
                    
                    <p><strong>Our Right to Terminate:</strong> We reserve the right to suspend or terminate accounts that violate these terms or engage in inappropriate behavior.</p>
                    
                    <p><strong>Effect of Termination:</strong> Upon termination, your access to the platform will cease, and we may delete your account data in accordance with our data retention policies.</p>
                  </div>
                </CardContent>
              </Card>

              {/* Disclaimers and Limitations */}
              <Card className="border-0 shadow-lg mb-12">
                <CardContent className="p-8">
                  <div className="flex items-center gap-3 mb-6">
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-yellow-100">
                      <AlertTriangle className="h-5 w-5 text-yellow-600" />
                    </div>
                    <h2 className="text-2xl font-bold text-protec-navy m-0">Disclaimers and Limitations</h2>
                  </div>
                  
                  <div className="space-y-4 text-gray-600">
                    <p><strong>As-Is Service:</strong> The platform is provided "as is" without warranties of any kind, either express or implied.</p>
                    
                    <p><strong>User Content:</strong> We are not responsible for the accuracy, completeness, or appropriateness of user-generated content.</p>
                    
                    <p><strong>Third-Party Links:</strong> We are not responsible for the content or practices of third-party websites linked from our platform.</p>
                    
                    <p><strong>Limitation of Liability:</strong> Our liability is limited to the maximum extent permitted by law.</p>
                  </div>
                </CardContent>
              </Card>

              {/* Changes to Terms */}
              <Card className="border-0 shadow-lg mb-12">
                <CardContent className="p-8">
                  <div className="flex items-center gap-3 mb-6">
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-protec-navy/10">
                      <FileText className="h-5 w-5 text-protec-navy" />
                    </div>
                    <h2 className="text-2xl font-bold text-protec-navy m-0">Changes to Terms</h2>
                  </div>
                  
                  <div className="space-y-4 text-gray-600">
                    <p>We reserve the right to modify these terms at any time. Material changes will be communicated through:</p>
                    <ul className="list-disc list-inside space-y-2">
                      <li>Email notifications to registered users</li>
                      <li>Platform announcements and notifications</li>
                      <li>Updates to this page with revised effective dates</li>
                    </ul>
                    <p>Continued use of the platform after changes constitutes acceptance of the new terms.</p>
                  </div>
                </CardContent>
              </Card>

              {/* Contact Information */}
              <Card className="border-0 shadow-lg">
                <CardContent className="p-8">
                  <div className="flex items-center gap-3 mb-6">
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-protec-red/10">
                      <Mail className="h-5 w-5 text-protec-red" />
                    </div>
                    <h2 className="text-2xl font-bold text-protec-navy m-0">Contact Us</h2>
                  </div>
                  
                  <div className="space-y-4 text-gray-600">
                    <p>If you have any questions about these Terms of Service, please contact us:</p>
                    
                    <div className="space-y-2">
                      <p><strong>Email:</strong> <EMAIL></p>
                      <p><strong>Phone:</strong> +27 11 403 6861</p>
                      <p><strong>Address:</strong> PROTEC Alumni Network, 123 PROTEC House, Braamfontein, Johannesburg 2001</p>
                    </div>
                    
                    <p className="text-sm text-gray-500 mt-6">
                      These Terms of Service are governed by the laws of South Africa. Any disputes arising from these terms 
                      will be subject to the exclusive jurisdiction of the South African courts.
                    </p>
                  </div>
                </CardContent>
              </Card>

            </div>
          </div>
        </section>
      </main>
      <LandingFooter />
    </div>
  )
}