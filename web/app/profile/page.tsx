"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import { redirect } from "next/navigation"
import { MainNav } from "@/components/navigation/main-nav"
import { ProfileHeader } from "@/components/profile/profile-header"
import { ProfileCompletion } from "@/components/profile/profile-completion"
import { BasicInfoSection } from "@/components/profile/basic-info-section"
import { CareerHistorySection } from "@/components/profile/career-history-section"
import { EducationSection } from "@/components/profile/education-section"
import { ProtecInvolvementSection } from "@/components/profile/protec-involvement-section"
import { PrivacySettingsSection } from "@/components/profile/privacy-settings-section"
import { Card, CardContent } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { api } from "@/components/providers/trpc-provider"
import { 
  User, 
  Briefcase, 
  GraduationCap, 
  Award, 
  Shield, 
  Eye,
  Edit3
} from "lucide-react"

export default function ProfilePage() {
  const { status } = useSession()
  const [isEditing, setIsEditing] = useState(false)
  const [activeTab, setActiveTab] = useState("overview")

  const { data: profile, isLoading, refetch } = api.profile.getComplete.useQuery()
  const { data: completionStatus } = api.profile.getCompletionStatus.useQuery()

  if (status === "loading" || isLoading) {
    return (
      <div className="flex min-h-screen flex-col">
        <MainNav />
        <main className="flex-1 p-6">
          <div className="max-w-6xl mx-auto space-y-6">
            {/* Header Skeleton */}
            <Card>
              <CardContent className="p-6">
                <div className="flex items-start space-x-6">
                  <Skeleton className="h-32 w-32 rounded-full" />
                  <div className="flex-1 space-y-4">
                    <Skeleton className="h-8 w-1/3" />
                    <Skeleton className="h-4 w-1/2" />
                    <Skeleton className="h-4 w-2/3" />
                    <div className="flex space-x-2">
                      <Skeleton className="h-8 w-24" />
                      <Skeleton className="h-8 w-24" />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Content Skeletons */}
            <div className="grid gap-6 lg:grid-cols-4">
              <div className="lg:col-span-3 space-y-6">
                <Card>
                  <CardContent className="p-6 space-y-4">
                    <Skeleton className="h-6 w-1/4" />
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-3/4" />
                  </CardContent>
                </Card>
              </div>
              <div className="space-y-6">
                <Card>
                  <CardContent className="p-6 space-y-4">
                    <Skeleton className="h-6 w-1/3" />
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-2/3" />
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </main>
      </div>
    )
  }

  if (status === "unauthenticated") {
    redirect("/auth/signin")
  }

  if (!profile) {
    return (
      <div className="flex min-h-screen flex-col">
        <MainNav />
        <main className="flex-1 flex items-center justify-center p-6">
          <Card className="w-full max-w-md">
            <CardContent className="p-6 text-center">
              <h2 className="text-xl font-semibold text-primary mb-2">
                Profile Not Found
              </h2>
              <p className="text-muted-foreground">
                Unable to load your profile. Please try again later.
              </p>
            </CardContent>
          </Card>
        </main>
      </div>
    )
  }

  return (
    <div className="flex min-h-screen flex-col">
      <MainNav />
      <main className="flex-1 p-6">
        <div className="max-w-6xl mx-auto space-y-6">
          {/* Profile Header */}
          <ProfileHeader 
            profile={profile} 
            isEditing={isEditing}
            onEditToggle={() => setIsEditing(!isEditing)}
          />

          {/* Profile Completion */}
          {completionStatus && completionStatus.completionPercentage < 100 && (
            <ProfileCompletion 
              completionStatus={completionStatus}
              onSectionClick={(section) => setActiveTab(section)}
            />
          )}

          {/* Main Content */}
          <div className="grid gap-6 lg:grid-cols-4">
            {/* Main Profile Content */}
            <div className="lg:col-span-3">
              <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
                <TabsList className="grid w-full grid-cols-5">
                  <TabsTrigger value="overview" className="flex items-center space-x-2">
                    <Eye className="h-4 w-4" />
                    <span className="hidden sm:inline">Overview</span>
                  </TabsTrigger>
                  <TabsTrigger value="basic" className="flex items-center space-x-2">
                    <User className="h-4 w-4" />
                    <span className="hidden sm:inline">Basic Info</span>
                  </TabsTrigger>
                  <TabsTrigger value="career" className="flex items-center space-x-2">
                    <Briefcase className="h-4 w-4" />
                    <span className="hidden sm:inline">Career</span>
                  </TabsTrigger>
                  <TabsTrigger value="education" className="flex items-center space-x-2">
                    <GraduationCap className="h-4 w-4" />
                    <span className="hidden sm:inline">Education</span>
                  </TabsTrigger>
                  <TabsTrigger value="protec" className="flex items-center space-x-2">
                    <Award className="h-4 w-4" />
                    <span className="hidden sm:inline">PROTEC</span>
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="overview" className="space-y-6">
                  <BasicInfoSection profile={profile} isEditing={false} onUpdate={refetch} />
                  <CareerHistorySection profile={profile} isEditing={false} onUpdate={refetch} />
                  <EducationSection profile={profile} isEditing={false} onUpdate={refetch} />
                  <ProtecInvolvementSection profile={profile} isEditing={false} onUpdate={refetch} />
                </TabsContent>

                <TabsContent value="basic">
                  <BasicInfoSection profile={profile} isEditing={true} onUpdate={refetch} />
                </TabsContent>

                <TabsContent value="career">
                  <CareerHistorySection profile={profile} isEditing={true} onUpdate={refetch} />
                </TabsContent>

                <TabsContent value="education">
                  <EducationSection profile={profile} isEditing={true} onUpdate={refetch} />
                </TabsContent>

                <TabsContent value="protec">
                  <ProtecInvolvementSection profile={profile} isEditing={true} onUpdate={refetch} />
                </TabsContent>
              </Tabs>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Privacy Settings */}
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="font-semibold flex items-center">
                      <Shield className="mr-2 h-4 w-4" />
                      Privacy Settings
                    </h3>
                    <Button 
                      variant="ghost" 
                      size="sm"
                      onClick={() => setActiveTab("privacy")}
                    >
                      <Edit3 className="h-4 w-4" />
                    </Button>
                  </div>
                  <PrivacySettingsSection profile={profile} isCompact={true} onUpdate={refetch} />
                </CardContent>
              </Card>

              {/* Quick Stats */}
              <Card>
                <CardContent className="p-6">
                  <h3 className="font-semibold mb-4">Profile Stats</h3>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Connections</span>
                      <span className="font-medium">{profile._count.connections}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Posts</span>
                      <span className="font-medium">{profile._count.posts}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Events Organized</span>
                      <span className="font-medium">{profile._count.organizedEvents}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Donations</span>
                      <span className="font-medium">{profile._count.donations}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
