"use client"

import { MainNav } from "@/components/navigation/main-nav"
import { AlumniDirectory } from "@/components/alumni/directory"

export default function DirectoryPage() {
  return (
    <div className="flex min-h-screen flex-col bg-background">
      <MainNav />
      <main className="flex-1 space-y-8 p-6">
        {/* Hero Section */}
        <div className="relative overflow-hidden rounded-lg bg-gradient-to-br from-protec-navy to-protec-navy/80 text-white">
          <div className="absolute inset-0 bg-grid-white opacity-10" />
          <div className="relative px-8 py-12 text-center">
            <h1 className="text-4xl font-bold mb-4">
              Connect with PROTEC Alumni
            </h1>
            <p className="text-xl text-blue-100 max-w-2xl mx-auto">
              Join a network of 5,000+ graduates making an impact across South Africa and beyond
            </p>
            <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
              <div className="text-center">
                <div className="text-3xl font-bold text-white">5,000+</div>
                <div className="text-blue-200 text-sm">Alumni Worldwide</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-white">40+</div>
                <div className="text-blue-200 text-sm">Years of Excellence</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-white">85%</div>
                <div className="text-blue-200 text-sm">Employment Rate</div>
              </div>
            </div>
          </div>
        </div>

        <AlumniDirectory />
      </main>
    </div>
  )
}
