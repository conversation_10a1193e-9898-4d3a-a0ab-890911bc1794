"use client"

import { useSession } from "next-auth/react"
import { redirect } from "next/navigation"
import { MainNav } from "@/components/navigation/main-nav"
import { EventsDashboardOverview } from "@/components/events/dashboard-overview"
import { MyEvents } from "@/components/events/my-events"
import { EventPerformance } from "@/components/events/event-performance"
import { AttendeeAnalytics } from "@/components/events/attendee-analytics"
import { UpcomingDeadlines } from "@/components/events/upcoming-deadlines"
import { EventsCalendarView } from "@/components/events/calendar-view"
import { Button } from "@/components/ui/button"
import { Plus } from "lucide-react"
import Link from "next/link"

export default function EventsDashboardPage() {
  const { data: session, status } = useSession()

  if (status === "loading") {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-protec-red"></div>
      </div>
    )
  }

  if (status === "unauthenticated") {
    redirect("/auth/signin")
  }

  return (
    <div className="flex min-h-screen flex-col">
      <MainNav />
      <main className="flex-1 space-y-6 p-6">
        {/* Header */}
        <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
          <div>
            <h1 className="text-3xl font-bold tracking-tight text-protec-navy">
              Events Dashboard
            </h1>
            <p className="text-muted-foreground">
              Manage your events, track performance, and engage with attendees
            </p>
          </div>
          
          <Button className="bg-protec-red hover:bg-protec-red/90" asChild>
            <Link href="/events/new">
              <Plus className="mr-2 h-4 w-4" />
              Create Event
            </Link>
          </Button>
        </div>

        {/* Overview Cards */}
        <EventsDashboardOverview />

        {/* Main Dashboard Grid */}
        <div className="grid gap-6 lg:grid-cols-4">
          {/* Left Column - Main Content */}
          <div className="lg:col-span-3 space-y-6">
            <div className="grid gap-6 lg:grid-cols-2">
              <EventPerformance />
              <AttendeeAnalytics />
            </div>
            <MyEvents />
          </div>

          {/* Right Column - Sidebar */}
          <div className="space-y-6">
            <UpcomingDeadlines />
            <EventsCalendarView />
          </div>
        </div>
      </main>
    </div>
  )
}
