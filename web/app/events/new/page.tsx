"use client"

import { useSession } from "next-auth/react"
import { redirect } from "next/navigation"
import { MainNav } from "@/components/navigation/main-nav"
import { EventForm } from "@/components/events/event-form"

export default function NewEventPage() {
  const { data: session, status } = useSession()

  if (status === "loading") {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-protec-red"></div>
      </div>
    )
  }

  if (status === "unauthenticated") {
    redirect("/auth/signin")
  }

  return (
    <div className="flex min-h-screen flex-col">
      <MainNav />
      <main className="flex-1 p-6">
        <div className="max-w-2xl mx-auto space-y-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight text-protec-navy">
              Create New Event
            </h1>
            <p className="text-muted-foreground">
              Organize a networking event, workshop, or gathering for the PROTEC alumni community
            </p>
          </div>
          
          <EventForm />
        </div>
      </main>
    </div>
  )
}
