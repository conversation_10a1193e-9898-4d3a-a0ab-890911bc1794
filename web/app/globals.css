@import "tailwindcss";

:root {
    /* PROTEC Brand Colors - Base definitions */
    --protec-navy: 220 25% 33%;        /* #3B4A6B */
    --protec-red: 356 85% 52%;         /* #E31E24 */
    --protec-gray: 210 17% 96%;        /* #F5F5F5 */
    
    /* shadcn/ui semantic colors mapped to PROTEC brand */
    --background: oklch(1.0000 0 0);                    /* White */
    --foreground: oklch(0.3211 0 0);                    /* Dark text */
    --card: oklch(1.0000 0 0);                          /* White cards */
    --card-foreground: oklch(0.3211 0 0);               /* Dark text on cards */
    --popover: oklch(1.0000 0 0);                       /* White popovers */
    --popover-foreground: oklch(0.3211 0 0);            /* Dark text on popovers */
    --primary: oklch(0.4461 0.0809 270.2148);          /* PROTEC Navy as primary */
    --primary-foreground: oklch(1.0000 0 0);            /* White text on primary */
    --secondary: oklch(0.9670 0.0029 264.5419);         /* Light gray */
    --secondary-foreground: oklch(0.4461 0.0263 256.8018); /* Dark text on secondary */
    --muted: oklch(0.9846 0.0017 247.8389);             /* Very light gray */
    --muted-foreground: oklch(0.5510 0.0234 264.3637);  /* Muted text */
    --accent: oklch(0.6368 0.2078 25.3313);             /* PROTEC Red as accent */
    --accent-foreground: oklch(1.0000 0 0);             /* White text on accent */
    --destructive: oklch(0.6368 0.2078 25.3313);        /* PROTEC Red for destructive actions */
    --destructive-foreground: oklch(1.0000 0 0);        /* White text on destructive */
    --border: oklch(0.9276 0.0058 264.5313);            /* Light border */
    --input: oklch(0.9276 0.0058 264.5313);             /* Input border */
    --ring: oklch(0.4461 0.0809 270.2148);              /* PROTEC Navy for focus rings */
    --chart-1: oklch(0.6231 0.1880 259.8145);           /* Chart colors */
    --chart-2: oklch(0.5786 0.2242 26.6169);
    --chart-3: oklch(0.4882 0.2172 264.3763);
    --chart-4: oklch(0.4703 0.1698 21.4020);
    --chart-5: oklch(0.3791 0.1378 265.5222);
    --sidebar: oklch(0.9846 0.0017 247.8389);           /* Light sidebar */
    --sidebar-foreground: oklch(0.3211 0 0);            /* Dark text on sidebar */
    --sidebar-primary: oklch(0.4461 0.0809 270.2148);   /* PROTEC Navy for sidebar primary */
    --sidebar-primary-foreground: oklch(1.0000 0 0);    /* White text on sidebar primary */
    --sidebar-accent: oklch(0.6368 0.2078 25.3313);     /* PROTEC Red for sidebar accent */
    --sidebar-accent-foreground: oklch(1.0000 0 0);     /* White text on sidebar accent */
    --sidebar-border: oklch(0.9276 0.0058 264.5313);    /* Light sidebar border */
    --sidebar-ring: oklch(0.4461 0.0809 270.2148);      /* PROTEC Navy for sidebar focus */
    --font-sans: Inter, sans-serif;
    --font-serif: Source Serif 4, serif;
    --font-mono: JetBrains Mono, monospace;
    --radius: 0.375rem;
    --shadow-2xs: 0rem 0.125rem 0.5rem 0rem hsl(0 0% 0% / 0.06);
    --shadow-xs: 0rem 0.125rem 0.5rem 0rem hsl(0 0% 0% / 0.06);
    --shadow-sm: 0rem 0.125rem 0.5rem 0rem hsl(0 0% 0% / 0.12), 0rem 1px 2px -1px hsl(0 0% 0% / 0.12);
    --shadow: 0rem 0.125rem 0.5rem 0rem hsl(0 0% 0% / 0.12), 0rem 1px 2px -1px hsl(0 0% 0% / 0.12);
    --shadow-md: 0rem 0.125rem 0.5rem 0rem hsl(0 0% 0% / 0.12), 0rem 2px 4px -1px hsl(0 0% 0% / 0.12);
    --shadow-lg: 0rem 0.125rem 0.5rem 0rem hsl(0 0% 0% / 0.12), 0rem 4px 6px -1px hsl(0 0% 0% / 0.12);
    --shadow-xl: 0rem 0.125rem 0.5rem 0rem hsl(0 0% 0% / 0.12), 0rem 8px 10px -1px hsl(0 0% 0% / 0.12);
    --shadow-2xl: 0rem 0.125rem 0.5rem 0rem hsl(0 0% 0% / 0.30);
    --tracking-normal: 0.025em;
    --spacing: 0.25rem;
}

.dark {
    /* PROTEC Brand Colors remain the same in dark mode */
    --protec-navy: 220 25% 33%;        /* #3B4A6B */
    --protec-red: 356 85% 52%;         /* #E31E24 */
    --protec-gray: 210 17% 96%;        /* #F5F5F5 */
    
    /* shadcn/ui semantic colors for dark mode */
    --background: oklch(0.2046 0 0);                    /* Dark background */
    --foreground: oklch(0.9219 0 0);                    /* Light text */
    --card: oklch(0.2686 0 0);                          /* Dark cards */
    --card-foreground: oklch(0.9219 0 0);               /* Light text on cards */
    --popover: oklch(0.2686 0 0);                       /* Dark popovers */
    --popover-foreground: oklch(0.9219 0 0);            /* Light text on popovers */
    --primary: oklch(0.5824 0.2365 25.7065);           /* Lighter PROTEC Navy for dark mode */
    --primary-foreground: oklch(1.0000 0 0);            /* White text on primary */
    --secondary: oklch(0.2686 0 0);                     /* Dark gray */
    --secondary-foreground: oklch(0.9219 0 0);          /* Light text on secondary */
    --muted: oklch(0.2686 0 0);                         /* Dark muted */
    --muted-foreground: oklch(0.7155 0 0);              /* Muted light text */
    --accent: oklch(0.7368 0.2078 25.3313);             /* Lighter PROTEC Red for dark mode */
    --accent-foreground: oklch(1.0000 0 0);             /* White text on accent */
    --destructive: oklch(0.7368 0.2078 25.3313);        /* Lighter PROTEC Red for destructive */
    --destructive-foreground: oklch(1.0000 0 0);        /* White text on destructive */
    --border: oklch(0.3715 0 0);                        /* Dark border */
    --input: oklch(0.3715 0 0);                         /* Dark input border */
    --ring: oklch(0.5824 0.2365 25.7065);               /* PROTEC Navy for focus rings */
    --chart-1: oklch(0.7137 0.1434 254.6240);           /* Chart colors for dark mode */
    --chart-2: oklch(0.6231 0.1880 259.8145);
    --chart-3: oklch(0.5461 0.2152 262.8809);
    --chart-4: oklch(0.4882 0.2172 264.3763);
    --chart-5: oklch(0.4244 0.1809 265.6377);
    --sidebar: oklch(0.2046 0 0);                       /* Dark sidebar */
    --sidebar-foreground: oklch(0.9219 0 0);            /* Light text on sidebar */
    --sidebar-primary: oklch(0.5824 0.2365 25.7065);    /* PROTEC Navy for sidebar primary */
    --sidebar-primary-foreground: oklch(1.0000 0 0);    /* White text on sidebar primary */
    --sidebar-accent: oklch(0.7368 0.2078 25.3313);     /* Lighter PROTEC Red for sidebar accent */
    --sidebar-accent-foreground: oklch(1.0000 0 0);     /* White text on sidebar accent */
    --sidebar-border: oklch(0.3715 0 0);                /* Dark sidebar border */
    --sidebar-ring: oklch(0.5824 0.2365 25.7065);       /* PROTEC Navy for sidebar focus */
    --font-sans: Inter, sans-serif;
    --font-serif: Source Serif 4, serif;
    --font-mono: JetBrains Mono, monospace;
    --radius: 0.375rem;
    --shadow-2xs: 0rem 0.125rem 0.5rem 0rem hsl(0 0% 0% / 0.06);
    --shadow-xs: 0rem 0.125rem 0.5rem 0rem hsl(0 0% 0% / 0.06);
    --shadow-sm: 0rem 0.125rem 0.5rem 0rem hsl(0 0% 0% / 0.12), 0rem 1px 2px -1px hsl(0 0% 0% / 0.12);
    --shadow: 0rem 0.125rem 0.5rem 0rem hsl(0 0% 0% / 0.12), 0rem 1px 2px -1px hsl(0 0% 0% / 0.12);
    --shadow-md: 0rem 0.125rem 0.5rem 0rem hsl(0 0% 0% / 0.12), 0rem 2px 4px -1px hsl(0 0% 0% / 0.12);
    --shadow-lg: 0rem 0.125rem 0.5rem 0rem hsl(0 0% 0% / 0.12), 0rem 4px 6px -1px hsl(0 0% 0% / 0.12);
    --shadow-xl: 0rem 0.125rem 0.5rem 0rem hsl(0 0% 0% / 0.12), 0rem 8px 10px -1px hsl(0 0% 0% / 0.12);
    --shadow-2xl: 0rem 0.125rem 0.5rem 0rem hsl(0 0% 0% / 0.30);
}

@theme inline {
    /* PROTEC Brand Colors for Tailwind */
    --color-protec-navy: hsl(var(--protec-navy));
    --color-protec-red: hsl(var(--protec-red));
    --color-protec-gray: hsl(var(--protec-gray));

    /* shadcn/ui semantic colors */
    --color-background: var(--background);
    --color-foreground: var(--foreground);
    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);
    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);
    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);
    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);
    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);
    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);
    --color-destructive: var(--destructive);
    --color-destructive-foreground: var(--destructive-foreground);
    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);
    --color-chart-1: var(--chart-1);
    --color-chart-2: var(--chart-2);
    --color-chart-3: var(--chart-3);
    --color-chart-4: var(--chart-4);
    --color-chart-5: var(--chart-5);
    --color-sidebar: var(--sidebar);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);

    --font-sans: var(--font-sans);
    --font-mono: var(--font-mono);
    --font-serif: var(--font-serif);

    --radius-sm: calc(var(--radius) - 4px);
    --radius-md: calc(var(--radius) - 2px);
    --radius-lg: var(--radius);
    --radius-xl: calc(var(--radius) + 4px);

    --shadow-2xs: var(--shadow-2xs);
    --shadow-xs: var(--shadow-xs);
    --shadow-sm: var(--shadow-sm);
    --shadow: var(--shadow);
    --shadow-md: var(--shadow-md);
    --shadow-lg: var(--shadow-lg);
    --shadow-xl: var(--shadow-xl);
    --shadow-2xl: var(--shadow-2xl);

    --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
    --tracking-tight: calc(var(--tracking-normal) - 0.025em);
    --tracking-normal: var(--tracking-normal);
    --tracking-wide: calc(var(--tracking-normal) + 0.025em);
    --tracking-wider: calc(var(--tracking-normal) + 0.05em);
    --tracking-widest: calc(var(--tracking-normal) + 0.1em);
}

body {
    letter-spacing: var(--tracking-normal); 
}

/* Custom animations for enhanced landing page */
@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in-down {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in-left {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-in-right {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scale-in {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-fade-in {
  animation: fade-in 0.6s ease-out;
}

.animate-fade-in-up {
  animation: fade-in-up 0.6s ease-out;
}

.animate-fade-in-down {
  animation: fade-in-down 0.6s ease-out;
}

.animate-slide-in-left {
  animation: slide-in-left 0.6s ease-out;
}

.animate-slide-in-right {
  animation: slide-in-right 0.6s ease-out;
}

.animate-scale-in {
  animation: scale-in 0.6s ease-out;
}

/* Animation delays */
.delay-100 {
  animation-delay: 100ms;
}

.delay-200 {
  animation-delay: 200ms;
}

.delay-300 {
  animation-delay: 300ms;
}

.delay-400 {
  animation-delay: 400ms;
}

.delay-500 {
  animation-delay: 500ms;
}

.delay-600 {
  animation-delay: 600ms;
}

/* Line clamp utility */
.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

.line-clamp-4 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 4;
}

/* Grid background pattern */
.bg-grid-slate-100 {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' width='32' height='32' fill='none' stroke='rgb(148 163 184 / 0.05)'%3e%3cpath d='m0 .5h32m-32 32v-32'/%3e%3c/svg%3e");
}

.bg-grid-slate-700\/25 {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' width='32' height='32' fill='none' stroke='rgb(51 65 85 / 0.25)'%3e%3cpath d='m0 .5h32m-32 32v-32'/%3e%3c/svg%3e");
}

.bg-grid-white\/10 {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' width='32' height='32' fill='none' stroke='rgb(255 255 255 / 0.1)'%3e%3cpath d='m0 .5h32m-32 32v-32'/%3e%3c/svg%3e");
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Focus styles for accessibility */
.focus-visible:focus {
  outline: 2px solid hsl(var(--protec-red));
  outline-offset: 2px;
}