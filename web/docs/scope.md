# 📱 PROTEC Alumni Mobile Application Documentation

*Developed by SoImagine Systems (Johannesburg, South Africa)*

## 🗂 Overview

This document outlines the structure and capabilities of the PROTEC Alumni Mobile Application, built to empower and reconnect South African learners who have graduated through the PROTEC education initiative.

The application serves as a digital hub to:

* Reconnect alumni
* Promote ongoing mentorship and participation
* Facilitate donations and support for PROTEC programmes

---

## 🌍 South African Context

### 🎯 Target Audience

South African learners who passed through the PROTEC programme and now form part of the alumni network.

### 💳 Local Payment Gateways Supported

* **PayFast** (🇿🇦 Preferred for ZAR transactions)
* **SnapScan** (Optional QR code-based payments)
* **Ozow** (EFT-based instant payments)
* **PayPal** (For international contributions)

### 💵 Currency

All transactions, donations, and fees are calculated in **South African Rand (ZAR)**.

---

## 🧱 Core Features

### 1. 👤 User Profiles

* Editable profile with:

  * Career history
  * Education background
  * Involvement with PROTEC
* Privacy controls & activity logs
* Profile photo and biography section

### 2. 🔗 Networking Directory

* Advanced filtering: industry, graduation year, province, shared interests
* In-app messaging (no need to share contact details)
* Connection requests & management

### 3. 📅 Events Calendar

* Interactive calendar views (list or month view)
* RSVP with push/email notifications
* Categorized events (webinars, workshops, socials)

### 4. 📰 News Feed

* PROTEC announcements
* Alumni success stories
* User-submitted posts (moderated)
* Social interactions (likes, comments, shares)

### 5. 💰 Donation Portal

* One-time and recurring donation options
* ZAR-based payments via:

  * **PayFast**
  * **SnapScan**
  * **Ozow**
  * **PayPal** (for diaspora alumni)
* Real-time campaign progress
* Personal impact dashboard

### 6. 📊 Analytics Dashboard (For Admins)

* Alumni engagement metrics
* Donation trends & campaign success
* Event participation & feedback reports
* Exportable CSV/Excel reports

---

## 🔒 Security & Compliance

* **End-to-end encryption** of personal data
* **OAuth 2.0** secure authentication
* **POPIA** (Protection of Personal Information Act, ZA) compliant
* **GDPR** aligned (for overseas users)
* Regular **penetration testing** and code audits

---

## 💻 Technology Stack

| Layer         | Stack                                               |
| ------------- | --------------------------------------------------- |
| **Frontend**  | React Native (Cross-platform for iOS, Android, PWA) |
| **Backend**   | Node.js + Express.js                                |
| **Database**  | MongoDB (Cloud-hosted via AWS)                      |
| **Hosting**   | Amazon Web Services (AWS)                           |
| **Analytics** | Google Analytics                                    |

---

## 📲 Platform Support

* **Android** via Google Play Store
* **Huawei** AppGallery (relevant for local users with Huawei devices)
* **PWA** for desktop and browser-based usage

---

## 🔄 Third-Party Integrations

* **Payment**: PayFast, PayPal, SnapScan, Ozow
* **Social Login & Sharing**: Google, Facebook, LinkedIn
* **Analytics**: Google Analytics
* **Cloud Services**: AWS S3, EC2, CloudWatch

---

## 👥 User Roles

| Role                   | Description                                    |
| ---------------------- | ---------------------------------------------- |
| **Alumni**             | Access to content, profile, and networking     |
| **Admins**             | Content moderation, analytics dashboard access |
| **Event Organizers**   | Post/manage event info                         |
| **Donor Coordinators** | Track donation metrics and campaigns           |

---

## 🧪 Development & Rollout Timeline

| Phase                                | Duration | Deliverables                                  |
| ------------------------------------ | -------- | --------------------------------------------- |
| Planning & Workshops                 | 1 Day    | Scope, user personas, feature list            |
| UI/UX Design                         | 1 Week   | Wireframes, design guide, clickable prototype |
| Core Development                     | 2 Weeks  | Build all key features                        |
| Integration & Testing                | 3 Weeks  | Connect services, QA, security review         |
| Beta Testing                         | 1 Week   | Alumni feedback and UX fixes                  |
| Final Adjustments & Store Submission | 1 Week   | Deployment on Play Store and AppGallery       |

---

## 📞 Contact Information

**Company**: SoImagine Systems
**CEO**: Shawn Danisa
**Phone**: +27 11 083 5705 / +27 76 831 3475
**Email**: [<EMAIL>](mailto:<EMAIL>)
**Address**: Tudor Rose Lodge, Sunninghill Gardens, Sandton, Johannesburg, 2196
**Website**: [https://soimagine.co.za](https://soimagine.co.za)

---

## 🔗 Social Media Links

* [LinkedIn](https://www.linkedin.com/company/soimagine-systems)
* [Facebook](https://www.facebook.com/soimaginesystems)
* [Twitter](https://twitter.com/soimaginesystem)
* [YouTube](https://www.youtube.com/@soimagine)
