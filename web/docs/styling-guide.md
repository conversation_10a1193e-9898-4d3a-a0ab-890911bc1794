# PROTEC Alumni Platform - Styling Guide

## Overview

This guide provides comprehensive information about the styling system used in the PROTEC Alumni Platform, including how to apply PROTEC brand colors, use utility classes, and maintain consistency across the application.

## Quick Start

### Using PROTEC Brand Colors

```tsx
// Background colors
<div className="bg-protec-navy">Navy background</div>
<div className="bg-protec-red">Red background</div>
<div className="bg-protec-gray">Gray background</div>

// Text colors
<h1 className="text-protec-navy">Navy text</h1>
<p className="text-protec-red">Red text</p>

// Border colors
<div className="border border-protec-navy">Navy border</div>
<div className="border-l-4 border-l-protec-red">Red left border accent</div>
```

### Using Inline Styles (Recommended for CSS Variable Issues)

If Tailwind utility classes are not working, use inline styles with CSS variables:

```tsx
// Background colors
<div style={{ backgroundColor: 'hsl(var(--protec-navy))' }}>
  Navy background
</div>

// Text colors
<h1 style={{ color: 'hsl(var(--protec-navy))' }}>
  Navy text
</h1>

// Gradients
<div style={{ 
  background: 'linear-gradient(135deg, hsl(var(--protec-navy)) 0%, hsl(var(--protec-red)) 100%)' 
}}>
  PROTEC gradient
</div>
```

## PROTEC Brand Colors

### Primary Colors

| Color | CSS Variable | HSL Value | Hex Value |
|-------|-------------|-----------|-----------|
| PROTEC Navy | `--protec-navy` | `220 25% 33%` | `#3B4A6B` |
| PROTEC Red | `--protec-red` | `356 85% 52%` | `#E31E24` |
| PROTEC Gray | `--protec-gray` | `210 17% 96%` | `#F5F5F5` |

### Usage Guidelines

- **Navy**: Primary brand color for headers, navigation, and important text
- **Red**: Accent color for CTAs, highlights, and interactive elements
- **Gray**: Background color for subtle sections and cards

## Common Patterns

### Dashboard Cards

```tsx
<Card className="border-l-4 border-l-protec-red card-hover">
  <CardHeader>
    <CardTitle className="text-protec-navy">
      Card Title
    </CardTitle>
  </CardHeader>
  <CardContent>
    <p className="text-muted-foreground">Card content</p>
  </CardContent>
</Card>
```

### Buttons

```tsx
// Primary button
<Button 
  className="bg-protec-red hover:bg-protec-red/90"
  // OR using inline styles
  style={{ 
    backgroundColor: 'hsl(var(--protec-red))',
    color: 'white'
  }}
>
  Primary Action
</Button>

// Secondary button
<Button 
  variant="outline"
  className="border-protec-navy text-protec-navy hover:bg-protec-navy hover:text-white"
  // OR using inline styles
  style={{ 
    borderColor: 'hsl(var(--protec-navy))',
    color: 'hsl(var(--protec-navy))'
  }}
>
  Secondary Action
</Button>
```

### Hero Sections

```tsx
<div 
  className="relative overflow-hidden rounded-lg text-white"
  style={{ 
    background: 'linear-gradient(135deg, hsl(var(--protec-navy)) 0%, hsl(var(--protec-red)) 100%)' 
  }}
>
  <div className="relative px-8 py-12 text-center">
    <h1 className="text-4xl font-bold mb-4">
      Hero Title
    </h1>
    <p className="text-xl opacity-90">
      Hero description
    </p>
  </div>
</div>
```

## Utility Classes

### PROTEC-Specific Classes

```css
/* Background Colors */
.bg-protec-navy
.bg-protec-red
.bg-protec-gray

/* Text Colors */
.text-protec-navy
.text-protec-red

/* Border Colors */
.border-protec-navy
.border-protec-red
.border-l-protec-red
.border-l-protec-red/20

/* Hover States */
.hover:bg-protec-navy:hover
.hover:bg-protec-red:hover
.hover:bg-protec-red/90:hover
.hover:text-white:hover

/* Gradients */
.protec-gradient
.protec-gradient-subtle

/* Enhanced Effects */
.card-hover
.focus-ring
.loading-skeleton
```

### Layout Classes

```css
.container-narrow    /* Max-width 896px */
.container-wide      /* Max-width 1280px */
.grid-auto-fit       /* Auto-fit grid */
.grid-auto-fill      /* Auto-fill grid */
```

### Animation Classes

```css
.animate-fade-in
.animate-slide-in
.animate-bounce-gentle
```

## Responsive Design

### Breakpoints

```css
sm: 640px
md: 768px
lg: 1024px
xl: 1280px
2xl: 1536px
```

### Usage

```tsx
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
  {/* Responsive grid */}
</div>

<h1 className="text-2xl md:text-3xl lg:text-4xl font-bold">
  Responsive heading
</h1>
```

## Dark Mode

The design system includes comprehensive dark mode support:

```tsx
// Automatic dark mode support
<div className="bg-background text-foreground">
  Content adapts to theme
</div>

// Manual dark mode variants
<div className="bg-white dark:bg-gray-900">
  Manual theme handling
</div>
```

## Accessibility

### Color Contrast

All PROTEC color combinations meet WCAG AA standards:
- Navy on white: 7.2:1 ratio
- Red on white: 4.8:1 ratio
- White on navy: 7.2:1 ratio

### Focus Management

```tsx
<button className="focus-ring">
  Accessible button with focus ring
</button>
```

### Screen Reader Support

```tsx
<span className="sr-only">Screen reader only text</span>
```

## Troubleshooting

### Styles Not Applying

If Tailwind utility classes are not working:

1. **Use inline styles with CSS variables**:
   ```tsx
   <div style={{ backgroundColor: 'hsl(var(--protec-navy))' }}>
     Content
   </div>
   ```

2. **Check CSS variable definitions** in `app/globals.css`

3. **Verify Tailwind configuration** in `tailwind.config.ts`

### Common Issues

1. **Unknown utility class errors**: Use inline styles as fallback
2. **Colors not displaying**: Check CSS variable definitions
3. **Dark mode not working**: Ensure theme provider is configured

## Best Practices

### Do's

✅ Use PROTEC brand colors consistently
✅ Apply proper contrast ratios
✅ Use responsive design patterns
✅ Include focus states for accessibility
✅ Test in both light and dark modes

### Don'ts

❌ Use arbitrary colors outside the brand palette
❌ Ignore accessibility guidelines
❌ Hardcode pixel values instead of using spacing scale
❌ Forget to test responsive breakpoints
❌ Skip focus states on interactive elements

## Examples

### Complete Component Example

```tsx
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

export function ExampleCard() {
  return (
    <Card 
      className="card-hover"
      style={{ borderLeft: '4px solid hsl(var(--protec-red) / 0.2)' }}
    >
      <CardHeader>
        <CardTitle style={{ color: 'hsl(var(--protec-navy))' }}>
          Example Card
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <p style={{ color: 'hsl(var(--muted-foreground))' }}>
          This is an example card using PROTEC styling.
        </p>
        
        <div className="flex gap-2">
          <Badge style={{ 
            backgroundColor: 'hsl(var(--protec-navy))', 
            color: 'white' 
          }}>
            Navy Badge
          </Badge>
          <Badge style={{ 
            backgroundColor: 'hsl(var(--protec-red))', 
            color: 'white' 
          }}>
            Red Badge
          </Badge>
        </div>
        
        <Button 
          style={{ 
            backgroundColor: 'hsl(var(--protec-red))',
            color: 'white'
          }}
        >
          PROTEC Button
        </Button>
      </CardContent>
    </Card>
  )
}
```

This styling guide ensures consistent application of the PROTEC brand across the entire platform while maintaining accessibility and usability standards.
