# Magic Links Authentication Setup Guide

This guide will help you set up Magic Links authentication for the PROTEC Alumni Network using NextAuth.js and Nodemailer.

## Overview

Magic Links provide a passwordless authentication method where users receive a secure link via email to sign in. This implementation includes:

- Custom email templates with PROTEC branding
- Enhanced security with link expiration
- Welcome emails for new users
- Admin testing interface
- Support for multiple email providers

## Prerequisites

- Node.js and npm/yarn installed
- A working email service (Gmail, SendGrid, Mailgun, etc.)
- Database configured with Prisma

## 1. Environment Configuration

Copy the `.env.example` file to `.env.local` and configure the email settings:

```bash
cp .env.example .env.local
```

### Email Provider Options

#### Option 1: Gmail (Recommended for Development)

```env
EMAIL_SERVER_HOST="smtp.gmail.com"
EMAIL_SERVER_PORT="587"
EMAIL_SERVER_USER="<EMAIL>"
EMAIL_SERVER_PASSWORD="your-app-password"
EMAIL_SERVER_SECURE="false"
EMAIL_FROM="<EMAIL>"
```

**Setup Steps for Gmail:**
1. Enable 2-Factor Authentication on your Google account
2. Generate an App Password:
   - Go to Google Account settings
   - Security → 2-Step Verification → App passwords
   - Generate a password for "Mail"
   - Use this password in `EMAIL_SERVER_PASSWORD`

#### Option 2: SendGrid (Recommended for Production)

```env
EMAIL_SERVER_HOST="smtp.sendgrid.net"
EMAIL_SERVER_PORT="587"
EMAIL_SERVER_USER="apikey"
EMAIL_SERVER_PASSWORD="your-sendgrid-api-key"
EMAIL_SERVER_SECURE="false"
EMAIL_FROM="<EMAIL>"
```

**Setup Steps for SendGrid:**
1. Create a SendGrid account
2. Generate an API key with "Mail Send" permissions
3. Verify your sender domain/email
4. Use "apikey" as the username and your API key as the password

#### Option 3: AWS SES (Enterprise)

```env
EMAIL_SERVER_HOST="email-smtp.us-east-1.amazonaws.com"
EMAIL_SERVER_PORT="587"
EMAIL_SERVER_USER="your-ses-smtp-username"
EMAIL_SERVER_PASSWORD="your-ses-smtp-password"
EMAIL_SERVER_SECURE="false"
EMAIL_FROM="<EMAIL>"
```

**Setup Steps for AWS SES:**
1. Set up AWS SES in your preferred region
2. Verify your domain and email addresses
3. Create SMTP credentials in the SES console
4. Move out of sandbox mode for production

## 2. NextAuth Configuration

The authentication is already configured in `lib/auth.ts` with:

- Custom email provider with Nodemailer
- Custom email templates
- Welcome email automation
- User creation for new sign-ins

## 3. Database Setup

Ensure your database includes the required NextAuth tables:

```bash
npx prisma db push
```

The following tables are required:
- `Account`
- `Session`
- `User`
- `VerificationToken`
- `Alumni` (custom table for user data)

## 4. Email Templates

The system includes two main email templates:

### Magic Link Email
- Professional PROTEC branding
- Security notices
- Mobile-friendly design
- Fallback text version

### Welcome Email
- Onboarding information
- Feature highlights
- Call-to-action buttons
- Support contact information

Templates are located in `lib/services/email-service.ts` and can be customized.

## 5. Testing the Setup

### Admin Testing Interface

Access the admin email testing panel at `/admin` (requires admin role):

1. **Connection Test**: Verify SMTP server connectivity
2. **Magic Link Test**: Send test magic link emails
3. **Welcome Email Test**: Send test welcome emails
4. **Custom Email Test**: Send custom test emails

### API Testing

Use the `/api/test-email` endpoint for programmatic testing:

```javascript
// Test connection
fetch('/api/test-email', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ type: 'connection' })
})

// Test magic link
fetch('/api/test-email', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ 
    type: 'magic-link',
    email: '<EMAIL>'
  })
})
```

## 6. User Flow

### Sign In Process
1. User enters email on `/auth/signin`
2. System sends magic link email
3. User redirected to `/auth/verify-request`
4. User clicks link in email
5. User authenticated and redirected to dashboard

### New User Process
1. Same as sign-in process
2. System creates new Alumni record
3. Welcome email sent automatically
4. User redirected to onboarding

## 7. Security Features

- **Link Expiration**: Magic links expire after 24 hours
- **Single Use**: Links can only be used once
- **Secure Tokens**: Cryptographically secure tokens
- **Rate Limiting**: Built-in rate limiting for email sending
- **CSRF Protection**: NextAuth CSRF protection

## 8. Customization

### Email Templates

Modify templates in `lib/services/email-service.ts`:

```typescript
const emailTemplates = {
  magicLink: {
    subject: 'Your custom subject',
    html: (url: string, host: string) => `Your custom HTML`,
    text: (url: string, host: string) => `Your custom text`
  }
}
```

### Email Service

Extend the email service for additional functionality:

```typescript
export const emailService = {
  // Add custom email functions
  async sendPasswordReset(email: string, token: string) {
    // Implementation
  }
}
```

## 9. Production Considerations

### Email Deliverability
- Use a dedicated email service (SendGrid, Mailgun, AWS SES)
- Set up SPF, DKIM, and DMARC records
- Monitor bounce and complaint rates
- Use a dedicated IP if sending high volumes

### Performance
- Implement email queuing for high volumes
- Use connection pooling
- Monitor email service limits
- Set up proper error handling and retries

### Security
- Use environment variables for all credentials
- Enable email service security features
- Monitor for suspicious activity
- Implement rate limiting

## 10. Troubleshooting

### Common Issues

**Email not sending:**
- Check SMTP credentials
- Verify email service configuration
- Check firewall/network restrictions
- Review email service logs

**Magic links not working:**
- Verify NEXTAUTH_URL is correct
- Check database connection
- Ensure VerificationToken table exists
- Check for expired tokens

**Welcome emails not sending:**
- Check signIn callback in auth configuration
- Verify email service is working
- Check for errors in server logs

### Debug Mode

Enable debug logging:

```env
NEXTAUTH_DEBUG=true
```

### Testing Commands

```bash
# Test database connection
npx prisma db push

# Check email configuration
curl -X GET http://localhost:3000/api/test-email

# Send test email
curl -X POST http://localhost:3000/api/test-email \
  -H "Content-Type: application/json" \
  -d '{"type":"connection"}'
```

## 11. Monitoring

### Email Metrics
- Delivery rates
- Open rates (if tracking enabled)
- Bounce rates
- Complaint rates

### Application Metrics
- Sign-in success rates
- Magic link click rates
- User conversion rates
- Error rates

## Support

For additional help:
- Check the NextAuth.js documentation
- Review email service provider documentation
- Contact the development team
- Check application logs for detailed error messages

## Security Best Practices

1. **Never log email credentials**
2. **Use app-specific passwords for Gmail**
3. **Rotate credentials regularly**
4. **Monitor for suspicious activity**
5. **Implement proper error handling**
6. **Use HTTPS in production**
7. **Validate email addresses**
8. **Implement rate limiting**