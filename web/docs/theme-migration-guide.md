# PROTEC Theme Migration Guide

## Overview

This guide helps you migrate from custom PROTEC color classes to shadcn/ui semantic theme classes for better consistency, accessibility, and maintainability.

## Color Mapping

### Primary Colors (PROTEC Navy → Primary)

| Old Class | New Class | Usage |
|-----------|-----------|-------|
| `bg-protec-navy` | `bg-primary` | Primary backgrounds |
| `text-protec-navy` | `text-primary` | Primary text |
| `border-protec-navy` | `border-primary` | Primary borders |
| `hover:bg-protec-navy` | `hover:bg-primary` | Primary hover states |

### Accent Colors (PROTEC Red → Accent)

| Old Class | New Class | Usage |
|-----------|-----------|-------|
| `bg-protec-red` | `bg-accent` | Accent backgrounds |
| `text-protec-red` | `text-accent` | Accent text |
| `border-protec-red` | `border-accent` | Accent borders |
| `hover:bg-protec-red` | `hover:bg-accent` | Accent hover states |
| `bg-protec-red/90` | `bg-accent/90` | Accent with opacity |

### Neutral Colors

| Old Class | New Class | Usage |
|-----------|-----------|-------|
| `bg-protec-gray` | `bg-muted` | Subtle backgrounds |
| `text-gray-600` | `text-muted-foreground` | Muted text |
| `bg-blue-50` | `bg-muted` | Light backgrounds |

## Benefits of Migration

### 1. Automatic Dark Mode Support
```tsx
// ❌ Old way - no dark mode support
<div className="bg-protec-navy text-white">

// ✅ New way - automatic dark mode
<div className="bg-primary text-primary-foreground">
```

### 2. Better Accessibility
```tsx
// ❌ Old way - fixed contrast
<button className="bg-protec-red text-white">

// ✅ New way - accessible contrast ratios
<button className="bg-accent text-accent-foreground">
```

### 3. Consistent Design System
```tsx
// ❌ Old way - mixed approaches
<Card className="border-protec-red">
  <CardTitle className="text-protec-navy">

// ✅ New way - consistent semantic naming
<Card className="border-accent">
  <CardTitle className="text-primary">
```

## Migration Examples

### Avatar Components
```tsx
// ❌ Before
<AvatarFallback className="bg-protec-navy text-white">
  {initials}
</AvatarFallback>

// ✅ After
<AvatarFallback className="bg-primary text-primary-foreground">
  {initials}
</AvatarFallback>
```

### Button Components
```tsx
// ❌ Before
<Button className="bg-protec-red hover:bg-protec-red/90">
  Submit
</Button>

// ✅ After
<Button className="bg-accent hover:bg-accent/90">
  Submit
</Button>
```

### Card Components
```tsx
// ❌ Before
<Card className="border-l-4 border-l-protec-red">
  <CardTitle className="text-protec-navy">
    Title
  </CardTitle>
</Card>

// ✅ After
<Card className="border-l-4 border-l-accent">
  <CardTitle className="text-primary">
    Title
  </CardTitle>
</Card>
```

### Selection States
```tsx
// ❌ Before
<div className={cn(
  "p-4 cursor-pointer",
  isSelected && "bg-protec-red/10 border-r-2 border-protec-red"
)}>

// ✅ After
<div className={cn(
  "p-4 cursor-pointer",
  isSelected && "bg-accent/10 border-r-2 border-accent"
)}>
```

## When to Use Brand Colors

Keep the `protec-*` classes for these specific cases:

### 1. Brand-specific Elements
```tsx
// Logo areas, brand headers
<div className="bg-protec-navy">
  <img src="/logo.png" alt="PROTEC" />
</div>
```

### 2. Marketing/Landing Pages
```tsx
// Hero sections, marketing content
<section className="gradient-protec">
  <h1 className="gradient-text-protec">Welcome to PROTEC</h1>
</section>
```

### 3. Charts and Data Visualization
```tsx
// When you need exact brand colors for charts
<div style={{ backgroundColor: 'hsl(var(--protec-red))' }}>
```

## Available Classes

### Semantic Classes (Preferred)
- `bg-primary` / `text-primary` / `border-primary`
- `bg-accent` / `text-accent` / `border-accent`
- `bg-muted` / `text-muted-foreground` / `border-muted`
- `bg-card` / `text-card-foreground` / `border-border`

### Brand Classes (When Needed)
- `bg-protec-navy` / `text-protec-navy` / `border-protec-navy`
- `bg-protec-red` / `text-protec-red` / `border-protec-red`
- `bg-protec-gray` / `text-protec-gray` / `border-protec-gray`

### Utility Classes
- `gradient-protec` - PROTEC brand gradient
- `gradient-text-protec` - PROTEC gradient text
- `hero-bg` - Hero section background
- `border-l-accent` - Left accent border

## Migration Checklist

### Phase 1: Core Components
- [ ] Update Avatar components
- [ ] Update Button components
- [ ] Update Card components
- [ ] Update Badge components

### Phase 2: Layout Components
- [ ] Update Navigation components
- [ ] Update Sidebar components
- [ ] Update Header components
- [ ] Update Footer components

### Phase 3: Feature Components
- [ ] Update Message components
- [ ] Update Feed components
- [ ] Update Dashboard components
- [ ] Update Form components

### Phase 4: Page Components
- [ ] Update Landing pages
- [ ] Update Dashboard pages
- [ ] Update Profile pages
- [ ] Update Admin pages

## Testing

After migration, test:

1. **Light Mode**: All components look correct
2. **Dark Mode**: Colors adapt properly
3. **Accessibility**: Contrast ratios are maintained
4. **Hover States**: Interactive elements work
5. **Focus States**: Keyboard navigation works

## CSS Variables Reference

### PROTEC Brand Colors
```css
--protec-navy: 220 25% 33%;    /* #3B4A6B */
--protec-red: 356 85% 52%;     /* #E31E24 */
--protec-gray: 210 17% 96%;    /* #F5F5F5 */
```

### Semantic Mappings
```css
--primary: /* Maps to PROTEC Navy */
--accent: /* Maps to PROTEC Red */
--muted: /* Maps to light grays */
```

## Need Help?

- Check the [Styling Guide](./styling-guide.md) for examples
- Review the [Components Guide](./components.md) for patterns
- Look at migrated components for reference