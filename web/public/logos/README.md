# PROTEC Alumni Platform - Logo Assets

## Logo Files

### Required Logo Versions
- `protec-primary.svg` - Full color logo for light backgrounds
- `protec-white.svg` - White logo for dark backgrounds  
- `favicon.png` - 32x32 favicon version

### Usage Guidelines

#### protec-primary.svg
- **Use on**: Light backgrounds, white backgrounds
- **Minimum width**: 120px
- **Format**: SVG (scalable)
- **Colors**: PROTEC Primary (#012A5B) and Accent (#D71920)

#### protec-white.svg  
- **Use on**: Dark backgrounds, PROTEC Primary backgrounds
- **Minimum width**: 120px
- **Format**: SVG (scalable)
- **Color**: White (#FFFFFF)

#### favicon.png
- **Use for**: Browser favicon, app icons
- **Size**: 32x32 pixels
- **Format**: PNG
- **Style**: Simplified logo mark or abstract badge

### Implementation Examples

```jsx
// Light background
<img src="/logos/protec-primary.svg" alt="PROTEC" className="h-8" />

// Dark background  
<img src="/logos/protec-white.svg" alt="PROTEC" className="h-8" />

// Responsive logo
<img 
  src="/logos/protec-primary.svg" 
  alt="PROTEC Alumni Platform"
  className="h-8 dark:hidden"
/>
<img 
  src="/logos/protec-white.svg" 
  alt="PROTEC Alumni Platform" 
  className="h-8 hidden dark:block"
/>
```

### Logo Specifications
- **Clear space**: Equal to the height of the logo mark
- **Never**: Stretch, recolor, rotate, or crop the logo
- **Accessibility**: Always include descriptive alt text

---

*Note: Actual logo files should be provided by the PROTEC design team*