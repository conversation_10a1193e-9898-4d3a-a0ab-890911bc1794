/* PROTEC Alumni Platform - Utility Classes for shadcn/ui */

/* 
 * PREFERRED APPROACH: Use shadcn/ui semantic classes for most styling:
 * 
 * INSTEAD OF:              USE:
 * bg-protec-navy          bg-primary
 * text-protec-navy        text-primary
 * bg-protec-red           bg-accent  
 * text-protec-red         text-accent
 * border-protec-red       border-accent
 * 
 * BENEFITS:
 * - Automatic dark mode support
 * - Consistent with shadcn/ui design system
 * - Better accessibility
 * - Easier maintenance
 */

/* PROTEC Brand Colors - Use only when semantic colors don't fit */
.bg-protec-navy { background-color: hsl(var(--protec-navy)); }
.bg-protec-red { background-color: hsl(var(--protec-red)); }
.bg-protec-gray { background-color: hsl(var(--protec-gray)); }

.text-protec-navy { color: hsl(var(--protec-navy)); }
.text-protec-red { color: hsl(var(--protec-red)); }
.text-protec-gray { color: hsl(var(--protec-gray)); }

.border-protec-navy { border-color: hsl(var(--protec-navy)); }
.border-protec-red { border-color: hsl(var(--protec-red)); }
.border-protec-gray { border-color: hsl(var(--protec-gray)); }

/* Border accents using both semantic and brand colors */
.border-l-primary { border-left-color: hsl(var(--primary)); }
.border-l-accent { border-left-color: hsl(var(--accent)); }
.border-l-protec-navy { border-left-color: hsl(var(--protec-navy)); }
.border-l-protec-red { border-left-color: hsl(var(--protec-red)); }

/* Hover states for PROTEC brand colors */
.hover\:bg-protec-navy:hover { background-color: hsl(var(--protec-navy)); }
.hover\:bg-protec-red:hover { background-color: hsl(var(--protec-red)); }
.hover\:bg-protec-red\/90:hover { background-color: hsl(var(--protec-red) / 0.9); }
.hover\:text-protec-navy:hover { color: hsl(var(--protec-navy)); }
.hover\:text-protec-red:hover { color: hsl(var(--protec-red)); }

/* PROTEC Brand Gradients - Use these sparingly for special effects */
.gradient-protec {
  background: linear-gradient(135deg, hsl(var(--protec-navy)) 0%, hsl(var(--protec-red)) 100%);
}

.gradient-text-protec {
  background: linear-gradient(135deg, hsl(var(--protec-navy)) 0%, hsl(var(--protec-red)) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-bg {
  background: linear-gradient(135deg,
          hsl(var(--background)) 0%,
          hsl(var(--muted)) 30%,
          hsl(var(--background)) 70%,
          hsl(var(--protec-navy) / 0.05) 100%);
}

/* Accessibility and Animation Utilities */

/* Loading Skeleton using shadcn colors */
.loading-skeleton {
  background: linear-gradient(90deg, hsl(var(--muted)) 25%, hsl(var(--muted) / 0.5) 50%, hsl(var(--muted)) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Gentle animations for PROTEC brand */
.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Utility Classes */
.text-balance {
  text-wrap: balance;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Accessibility Support */
@media (prefers-contrast: high) {
  .gradient-protec {
    background: hsl(var(--primary));
  }
  
  .hero-bg {
    background: hsl(var(--background));
  }
}

@media (prefers-reduced-motion: reduce) {
  .animate-fade-in,
  .loading-skeleton {
    animation: none;
  }
  
  * {
    transition: none !important;
  }
}