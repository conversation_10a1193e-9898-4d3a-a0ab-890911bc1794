import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  GraduationCap, 
  Users, 
  Award, 
  Target, 
  Heart, 
  Lightbulb,
  TrendingUp,
  Globe
} from "lucide-react"

const milestones = [
  {
    year: "1982",
    title: "PROTEC Founded",
    description: "Established to address the shortage of skilled professionals in STEM fields",
    icon: GraduationCap
  },
  {
    year: "1990s",
    title: "Expansion Across SA",
    description: "Extended programmes to multiple provinces, reaching more communities",
    icon: Globe
  },
  {
    year: "2000s",
    title: "Industry Partnerships",
    description: "Formed strategic partnerships with leading companies and universities",
    icon: Users
  },
  {
    year: "2010s",
    title: "Digital Innovation",
    description: "Embraced technology to enhance learning and career development",
    icon: Lightbulb
  },
  {
    year: "2020s",
    title: "Alumni Network",
    description: "Launched comprehensive alumni platform for lifelong connections",
    icon: TrendingUp
  }
]

const values = [
  {
    icon: Award,
    title: "Excellence",
    description: "Striving for the highest standards in education and professional development"
  },
  {
    icon: Heart,
    title: "Community",
    description: "Building strong, supportive networks that last beyond graduation"
  },
  {
    icon: Target,
    title: "Impact",
    description: "Creating meaningful change in South Africa's STEM landscape"
  },
  {
    icon: Users,
    title: "Inclusion",
    description: "Ensuring equal opportunities for all aspiring STEM professionals"
  }
]

export function LandingAbout() {
  return (
    <section className="py-20 sm:py-32 bg-gradient-to-b from-white to-protec-gray/30">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        {/* Header */}
        <div className="mx-auto max-w-2xl text-center mb-16">
          <Badge variant="outline" className="mb-4 border-protec-navy/20 bg-protec-navy/5 text-protec-navy">
            <Award className="mr-2 h-4 w-4" />
            Our Story
          </Badge>
          <h2 className="text-3xl font-bold tracking-tight text-protec-navy sm:text-4xl">
            40 Years of Transforming Lives
          </h2>
          <p className="mt-6 text-lg leading-8 text-gray-600">
            Since 1982, PROTEC has been at the forefront of developing South Africa's STEM talent, 
            creating pathways to success for thousands of students and professionals.
          </p>
        </div>

        {/* Mission Statement */}
        <div className="mb-20">
          <Card className="border-0 bg-gradient-to-br from-protec-navy to-protec-red text-white shadow-2xl">
            <CardContent className="p-12 text-center">
              <h3 className="text-2xl font-bold mb-6">Our Mission</h3>
              <p className="text-xl leading-relaxed opacity-90 max-w-4xl mx-auto">
                To develop and empower South African students and professionals in Science, 
                Technology, Engineering, and Mathematics, creating a sustainable pipeline of 
                skilled talent that drives innovation and economic growth across the continent.
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Timeline */}
        <div className="mb-20">
          <h3 className="text-2xl font-bold text-protec-navy text-center mb-12">
            Our Journey Through the Years
          </h3>
          <div className="relative">
            {/* Timeline line */}
            <div className="absolute left-1/2 transform -translate-x-px h-full w-0.5 bg-gradient-to-b from-protec-navy to-protec-red" />
            
            <div className="space-y-12">
              {milestones.map((milestone, index) => {
                const Icon = milestone.icon
                const isEven = index % 2 === 0
                
                return (
                  <div key={milestone.year} className="relative flex items-center">
                    {/* Timeline dot */}
                    <div className="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-protec-red rounded-full border-4 border-white shadow-lg z-10" />
                    
                    {/* Content */}
                    <div className={`w-1/2 ${isEven ? 'pr-8 text-right' : 'pl-8 ml-auto'}`}>
                      <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                        <CardContent className="p-6">
                          <div className={`flex items-center gap-3 mb-3 ${isEven ? 'justify-end' : 'justify-start'}`}>
                            <div className={`flex h-10 w-10 items-center justify-center rounded-full bg-protec-navy/10 ${isEven ? 'order-2' : ''}`}>
                              <Icon className="h-5 w-5 text-protec-navy" />
                            </div>
                            <Badge variant="outline" className="border-protec-red/20 bg-protec-red/5 text-protec-red">
                              {milestone.year}
                            </Badge>
                          </div>
                          <h4 className="text-lg font-semibold text-protec-navy mb-2">
                            {milestone.title}
                          </h4>
                          <p className="text-gray-600">
                            {milestone.description}
                          </p>
                        </CardContent>
                      </Card>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        </div>

        {/* Values */}
        <div>
          <h3 className="text-2xl font-bold text-protec-navy text-center mb-12">
            Our Core Values
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {values.map((value, index) => {
              const Icon = value.icon
              return (
                <Card 
                  key={value.title} 
                  className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 group"
                >
                  <CardContent className="p-6 text-center">
                    <div className="flex h-16 w-16 items-center justify-center rounded-full bg-protec-navy/10 group-hover:bg-protec-navy/20 transition-colors mx-auto mb-4">
                      <Icon className="h-8 w-8 text-protec-navy" />
                    </div>
                    <h4 className="text-lg font-semibold text-protec-navy mb-3">
                      {value.title}
                    </h4>
                    <p className="text-gray-600 text-sm leading-relaxed">
                      {value.description}
                    </p>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      </div>
    </section>
  )
}