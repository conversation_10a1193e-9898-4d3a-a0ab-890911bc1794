"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Quote, Star, ChevronLeft, ChevronRight, Users2 } from "lucide-react"

const testimonials = [
  {
    content: "PROTEC didn't just give me an education, it gave me a network that has been instrumental in my career growth. The alumni platform keeps us all connected and continues to open doors.",
    author: "<PERSON>hab<PERSON> Mthembu",
    role: "Senior Software Engineer",
    company: "Microsoft",
    year: "Class of 2015",
    avatar: "/avatars/thabo.jpg",
    initials: "TM",
    rating: 5,
    category: "Career Growth"
  },
  {
    content: "Through the alumni network, I found my first job, my mentor, and even co-founded my startup with a fellow PROTEC graduate. The connections you make here last a lifetime.",
    author: "<PERSON><PERSON><PERSON>",
    role: "Founder & CEO",
    company: "TechInnovate SA",
    year: "Class of 2012",
    avatar: "/avatars/nomsa.jpg",
    initials: "ND",
    rating: 5,
    category: "Entrepreneurship"
  },
  {
    content: "Being able to give back to current students through the platform while staying connected with my peers has been incredibly rewarding. PROTEC's impact extends far beyond graduation.",
    author: "Ahmed Hassan",
    role: "Principal Engineer",
    company: "Amazon Web Services",
    year: "Class of 2008",
    avatar: "/avatars/ahmed.jpg",
    initials: "AH",
    rating: 5,
    category: "Mentorship"
  },
  {
    content: "The mentorship I received from PROTEC alumni helped me navigate my career in engineering. Now I'm proud to mentor the next generation through this amazing platform.",
    author: "Lerato Mokwena",
    role: "Engineering Manager",
    company: "Naspers",
    year: "Class of 2010",
    avatar: "/avatars/lerato.jpg",
    initials: "LM",
    rating: 5,
    category: "Mentorship"
  },
  {
    content: "From Cape Town to Silicon Valley, the PROTEC network has been my constant. The platform makes it easy to stay connected and discover opportunities worldwide.",
    author: "David Chen",
    role: "Product Manager",
    company: "Google",
    year: "Class of 2014",
    avatar: "/avatars/david.jpg",
    initials: "DC",
    rating: 5,
    category: "Global Network"
  },
  {
    content: "The alumni events and networking opportunities have been game-changers for my career. PROTEC continues to invest in our success long after graduation.",
    author: "Zanele Ndaba",
    role: "Data Scientist",
    company: "Standard Bank",
    year: "Class of 2016",
    avatar: "/avatars/zanele.jpg",
    initials: "ZN",
    rating: 5,
    category: "Networking"
  }
]

const categories = ["All", "Career Growth", "Entrepreneurship", "Mentorship", "Global Network", "Networking"]

export function LandingTestimonials() {
  const [selectedCategory, setSelectedCategory] = useState("All")
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = useState(true)

  const filteredTestimonials = selectedCategory === "All" 
    ? testimonials 
    : testimonials.filter(t => t.category === selectedCategory)

  // Auto-rotate testimonials
  useEffect(() => {
    if (!isAutoPlaying) return
    
    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % filteredTestimonials.length)
    }, 5000)

    return () => clearInterval(interval)
  }, [filteredTestimonials.length, isAutoPlaying])

  const nextTestimonial = () => {
    setCurrentIndex((prev) => (prev + 1) % filteredTestimonials.length)
    setIsAutoPlaying(false)
  }

  const prevTestimonial = () => {
    setCurrentIndex((prev) => (prev - 1 + filteredTestimonials.length) % filteredTestimonials.length)
    setIsAutoPlaying(false)
  }

  const featuredTestimonial = filteredTestimonials[currentIndex]
  const otherTestimonials = filteredTestimonials.filter((_, index) => index !== currentIndex)

  return (
    <section className="py-20 sm:py-32 bg-gradient-to-b from-protec-gray/30 to-white">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        {/* Header */}
        <div className="mx-auto max-w-2xl text-center mb-16">
          <Badge variant="outline" className="mb-4 border-protec-red/20 bg-protec-red/5 text-protec-red">
            <Users2 className="mr-2 h-4 w-4" />
            Alumni Stories
          </Badge>
          <h2 className="text-3xl font-bold tracking-tight text-protec-navy sm:text-4xl">
            Stories from our alumni
          </h2>
          <p className="mt-6 text-lg leading-8 text-gray-600">
            Hear from PROTEC graduates who are making their mark in the world and how the alumni network continues to support their journey.
          </p>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-2 mb-12">
          {categories.map((category) => (
            <Button
              key={category}
              variant={selectedCategory === category ? "default" : "outline"}
              className={`${
                selectedCategory === category 
                  ? "bg-protec-navy hover:bg-protec-navy/90" 
                  : "hover:bg-protec-navy/10"
              } transition-all duration-300`}
              onClick={() => {
                setSelectedCategory(category)
                setCurrentIndex(0)
              }}
            >
              {category}
            </Button>
          ))}
        </div>

        {/* Featured Testimonial */}
        {featuredTestimonial && (
          <div className="mb-16">
            <Card className="border-0 bg-gradient-to-br from-protec-navy to-protec-red text-white shadow-2xl max-w-4xl mx-auto">
              <CardContent className="p-12">
                <div className="flex items-center justify-between mb-8">
                  <Quote className="h-12 w-12 text-white/30" />
                  <div className="flex">
                    {[...Array(featuredTestimonial.rating)].map((_, i) => (
                      <Star key={i} className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                    ))}
                  </div>
                </div>

                <blockquote className="text-xl leading-relaxed mb-8 text-white/90">
                  "{featuredTestimonial.content}"
                </blockquote>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <Avatar className="h-16 w-16 border-2 border-white/20">
                      <AvatarImage src={featuredTestimonial.avatar} alt={featuredTestimonial.author} />
                      <AvatarFallback className="bg-white/20 text-white font-semibold text-lg">
                        {featuredTestimonial.initials}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="font-semibold text-white text-lg">
                        {featuredTestimonial.author}
                      </div>
                      <div className="text-white/80">
                        {featuredTestimonial.role} at {featuredTestimonial.company}
                      </div>
                      <Badge variant="outline" className="mt-1 border-white/30 bg-white/10 text-white">
                        {featuredTestimonial.year}
                      </Badge>
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-white hover:bg-white/20"
                      onClick={prevTestimonial}
                    >
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-white hover:bg-white/20"
                      onClick={nextTestimonial}
                    >
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Other Testimonials Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {otherTestimonials.slice(0, 6).map((testimonial, index) => (
            <Card 
              key={testimonial.author} 
              className="group relative overflow-hidden border-0 bg-white shadow-lg transition-all duration-300 hover:shadow-xl hover:-translate-y-1 cursor-pointer"
              onClick={() => setCurrentIndex(filteredTestimonials.findIndex(t => t.author === testimonial.author))}
            >
              <CardContent className="p-6">
                {/* Rating */}
                <div className="flex items-center gap-1 mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>

                {/* Quote icon */}
                <div className="mb-4">
                  <Quote className="h-6 w-6 text-protec-red/20" />
                </div>

                {/* Testimonial content */}
                <blockquote className="text-gray-700 leading-relaxed text-sm mb-6 line-clamp-4">
                  "{testimonial.content}"
                </blockquote>

                {/* Author info */}
                <div className="flex items-center gap-3">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={testimonial.avatar} alt={testimonial.author} />
                    <AvatarFallback className="bg-protec-navy text-white font-semibold text-sm">
                      {testimonial.initials}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <div className="font-semibold text-protec-navy text-sm truncate">
                      {testimonial.author}
                    </div>
                    <div className="text-xs text-gray-600 truncate">
                      {testimonial.role}
                    </div>
                    <Badge variant="outline" className="mt-1 border-protec-red/20 bg-protec-red/5 text-protec-red text-xs">
                      {testimonial.category}
                    </Badge>
                  </div>
                </div>
              </CardContent>

              {/* Hover effect */}
              <div className="absolute inset-0 bg-gradient-to-br from-protec-navy/5 to-protec-red/5 opacity-0 transition-opacity group-hover:opacity-100" />
            </Card>
          ))}
        </div>

        {/* Pagination Dots */}
        <div className="flex justify-center gap-2 mt-12">
          {filteredTestimonials.map((_, index) => (
            <button
              key={index}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                index === currentIndex 
                  ? "bg-protec-red scale-125" 
                  : "bg-gray-300 hover:bg-gray-400"
              }`}
              onClick={() => setCurrentIndex(index)}
            />
          ))}
        </div>
      </div>
    </section>
  )
}
