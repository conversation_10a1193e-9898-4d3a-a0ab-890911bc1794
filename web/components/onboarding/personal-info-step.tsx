"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { 
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form"
import { api } from "@/components/providers/trpc-provider"
import { toast } from "sonner"
import { ArrowLeft, ArrowRight, User, Upload, Camera } from "lucide-react"

const personalInfoSchema = z.object({
  name: z.string().min(1, "Name is required").max(100),
  bio: z.string().max(500).optional(),
  photoUrl: z.string().url().optional().or(z.literal("")),
  currentRole: z.string().max(100).optional(),
  company: z.string().max(100).optional(),
  industry: z.string().max(50).optional(),
  province: z.string().max(50).optional(),
  city: z.string().max(50).optional(),
  country: z.string().max(50).optional(),
})

type PersonalInfoFormData = z.infer<typeof personalInfoSchema>

interface PersonalInfoStepProps {
  alumni?: {
    name?: string
    bio?: string
    photoUrl?: string
    currentRole?: string
    company?: string
    industry?: string
    province?: string
    city?: string
    country?: string
  }
  onNext: () => void
  onPrevious: () => void
}

export function PersonalInfoStep({ alumni, onNext, onPrevious }: PersonalInfoStepProps) {
  const [isUploading, setIsUploading] = useState(false)

  const form = useForm<PersonalInfoFormData>({
    resolver: zodResolver(personalInfoSchema),
    defaultValues: {
      name: alumni?.name || "",
      bio: alumni?.bio || "",
      photoUrl: alumni?.photoUrl || "",
      currentRole: alumni?.currentRole || "",
      company: alumni?.company || "",
      industry: alumni?.industry || "",
      province: alumni?.province || "",
      city: alumni?.city || "",
      country: alumni?.country || "South Africa",
    },
  })

  const updatePersonalInfoMutation = api.onboarding.updatePersonalInfo.useMutation({
    onSuccess: () => {
      toast.success("Personal information saved!")
      onNext()
    },
    onError: (error) => {
      toast.error(error.message || "Failed to save personal information")
    },
  })

  const onSubmit = async (data: PersonalInfoFormData) => {
    await updatePersonalInfoMutation.mutateAsync(data)
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const handlePhotoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    setIsUploading(true)
    try {
      // In a real implementation, you would upload to a service like Cloudinary or AWS S3
      // For now, we'll just show a placeholder
      toast.info("Photo upload feature coming soon!")
      
      // Placeholder for actual upload logic:
      // const formData = new FormData()
      // formData.append('file', file)
      // const response = await fetch('/api/upload', { method: 'POST', body: formData })
      // const { url } = await response.json()
      // form.setValue('photoUrl', url)
      
    } catch (error) {
      toast.error("Failed to upload photo")
    } finally {
      setIsUploading(false)
    }
  }

  const currentPhotoUrl = form.watch('photoUrl')

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <div className="w-12 h-12 mx-auto bg-primary/10 rounded-full flex items-center justify-center">
          <User className="h-6 w-6 text-primary" />
        </div>
        <h2 className="text-2xl font-bold text-foreground">Tell us about yourself</h2>
        <p className="text-muted-foreground">
          Help other alumni get to know you better
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Profile Photo */}
          <div className="flex flex-col items-center space-y-4">
            <div className="relative">
              <Avatar className="h-24 w-24">
                <AvatarImage src={currentPhotoUrl || ""} alt="Profile photo" />
                <AvatarFallback className="bg-primary text-primary-foreground text-xl">
                  {form.watch('name') ? getInitials(form.watch('name')) : 'YN'}
                </AvatarFallback>
              </Avatar>
              
              <label
                htmlFor="photo-upload"
                className="absolute bottom-0 right-0 bg-primary text-primary-foreground rounded-full p-2 cursor-pointer hover:bg-primary/90 transition-colors"
              >
                <Camera className="h-4 w-4" />
                <input
                  id="photo-upload"
                  type="file"
                  accept="image/*"
                  onChange={handlePhotoUpload}
                  className="hidden"
                  disabled={isUploading}
                />
              </label>
            </div>
            
            <div className="text-center">
              <p className="text-sm text-muted-foreground">
                {isUploading ? "Uploading..." : "Click the camera icon to upload a photo"}
              </p>
            </div>
          </div>

          {/* Basic Information */}
          <div className="grid gap-4 md:grid-cols-2">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem className="md:col-span-2">
                  <FormLabel>Full Name *</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="Enter your full name" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="currentRole"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Current Role</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="e.g. Software Engineer" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="company"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Company</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="e.g. Google" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="industry"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Industry</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="e.g. Technology" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="city"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>City</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="e.g. Cape Town" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="province"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Province</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="e.g. Western Cape" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="country"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Country</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="e.g. South Africa" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Bio */}
          <FormField
            control={form.control}
            name="bio"
            render={({ field }) => (
              <FormItem>
                <FormLabel>About Me</FormLabel>
                <FormControl>
                  <Textarea 
                    {...field} 
                    placeholder="Tell us about yourself, your interests, and what you're passionate about..."
                    rows={4}
                  />
                </FormControl>
                <FormDescription>
                  Share a brief introduction about yourself (optional)
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Navigation Buttons */}
          <div className="flex justify-between pt-6">
            <Button
              type="button"
              variant="outline"
              onClick={onPrevious}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>

            <Button
              type="submit"
              disabled={updatePersonalInfoMutation.isPending}
              className="bg-primary hover:bg-primary/90"
            >
              {updatePersonalInfoMutation.isPending ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Saving...
                </>
              ) : (
                <>
                  Continue
                  <ArrowRight className="ml-2 h-4 w-4" />
                </>
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  )
}
