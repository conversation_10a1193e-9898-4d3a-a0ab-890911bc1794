"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { 
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form"
import { api } from "@/components/providers/trpc-provider"
import { toast } from "sonner"
import { ArrowLeft, ArrowRight, Briefcase, Plus, X } from "lucide-react"

const professionalInfoSchema = z.object({
  skills: z.array(z.string()).default([]),
  interests: z.array(z.string()).default([]),
})

type ProfessionalInfoFormData = z.infer<typeof professionalInfoSchema>

interface ProfessionalInfoStepProps {
  alumni?: {
    skills?: string[]
    interests?: string[]
  }
  onNext: () => void
  onPrevious: () => void
}

const SUGGESTED_SKILLS = [
  "JavaScript", "Python", "Java", "C++", "React", "Node.js", "SQL", "AWS",
  "Project Management", "Data Analysis", "Machine Learning", "DevOps",
  "Leadership", "Communication", "Problem Solving", "Team Management",
  "Agile", "Scrum", "Git", "Docker", "Kubernetes", "MongoDB", "PostgreSQL",
  "Excel", "PowerBI", "Tableau", "Photoshop", "Figma", "AutoCAD",
  "MATLAB", "R", "TensorFlow", "PyTorch", "Blockchain", "Cybersecurity"
]

const SUGGESTED_INTERESTS = [
  "Artificial Intelligence", "Sustainability", "Renewable Energy", "Fintech",
  "Healthcare Technology", "Education", "Social Impact", "Entrepreneurship",
  "Innovation", "Research", "Mentoring", "Public Speaking", "Writing",
  "Photography", "Travel", "Sports", "Music", "Reading", "Cooking",
  "Volunteering", "Community Service", "Environmental Conservation",
  "Diversity & Inclusion", "Women in Tech", "Youth Development"
]

export function ProfessionalInfoStep({ alumni, onNext, onPrevious }: ProfessionalInfoStepProps) {
  const [skills, setSkills] = useState<string[]>(alumni?.skills || [])
  const [interests, setInterests] = useState<string[]>(alumni?.interests || [])
  const [newSkill, setNewSkill] = useState("")
  const [newInterest, setNewInterest] = useState("")

  const form = useForm<ProfessionalInfoFormData>({
    resolver: zodResolver(professionalInfoSchema),
    defaultValues: {
      skills: alumni?.skills || [],
      interests: alumni?.interests || [],
    },
  })

  const updateProfessionalInfoMutation = api.onboarding.updateProfessionalInfo.useMutation({
    onSuccess: () => {
      toast.success("Professional information saved!")
      onNext()
    },
    onError: (error) => {
      toast.error(error.message || "Failed to save professional information")
    },
  })

  const onSubmit = async (data: ProfessionalInfoFormData) => {
    await updateProfessionalInfoMutation.mutateAsync({
      skills,
      interests,
    })
  }

  const addSkill = (skill: string) => {
    if (skill.trim() && !skills.includes(skill.trim())) {
      const newSkills = [...skills, skill.trim()]
      setSkills(newSkills)
      form.setValue('skills', newSkills)
      setNewSkill("")
    }
  }

  const removeSkill = (skillToRemove: string) => {
    const newSkills = skills.filter(skill => skill !== skillToRemove)
    setSkills(newSkills)
    form.setValue('skills', newSkills)
  }

  const addInterest = (interest: string) => {
    if (interest.trim() && !interests.includes(interest.trim())) {
      const newInterests = [...interests, interest.trim()]
      setInterests(newInterests)
      form.setValue('interests', newInterests)
      setNewInterest("")
    }
  }

  const removeInterest = (interestToRemove: string) => {
    const newInterests = interests.filter(interest => interest !== interestToRemove)
    setInterests(newInterests)
    form.setValue('interests', newInterests)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <div className="w-12 h-12 mx-auto bg-primary/10 rounded-full flex items-center justify-center">
          <Briefcase className="h-6 w-6 text-primary" />
        </div>
        <h2 className="text-2xl font-bold text-foreground">Professional Information</h2>
        <p className="text-muted-foreground">
          Share your skills and interests to connect with like-minded alumni
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Skills Section */}
          <div className="space-y-4">
            <div>
              <Label className="text-base font-medium">Skills & Expertise</Label>
              <p className="text-sm text-muted-foreground mt-1">
                Add skills that represent your professional expertise
              </p>
            </div>

            {/* Add Custom Skill */}
            <div className="flex gap-2">
              <Input
                value={newSkill}
                onChange={(e) => setNewSkill(e.target.value)}
                placeholder="Add a skill (e.g., Python, Project Management)"
                onKeyPress={(e) => e.key === "Enter" && (e.preventDefault(), addSkill(newSkill))}
              />
              <Button 
                type="button" 
                onClick={() => addSkill(newSkill)}
                disabled={!newSkill.trim()}
                size="sm"
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>

            {/* Selected Skills */}
            {skills.length > 0 && (
              <div className="space-y-2">
                <Label className="text-sm">Your Skills</Label>
                <div className="flex flex-wrap gap-2">
                  {skills.map((skill) => (
                    <Badge
                      key={skill}
                      variant="secondary"
                      className="cursor-pointer hover:bg-destructive hover:text-destructive-foreground"
                      onClick={() => removeSkill(skill)}
                    >
                      {skill}
                      <X className="ml-1 h-3 w-3" />
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Suggested Skills */}
            <div className="space-y-2">
              <Label className="text-sm">Suggested Skills</Label>
              <div className="flex flex-wrap gap-2 max-h-32 overflow-y-auto">
                {SUGGESTED_SKILLS.filter(skill => !skills.includes(skill)).map((skill) => (
                  <Badge
                    key={skill}
                    variant="outline"
                    className="cursor-pointer hover:bg-primary hover:text-primary-foreground"
                    onClick={() => addSkill(skill)}
                  >
                    {skill}
                    <Plus className="ml-1 h-3 w-3" />
                  </Badge>
                ))}
              </div>
            </div>
          </div>

          {/* Interests Section */}
          <div className="space-y-4">
            <div>
              <Label className="text-base font-medium">Interests & Passions</Label>
              <p className="text-sm text-muted-foreground mt-1">
                Share what you're passionate about beyond work
              </p>
            </div>

            {/* Add Custom Interest */}
            <div className="flex gap-2">
              <Input
                value={newInterest}
                onChange={(e) => setNewInterest(e.target.value)}
                placeholder="Add an interest (e.g., Sustainability, Mentoring)"
                onKeyPress={(e) => e.key === "Enter" && (e.preventDefault(), addInterest(newInterest))}
              />
              <Button 
                type="button" 
                onClick={() => addInterest(newInterest)}
                disabled={!newInterest.trim()}
                size="sm"
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>

            {/* Selected Interests */}
            {interests.length > 0 && (
              <div className="space-y-2">
                <Label className="text-sm">Your Interests</Label>
                <div className="flex flex-wrap gap-2">
                  {interests.map((interest) => (
                    <Badge
                      key={interest}
                      variant="outline"
                      className="cursor-pointer hover:bg-destructive hover:text-destructive-foreground"
                      onClick={() => removeInterest(interest)}
                    >
                      {interest}
                      <X className="ml-1 h-3 w-3" />
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Suggested Interests */}
            <div className="space-y-2">
              <Label className="text-sm">Suggested Interests</Label>
              <div className="flex flex-wrap gap-2 max-h-32 overflow-y-auto">
                {SUGGESTED_INTERESTS.filter(interest => !interests.includes(interest)).map((interest) => (
                  <Badge
                    key={interest}
                    variant="outline"
                    className="cursor-pointer hover:bg-primary hover:text-primary-foreground"
                    onClick={() => addInterest(interest)}
                  >
                    {interest}
                    <Plus className="ml-1 h-3 w-3" />
                  </Badge>
                ))}
              </div>
            </div>
          </div>

          {/* Benefits */}
          <div className="bg-muted/50 rounded-lg p-4 space-y-3">
            <h3 className="font-medium text-foreground">Why add skills and interests?</h3>
            <div className="space-y-2 text-sm text-muted-foreground">
              <div className="flex items-start space-x-2">
                <div className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                <span>Get matched with alumni who share similar interests</span>
              </div>
              <div className="flex items-start space-x-2">
                <div className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                <span>Receive relevant job opportunities and collaborations</span>
              </div>
              <div className="flex items-start space-x-2">
                <div className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                <span>Join specialized groups and discussions</span>
              </div>
            </div>
          </div>

          {/* Navigation Buttons */}
          <div className="flex justify-between pt-6">
            <Button
              type="button"
              variant="outline"
              onClick={onPrevious}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>

            <Button
              type="submit"
              disabled={updateProfessionalInfoMutation.isPending}
              className="bg-primary hover:bg-primary/90"
            >
              {updateProfessionalInfoMutation.isPending ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Saving...
                </>
              ) : (
                <>
                  Continue
                  <ArrowRight className="ml-2 h-4 w-4" />
                </>
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  )
}
