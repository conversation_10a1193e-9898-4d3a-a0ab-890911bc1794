"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { 
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { api } from "@/components/providers/trpc-provider"
import { toast } from "sonner"
import { ArrowLeft, ArrowRight, GraduationCap, X } from "lucide-re``ct"

const protecBackgroundSchema = z.object({
  graduationYear: z.number().int().min(1982).max(new Date().getFullYear()),
  programmes: z.array(z.string()).min(1, "Please select at least one programme"),
})

type ProtecBackgroundFormData = z.infer<typeof protecBackgroundSchema>

interface ProtecBackgroundStepProps {
  alumni?: {
    graduationYear?: number
    programmes?: string[]
  }
  onNext: () => void
  onPrevious: () => void
}

const PROTEC_PROGRAMMES = [
  "Engineering",
  "Science",
  "Mathematics",
  "Technology",
  "Computer Science",
  "Information Technology",
  "Electrical Engineering",
  "Mechanical Engineering",
  "Civil Engineering",
  "Chemical Engineering",
  "Industrial Engineering",
  "Mining Engineering",
  "Metallurgical Engineering",
  "Agricultural Engineering",
  "Environmental Engineering",
  "Biomedical Engineering",
  "Aerospace Engineering",
  "Physics",
  "Chemistry",
  "Biology",
  "Mathematics",
  "Statistics",
  "Actuarial Science",
  "Other",
]

export function ProtecBackgroundStep({ alumni, onNext, onPrevious }: ProtecBackgroundStepProps) {
  const [selectedProgrammes, setSelectedProgrammes] = useState<string[]>(
    alumni?.programmes || []
  )

  const form = useForm<ProtecBackgroundFormData>({
    resolver: zodResolver(protecBackgroundSchema),
    defaultValues: {
      graduationYear: alumni?.graduationYear || new Date().getFullYear(),
      programmes: alumni?.programmes || [],
    },
  })

  const updateProtecBackgroundMutation = api.onboarding.updateProtecBackground.useMutation({
    onSuccess: () => {
      toast.success("PROTEC background saved!")
      onNext()
    },
    onError: (error) => {
      toast.error(error.message || "Failed to save PROTEC background")
    },
  })

  const onSubmit = async (data: ProtecBackgroundFormData) => {
    await updateProtecBackgroundMutation.mutateAsync({
      ...data,
      programmes: selectedProgrammes,
    })
  }

  const handleProgrammeToggle = (programme: string) => {
    setSelectedProgrammes(prev => {
      const newProgrammes = prev.includes(programme)
        ? prev.filter(p => p !== programme)
        : [...prev, programme]
      
      form.setValue('programmes', newProgrammes)
      return newProgrammes
    })
  }

  const removeProgramme = (programme: string) => {
    setSelectedProgrammes(prev => {
      const newProgrammes = prev.filter(p => p !== programme)
      form.setValue('programmes', newProgrammes)
      return newProgrammes
    })
  }

  // Generate year options
  const currentYear = new Date().getFullYear()
  const yearOptions = Array.from({ length: currentYear - 1982 + 1 }, (_, i) => currentYear - i)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <div className="w-12 h-12 mx-auto bg-primary/10 rounded-full flex items-center justify-center">
          <GraduationCap className="h-6 w-6 text-primary" />
        </div>
        <h2 className="text-2xl font-bold text-foreground">Your PROTEC Journey</h2>
        <p className="text-muted-foreground">
          Tell us about your experience with PROTEC programmes
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Graduation Year */}
          <FormField
            control={form.control}
            name="graduationYear"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Graduation Year *</FormLabel>
                <Select
                  value={field.value?.toString()}
                  onValueChange={(value) => field.onChange(parseInt(value))}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select your graduation year" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {yearOptions.map((year) => (
                      <SelectItem key={year} value={year.toString()}>
                        {year}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Selected Programmes */}
          {selectedProgrammes.length > 0 && (
            <div className="space-y-3">
              <Label>Selected Programmes</Label>
              <div className="flex flex-wrap gap-2">
                {selectedProgrammes.map((programme) => (
                  <Badge
                    key={programme}
                    variant="secondary"
                    className="cursor-pointer hover:bg-destructive hover:text-destructive-foreground"
                    onClick={() => removeProgramme(programme)}
                  >
                    {programme}
                    <X className="ml-1 h-3 w-3" />
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Programme Selection */}
          <div className="space-y-3">
            <Label>PROTEC Programmes *</Label>
            <p className="text-sm text-muted-foreground">
              Select all programmes you participated in during your time with PROTEC
            </p>
            
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3 max-h-64 overflow-y-auto border rounded-lg p-4">
              {PROTEC_PROGRAMMES.map((programme) => (
                <div key={programme} className="flex items-center space-x-2">
                  <Checkbox
                    id={programme}
                    checked={selectedProgrammes.includes(programme)}
                    onCheckedChange={() => handleProgrammeToggle(programme)}
                  />
                  <Label
                    htmlFor={programme}
                    className="text-sm cursor-pointer"
                  >
                    {programme}
                  </Label>
                </div>
              ))}
            </div>
            
            {selectedProgrammes.length === 0 && (
              <p className="text-sm text-destructive">Please select at least one programme</p>
            )}
          </div>

          {/* Additional Information */}
          <div className="bg-muted/50 rounded-lg p-4 space-y-3">
            <h3 className="font-medium text-foreground">Why this matters</h3>
            <div className="space-y-2 text-sm text-muted-foreground">
              <div className="flex items-start space-x-2">
                <div className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                <span>Connect with alumni from your programme and graduation year</span>
              </div>
              <div className="flex items-start space-x-2">
                <div className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                <span>Receive relevant event invitations and opportunities</span>
              </div>
              <div className="flex items-start space-x-2">
                <div className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                <span>Help us understand the PROTEC community better</span>
              </div>
            </div>
          </div>

          {/* Navigation Buttons */}
          <div className="flex justify-between pt-6">
            <Button
              type="button"
              variant="outline"
              onClick={onPrevious}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>

            <Button
              type="submit"
              disabled={updateProtecBackgroundMutation.isPending || selectedProgrammes.length === 0}
              className="bg-primary hover:bg-primary/90"
            >
              {updateProtecBackgroundMutation.isPending ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Saving...
                </>
              ) : (
                <>
                  Continue
                  <ArrowRight className="ml-2 h-4 w-4" />
                </>
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  )
}
