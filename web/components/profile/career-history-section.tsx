"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Briefcase, 
  Edit3, 
  Plus, 
  Calendar,
  MapPin,
  Building
} from "lucide-react"

interface CareerHistorySectionProps {
  profile: {
    careerHistory: Array<{
      id: string
      company: string
      position: string
      industry?: string | null
      startDate: string
      endDate?: string | null
      isCurrent: boolean
      description?: string | null
      location?: string | null
    }>
  }
  isEditing: boolean
  onUpdate: () => void
}

export function CareerHistorySection({ profile, isEditing, onUpdate }: CareerHistorySectionProps) {
  const [isEditMode, setIsEditMode] = useState(isEditing)

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long' 
    })
  }

  const calculateDuration = (startDate: string, endDate?: string | null) => {
    const start = new Date(startDate)
    const end = endDate ? new Date(endDate) : new Date()
    
    const months = (end.getFullYear() - start.getFullYear()) * 12 + 
                   (end.getMonth() - start.getMonth())
    
    if (months < 12) {
      return `${months} month${months !== 1 ? 's' : ''}`
    }
    
    const years = Math.floor(months / 12)
    const remainingMonths = months % 12
    
    if (remainingMonths === 0) {
      return `${years} year${years !== 1 ? 's' : ''}`
    }
    
    return `${years} year${years !== 1 ? 's' : ''} ${remainingMonths} month${remainingMonths !== 1 ? 's' : ''}`
  }

  if (!isEditMode && !isEditing) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center">
              <Briefcase className="mr-2 h-5 w-5" />
              Career History
            </span>
            <Button variant="ghost" size="sm" onClick={() => setIsEditMode(true)}>
              <Edit3 className="h-4 w-4" />
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {profile.careerHistory.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Briefcase className="mx-auto h-12 w-12 mb-4 opacity-50" />
              <p className="text-lg font-medium mb-2">No career history added yet</p>
              <p className="text-sm mb-4">
                Share your professional journey to help alumni connect with you
              </p>
              <Button onClick={() => setIsEditMode(true)} className="bg-primary hover:bg-primary/90">
                <Plus className="mr-2 h-4 w-4" />
                Add Career Experience
              </Button>
            </div>
          ) : (
            <div className="space-y-6">
              {profile.careerHistory.map((career, index) => (
                <div key={career.id} className="relative">
                  {/* Timeline line */}
                  {index < profile.careerHistory.length - 1 && (
                    <div className="absolute left-6 top-12 w-0.5 h-16 bg-border"></div>
                  )}
                  
                  <div className="flex items-start space-x-4">
                    {/* Timeline dot */}
                    <div className={`w-3 h-3 rounded-full mt-2 ${
                      career.isCurrent ? 'bg-primary' : 'bg-muted-foreground'
                    }`}></div>
                    
                    <div className="flex-1 space-y-2">
                      <div className="flex items-start justify-between">
                        <div>
                          <h3 className="font-semibold text-lg">{career.position}</h3>
                          <div className="flex items-center space-x-2 text-muted-foreground">
                            <Building className="h-4 w-4" />
                            <span className="font-medium">{career.company}</span>
                            {career.industry && (
                              <>
                                <span>•</span>
                                <span>{career.industry}</span>
                              </>
                            )}
                          </div>
                        </div>
                        {career.isCurrent && (
                          <Badge className="bg-green-100 text-green-800">
                            Current
                          </Badge>
                        )}
                      </div>
                      
                      <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                        <div className="flex items-center">
                          <Calendar className="mr-1 h-3 w-3" />
                          <span>
                            {formatDate(career.startDate)} - {
                              career.isCurrent ? 'Present' : 
                              career.endDate ? formatDate(career.endDate) : 'Present'
                            }
                          </span>
                        </div>
                        <span>•</span>
                        <span>{calculateDuration(career.startDate, career.endDate)}</span>
                        {career.location && (
                          <>
                            <span>•</span>
                            <div className="flex items-center">
                              <MapPin className="mr-1 h-3 w-3" />
                              <span>{career.location}</span>
                            </div>
                          </>
                        )}
                      </div>
                      
                      {career.description && (
                        <p className="text-sm text-muted-foreground leading-relaxed">
                          {career.description}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span className="flex items-center">
            <Briefcase className="mr-2 h-5 w-5" />
            Edit Career History
          </span>
          <div className="flex space-x-2">
            <Button variant="outline" size="sm">
              <Plus className="mr-2 h-4 w-4" />
              Add Experience
            </Button>
            <Button variant="ghost" size="sm" onClick={() => setIsEditMode(false)}>
              Done
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-center py-8 text-muted-foreground">
          <Briefcase className="mx-auto h-12 w-12 mb-4 opacity-50" />
          <p className="text-lg font-medium mb-2">Career History Editor</p>
          <p className="text-sm mb-4">
            This section is under development. You can add and edit your career history here.
          </p>
          <Button onClick={() => setIsEditMode(false)} variant="outline">
            Back to View Mode
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
