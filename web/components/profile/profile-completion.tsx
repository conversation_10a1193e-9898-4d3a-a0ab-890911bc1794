"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { 
  CheckCircle, 
  AlertCircle, 
  User, 
  Briefcase, 
  GraduationCap, 
  Award,
  Target,
  ArrowRight
} from "lucide-react"

interface ProfileCompletionProps {
  completionStatus: {
    completionPercentage: number
    missingFields: {
      basicInfo: boolean
      skills: boolean
      interests: boolean
      careerHistory: boolean
      education: boolean
      protecInvolvement: boolean
    }
  }
  onSectionClick: (section: string) => void
}

export function ProfileCompletion({ completionStatus, onSectionClick }: ProfileCompletionProps) {
  const { completionPercentage, missingFields } = completionStatus

  const sections = [
    {
      key: "basic",
      title: "Basic Information",
      description: "Complete your profile with photo, bio, and current role",
      icon: User,
      missing: missingFields.basicInfo,
      weight: 30,
    },
    {
      key: "career",
      title: "Career History",
      description: "Add your work experience and professional journey",
      icon: Briefcase,
      missing: missingFields.careerHistory,
      weight: 25,
    },
    {
      key: "education",
      title: "Education Background",
      description: "Include your educational qualifications and achievements",
      icon: GraduationCap,
      missing: missingFields.education,
      weight: 20,
    },
    {
      key: "protec",
      title: "PROTEC Involvement",
      description: "Share your PROTEC programme participation and achievements",
      icon: Award,
      missing: missingFields.protecInvolvement,
      weight: 15,
    },
  ]

  const missingSections = sections.filter(section => section.missing)
  const completedSections = sections.filter(section => !section.missing)

  const getProgressColor = (percentage: number) => {
    if (percentage >= 80) return "bg-green-500"
    if (percentage >= 60) return "bg-yellow-500"
    return "bg-red-500"
  }

  const getProgressMessage = (percentage: number) => {
    if (percentage >= 90) return "Excellent! Your profile is almost complete."
    if (percentage >= 70) return "Great progress! Just a few more sections to go."
    if (percentage >= 50) return "Good start! Keep adding more information."
    return "Let's build your profile to connect with more alumni."
  }

  return (
    <Card className="border-l-4 border-l-primary bg-gradient-to-r from-primary/5 to-transparent">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Target className="h-5 w-5 text-primary" />
            <span>Profile Completion</span>
            <Badge variant="secondary" className="ml-2">
              {completionPercentage}%
            </Badge>
          </div>
          {completionPercentage >= 80 && (
            <Badge className="bg-green-100 text-green-800">
              <CheckCircle className="mr-1 h-3 w-3" />
              Well Done!
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Progress Bar */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Progress</span>
            <span className="font-medium">{completionPercentage}% Complete</span>
          </div>
          <Progress 
            value={completionPercentage} 
            className="h-3"
          />
          <p className="text-sm text-muted-foreground">
            {getProgressMessage(completionPercentage)}
          </p>
        </div>

        {/* Missing Sections */}
        {missingSections.length > 0 && (
          <div className="space-y-3">
            <h4 className="font-medium text-sm flex items-center">
              <AlertCircle className="mr-2 h-4 w-4 text-amber-500" />
              Complete these sections to improve your profile
            </h4>
            <div className="grid gap-3 md:grid-cols-2">
              {missingSections.map((section) => {
                const Icon = section.icon
                return (
                  <div
                    key={section.key}
                    className="flex items-center justify-between p-3 bg-muted/50 rounded-lg border border-dashed border-muted-foreground/20 hover:bg-muted/70 transition-colors cursor-pointer"
                    onClick={() => onSectionClick(section.key)}
                  >
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-background rounded-md">
                        <Icon className="h-4 w-4 text-muted-foreground" />
                      </div>
                      <div>
                        <div className="font-medium text-sm">{section.title}</div>
                        <div className="text-xs text-muted-foreground">
                          {section.description}
                        </div>
                      </div>
                    </div>
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                      <ArrowRight className="h-4 w-4" />
                    </Button>
                  </div>
                )
              })}
            </div>
          </div>
        )}

        {/* Completed Sections */}
        {completedSections.length > 0 && (
          <div className="space-y-3">
            <h4 className="font-medium text-sm flex items-center">
              <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
              Completed sections
            </h4>
            <div className="flex flex-wrap gap-2">
              {completedSections.map((section) => {
                const Icon = section.icon
                return (
                  <Badge
                    key={section.key}
                    variant="secondary"
                    className="bg-green-100 text-green-800 hover:bg-green-200 cursor-pointer"
                    onClick={() => onSectionClick(section.key)}
                  >
                    <Icon className="mr-1 h-3 w-3" />
                    {section.title}
                    <CheckCircle className="ml-1 h-3 w-3" />
                  </Badge>
                )
              })}
            </div>
          </div>
        )}

        {/* Call to Action */}
        {completionPercentage < 100 && (
          <div className="flex items-center justify-between p-4 bg-primary/10 rounded-lg border border-primary/20">
            <div>
              <div className="font-medium text-sm">
                {missingSections.length > 0 
                  ? `Complete ${missingSections.length} more section${missingSections.length > 1 ? 's' : ''}`
                  : "Add more details to reach 100%"
                }
              </div>
              <div className="text-xs text-muted-foreground">
                A complete profile helps you connect with more alumni
              </div>
            </div>
            {missingSections.length > 0 && (
              <Button 
                size="sm"
                onClick={() => onSectionClick(missingSections[0].key)}
                className="bg-primary hover:bg-primary/90"
              >
                Continue
                <ArrowRight className="ml-1 h-3 w-3" />
              </Button>
            )}
          </div>
        )}

        {/* Achievement Badge */}
        {completionPercentage === 100 && (
          <div className="text-center p-6 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-200">
            <div className="w-16 h-16 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
            <h3 className="font-semibold text-green-800 mb-2">Profile Complete!</h3>
            <p className="text-sm text-green-700">
              Congratulations! Your profile is now complete and ready to help you connect with the PROTEC alumni community.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
