"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  GraduationCap, 
  Edit3, 
  Plus, 
  Calendar,
  Award
} from "lucide-react"

interface EducationSectionProps {
  profile: {
    education: Array<{
      id: string
      institution: string
      degree: string
      fieldOfStudy?: string | null
      startDate: string
      endDate?: string | null
      isCurrent: boolean
      grade?: string | null
      description?: string | null
    }>
  }
  isEditing: boolean
  onUpdate: () => void
}

export function EducationSection({ profile, isEditing, onUpdate }: EducationSectionProps) {
  const [isEditMode, setIsEditMode] = useState(isEditing)

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long' 
    })
  }

  if (!isEditMode && !isEditing) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center">
              <GraduationCap className="mr-2 h-5 w-5" />
              Education
            </span>
            <Button variant="ghost" size="sm" onClick={() => setIsEditMode(true)}>
              <Edit3 className="h-4 w-4" />
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {profile.education.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <GraduationCap className="mx-auto h-12 w-12 mb-4 opacity-50" />
              <p className="text-lg font-medium mb-2">No education history added yet</p>
              <p className="text-sm mb-4">
                Add your educational background to showcase your academic journey
              </p>
              <Button onClick={() => setIsEditMode(true)} className="bg-primary hover:bg-primary/90">
                <Plus className="mr-2 h-4 w-4" />
                Add Education
              </Button>
            </div>
          ) : (
            <div className="space-y-6">
              {profile.education.map((education, index) => (
                <div key={education.id} className="relative">
                  {/* Timeline line */}
                  {index < profile.education.length - 1 && (
                    <div className="absolute left-6 top-12 w-0.5 h-16 bg-border"></div>
                  )}
                  
                  <div className="flex items-start space-x-4">
                    {/* Timeline dot */}
                    <div className={`w-3 h-3 rounded-full mt-2 ${
                      education.isCurrent ? 'bg-primary' : 'bg-muted-foreground'
                    }`}></div>
                    
                    <div className="flex-1 space-y-2">
                      <div className="flex items-start justify-between">
                        <div>
                          <h3 className="font-semibold text-lg">{education.degree}</h3>
                          {education.fieldOfStudy && (
                            <p className="text-muted-foreground">{education.fieldOfStudy}</p>
                          )}
                          <p className="font-medium text-primary">{education.institution}</p>
                        </div>
                        <div className="flex flex-col items-end space-y-1">
                          {education.isCurrent && (
                            <Badge className="bg-green-100 text-green-800">
                              Current
                            </Badge>
                          )}
                          {education.grade && (
                            <Badge variant="outline" className="bg-blue-50 text-blue-700">
                              <Award className="mr-1 h-3 w-3" />
                              {education.grade}
                            </Badge>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                        <Calendar className="h-3 w-3" />
                        <span>
                          {formatDate(education.startDate)} - {
                            education.isCurrent ? 'Present' : 
                            education.endDate ? formatDate(education.endDate) : 'Present'
                          }
                        </span>
                      </div>
                      
                      {education.description && (
                        <p className="text-sm text-muted-foreground leading-relaxed">
                          {education.description}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span className="flex items-center">
            <GraduationCap className="mr-2 h-5 w-5" />
            Edit Education
          </span>
          <div className="flex space-x-2">
            <Button variant="outline" size="sm">
              <Plus className="mr-2 h-4 w-4" />
              Add Education
            </Button>
            <Button variant="ghost" size="sm" onClick={() => setIsEditMode(false)}>
              Done
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-center py-8 text-muted-foreground">
          <GraduationCap className="mx-auto h-12 w-12 mb-4 opacity-50" />
          <p className="text-lg font-medium mb-2">Education Editor</p>
          <p className="text-sm mb-4">
            This section is under development. You can add and edit your education history here.
          </p>
          <Button onClick={() => setIsEditMode(false)} variant="outline">
            Back to View Mode
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
