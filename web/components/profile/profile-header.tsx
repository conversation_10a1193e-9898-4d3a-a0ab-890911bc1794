"use client"

import { useState } from "react"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { 
  Edit3, 
  MapPin, 
  Briefcase, 
  Calendar, 
  ExternalLink,
  Linkedin,
  Twitter,
  Github,
  Globe
} from "lucide-react"

interface ProfileHeaderProps {
  profile: {
    id: string
    name: string
    email: string
    photoUrl?: string | null
    bio?: string | null
    currentRole?: string | null
    company?: string | null
    industry?: string | null
    graduationYear: number
    programmes: string[]
    skills: string[]
    interests: string[]
    province?: string | null
    city?: string | null
    country?: string | null
    socialLinks?: {
      linkedin?: string
      twitter?: string
      github?: string
      website?: string
    } | null
    _count: {
      connections: number
      posts: number
      donations: number
      organizedEvents: number
    }
  }
  isEditing: boolean
  onEditToggle: () => void
}

export function ProfileHeader({ profile, isEditing, onEditToggle }: ProfileHeaderProps) {
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const getSocialIcon = (platform: string) => {
    switch (platform) {
      case 'linkedin':
        return <Linkedin className="h-4 w-4" />
      case 'twitter':
        return <Twitter className="h-4 w-4" />
      case 'github':
        return <Github className="h-4 w-4" />
      case 'website':
        return <Globe className="h-4 w-4" />
      default:
        return <ExternalLink className="h-4 w-4" />
    }
  }

  const socialLinks = profile.socialLinks || {}

  return (
    <Card className="overflow-hidden">
      <div className="h-32 bg-gradient-to-r from-primary/20 to-primary/10"></div>
      <CardContent className="relative p-6">
        {/* Profile Picture */}
        <div className="absolute -top-16 left-6">
          <Avatar className="h-32 w-32 border-4 border-background shadow-lg">
            <AvatarImage src={profile.photoUrl || ""} alt={profile.name} />
            <AvatarFallback className="bg-primary text-primary-foreground text-2xl">
              {getInitials(profile.name)}
            </AvatarFallback>
          </Avatar>
        </div>

        {/* Edit Button */}
        <div className="flex justify-end mb-4">
          <Button 
            variant={isEditing ? "default" : "outline"} 
            onClick={onEditToggle}
            className="shadow-sm"
          >
            <Edit3 className="mr-2 h-4 w-4" />
            {isEditing ? "View Mode" : "Edit Profile"}
          </Button>
        </div>

        {/* Profile Info */}
        <div className="ml-40 space-y-4">
          {/* Name and Title */}
          <div>
            <h1 className="text-3xl font-bold text-foreground">{profile.name}</h1>
            {profile.currentRole && (
              <div className="flex items-center mt-2 text-lg text-muted-foreground">
                <Briefcase className="mr-2 h-5 w-5" />
                <span>{profile.currentRole}</span>
                {profile.company && (
                  <>
                    <span className="mx-2">at</span>
                    <span className="font-medium">{profile.company}</span>
                  </>
                )}
              </div>
            )}
          </div>

          {/* Location and Graduation */}
          <div className="flex flex-wrap items-center gap-4 text-muted-foreground">
            {(profile.city || profile.province) && (
              <div className="flex items-center">
                <MapPin className="mr-1 h-4 w-4" />
                <span>
                  {profile.city}
                  {profile.city && profile.province && ", "}
                  {profile.province}
                  {profile.country && profile.country !== "South Africa" && `, ${profile.country}`}
                </span>
              </div>
            )}
            <div className="flex items-center">
              <Calendar className="mr-1 h-4 w-4" />
              <span>Class of {profile.graduationYear}</span>
            </div>
          </div>

          {/* Bio */}
          {profile.bio && (
            <p className="text-muted-foreground leading-relaxed max-w-3xl">
              {profile.bio}
            </p>
          )}

          {/* Programmes */}
          {profile.programmes.length > 0 && (
            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-2">PROTEC Programmes</h3>
              <div className="flex flex-wrap gap-2">
                {profile.programmes.map((programme) => (
                  <Badge key={programme} variant="secondary" className="bg-primary/10 text-primary">
                    {programme}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Skills */}
          {profile.skills.length > 0 && (
            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-2">Skills</h3>
              <div className="flex flex-wrap gap-2">
                {profile.skills.slice(0, 8).map((skill) => (
                  <Badge key={skill} variant="outline" className="text-xs">
                    {skill}
                  </Badge>
                ))}
                {profile.skills.length > 8 && (
                  <Badge variant="outline" className="text-xs">
                    +{profile.skills.length - 8} more
                  </Badge>
                )}
              </div>
            </div>
          )}

          {/* Interests */}
          {profile.interests.length > 0 && (
            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-2">Interests</h3>
              <div className="flex flex-wrap gap-2">
                {profile.interests.slice(0, 6).map((interest) => (
                  <Badge key={interest} variant="outline" className="text-xs bg-muted/50">
                    {interest}
                  </Badge>
                ))}
                {profile.interests.length > 6 && (
                  <Badge variant="outline" className="text-xs bg-muted/50">
                    +{profile.interests.length - 6} more
                  </Badge>
                )}
              </div>
            </div>
          )}

          {/* Social Links */}
          {Object.keys(socialLinks).length > 0 && (
            <div className="flex items-center space-x-3">
              {Object.entries(socialLinks).map(([platform, url]) => {
                if (!url) return null
                return (
                  <Button
                    key={platform}
                    variant="ghost"
                    size="sm"
                    asChild
                    className="h-8 w-8 p-0"
                  >
                    <a
                      href={url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-muted-foreground hover:text-primary"
                    >
                      {getSocialIcon(platform)}
                      <span className="sr-only">{platform}</span>
                    </a>
                  </Button>
                )
              })}
            </div>
          )}

          {/* Stats */}
          <div className="flex items-center space-x-6 pt-4 border-t">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">{profile._count.connections}</div>
              <div className="text-xs text-muted-foreground">Connections</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">{profile._count.posts}</div>
              <div className="text-xs text-muted-foreground">Posts</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">{profile._count.organizedEvents}</div>
              <div className="text-xs text-muted-foreground">Events</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">{profile._count.donations}</div>
              <div className="text-xs text-muted-foreground">Donations</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
