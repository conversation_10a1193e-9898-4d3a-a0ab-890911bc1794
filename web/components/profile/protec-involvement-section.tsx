"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Award, 
  Edit3, 
  Plus, 
  Calendar,
  Star,
  Trophy
} from "lucide-react"

interface ProtecInvolvementSectionProps {
  profile: {
    protecInvolvement: Array<{
      id: string
      programme: string
      year: number
      role?: string | null
      description?: string | null
      achievements: string[]
    }>
  }
  isEditing: boolean
  onUpdate: () => void
}

export function ProtecInvolvementSection({ profile, isEditing, onUpdate }: ProtecInvolvementSectionProps) {
  const [isEditMode, setIsEditMode] = useState(isEditing)

  if (!isEditMode && !isEditing) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center">
              <Award className="mr-2 h-5 w-5" />
              PROTEC Involvement
            </span>
            <Button variant="ghost" size="sm" onClick={() => setIsEditMode(true)}>
              <Edit3 className="h-4 w-4" />
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {profile.protecInvolvement.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Award className="mx-auto h-12 w-12 mb-4 opacity-50" />
              <p className="text-lg font-medium mb-2">No PROTEC involvement added yet</p>
              <p className="text-sm mb-4">
                Share your PROTEC journey and achievements with the community
              </p>
              <Button onClick={() => setIsEditMode(true)} className="bg-primary hover:bg-primary/90">
                <Plus className="mr-2 h-4 w-4" />
                Add PROTEC Experience
              </Button>
            </div>
          ) : (
            <div className="space-y-6">
              {profile.protecInvolvement.map((involvement, index) => (
                <div key={involvement.id} className="relative">
                  {/* Timeline line */}
                  {index < profile.protecInvolvement.length - 1 && (
                    <div className="absolute left-6 top-12 w-0.5 h-16 bg-border"></div>
                  )}
                  
                  <div className="flex items-start space-x-4">
                    {/* Timeline dot */}
                    <div className="w-3 h-3 rounded-full mt-2 bg-primary"></div>
                    
                    <div className="flex-1 space-y-3">
                      <div className="flex items-start justify-between">
                        <div>
                          <h3 className="font-semibold text-lg">{involvement.programme}</h3>
                          {involvement.role && (
                            <p className="text-muted-foreground font-medium">{involvement.role}</p>
                          )}
                        </div>
                        <Badge variant="secondary" className="bg-primary/10 text-primary">
                          <Calendar className="mr-1 h-3 w-3" />
                          {involvement.year}
                        </Badge>
                      </div>
                      
                      {involvement.description && (
                        <p className="text-sm text-muted-foreground leading-relaxed">
                          {involvement.description}
                        </p>
                      )}
                      
                      {involvement.achievements.length > 0 && (
                        <div className="space-y-2">
                          <h4 className="text-sm font-medium flex items-center">
                            <Trophy className="mr-1 h-4 w-4 text-amber-500" />
                            Achievements
                          </h4>
                          <div className="space-y-1">
                            {involvement.achievements.map((achievement, achievementIndex) => (
                              <div key={achievementIndex} className="flex items-start space-x-2">
                                <Star className="h-3 w-3 text-amber-500 mt-1 flex-shrink-0" />
                                <span className="text-sm text-muted-foreground">{achievement}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span className="flex items-center">
            <Award className="mr-2 h-5 w-5" />
            Edit PROTEC Involvement
          </span>
          <div className="flex space-x-2">
            <Button variant="outline" size="sm">
              <Plus className="mr-2 h-4 w-4" />
              Add Experience
            </Button>
            <Button variant="ghost" size="sm" onClick={() => setIsEditMode(false)}>
              Done
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-center py-8 text-muted-foreground">
          <Award className="mx-auto h-12 w-12 mb-4 opacity-50" />
          <p className="text-lg font-medium mb-2">PROTEC Involvement Editor</p>
          <p className="text-sm mb-4">
            This section is under development. You can add and edit your PROTEC involvement here.
          </p>
          <Button onClick={() => setIsEditMode(false)} variant="outline">
            Back to View Mode
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
