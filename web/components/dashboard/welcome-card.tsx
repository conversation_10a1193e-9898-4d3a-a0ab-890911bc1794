"use client"

import { useSession } from "next-auth/react"
import Link from "next/link"
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON>Right, GraduationCap, MapPin, Briefcase } from "lucide-react"

export function WelcomeCard() {
  const { data: session } = useSession()

  // Mock data - in real app, this would come from tRPC
  const profileCompletion = 75
  const isProfileIncomplete = profileCompletion < 100

  return (
    <Card className="bg-gradient-to-br from-protec-navy to-protec-navy/80 text-white">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-xl">Welcome to PROTEC Alumni!</CardTitle>
            <p className="text-blue-100 mt-1">
              Connect, grow, and give back to the community
            </p>
          </div>
          <div className="text-right">
            <Badge variant="secondary" className="bg-white/20 text-white">
              Profile {profileCompletion}% complete
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {isProfileIncomplete && (
          <div className="bg-white/10 rounded-lg p-4">
            <h4 className="font-semibold mb-2">Complete Your Profile</h4>
            <p className="text-sm text-blue-100 mb-3">
              Add more details to help fellow alumni find and connect with you.
            </p>
            <div className="flex flex-wrap gap-2 mb-3">
              <Badge variant="outline" className="border-white/30 text-white">
                <GraduationCap className="w-3 h-3 mr-1" />
                Add graduation year
              </Badge>
              <Badge variant="outline" className="border-white/30 text-white">
                <MapPin className="w-3 h-3 mr-1" />
                Add location
              </Badge>
              <Badge variant="outline" className="border-white/30 text-white">
                <Briefcase className="w-3 h-3 mr-1" />
                Add current role
              </Badge>
            </div>
            <Button 
              variant="secondary" 
              size="sm" 
              className="bg-white text-protec-navy hover:bg-white/90"
              asChild
            >
              <Link href="/profile/edit">
                Complete Profile
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold">5,000+</div>
            <div className="text-sm text-blue-100">Alumni Network</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold">200+</div>
            <div className="text-sm text-blue-100">Annual Events</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold">R2M+</div>
            <div className="text-sm text-blue-100">Donated Back</div>
          </div>
        </div>

        <div className="flex flex-wrap gap-2">
          <Button 
            variant="secondary" 
            size="sm"
            className="bg-white/20 text-white hover:bg-white/30 border-white/30"
            asChild
          >
            <Link href="/directory">Explore Alumni</Link>
          </Button>
          <Button 
            variant="secondary" 
            size="sm"
            className="bg-white/20 text-white hover:bg-white/30 border-white/30"
            asChild
          >
            <Link href="/events">View Events</Link>
          </Button>
          <Button 
            variant="secondary" 
            size="sm"
            className="bg-white/20 text-white hover:bg-white/30 border-white/30"
            asChild
          >
            <Link href="/donations">Give Back</Link>
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
