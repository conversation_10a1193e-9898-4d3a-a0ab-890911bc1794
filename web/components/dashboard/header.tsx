"use client"

import { useSession } from "next-auth/react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Bell, Plus } from "lucide-react"

export function DashboardHeader() {
  const { data: session } = useSession()

  const getGreeting = () => {
    const hour = new Date().getHours()
    if (hour < 12) return "Good morning"
    if (hour < 17) return "Good afternoon"
    return "Good evening"
  }

  return (
    <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
      <div>
        <h1 className="text-3xl font-bold tracking-tight text-protec-navy">
          {getGreeting()}, {session?.user?.name?.split(' ')[0] || 'Alumni'}!
        </h1>
        <p className="text-muted-foreground">
          Welcome back to the PROTEC Alumni Platform
        </p>
      </div>

      <div className="flex items-center space-x-4">
        {/* Notifications */}
        <Button variant="outline" size="icon" className="relative">
          <Bell className="h-4 w-4" />
          <Badge 
            variant="destructive" 
            className="absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 text-xs"
          >
            3
          </Badge>
        </Button>

        {/* Quick Actions */}
        <Button className="bg-protec-red hover:bg-protec-red/90">
          <Plus className="mr-2 h-4 w-4" />
          New Post
        </Button>
      </div>
    </div>
  )
}
