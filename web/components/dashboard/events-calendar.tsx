"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Calendar, ChevronLeft, ChevronRight, MapPin, Clock, Users } from "lucide-react"

// Mock data - in real app, this would come from tRPC
const currentDate = new Date()
const currentMonth = currentDate.getMonth()
const currentYear = currentDate.getFullYear()

const events = [
  {
    id: "1",
    title: "Tech Career Fair",
    date: new Date(2024, 1, 15),
    time: "09:00",
    location: "Sandton Convention Centre",
    attendees: 156,
    category: "career",
    isUserAttending: true
  },
  {
    id: "2",
    title: "Alumni Mixer",
    date: new Date(2024, 1, 20),
    time: "18:00",
    location: "Cape Town",
    attendees: 89,
    category: "networking",
    isUserAttending: false
  },
  {
    id: "3",
    title: "AI Workshop",
    date: new Date(2024, 1, 25),
    time: "14:00",
    location: "Virtual",
    attendees: 234,
    category: "workshop",
    isUserAttending: true
  }
]

const categoryColors = {
  career: "bg-blue-100 text-blue-800",
  networking: "bg-green-100 text-green-800",
  workshop: "bg-purple-100 text-purple-800",
  social: "bg-orange-100 text-orange-800"
}

export function EventsCalendar() {
  const [selectedDate, setSelectedDate] = useState(currentDate)
  const [viewMonth, setViewMonth] = useState(currentMonth)
  const [viewYear, setViewYear] = useState(currentYear)

  const getDaysInMonth = (month: number, year: number) => {
    return new Date(year, month + 1, 0).getDate()
  }

  const getFirstDayOfMonth = (month: number, year: number) => {
    return new Date(year, month, 1).getDay()
  }

  const getEventsForDate = (date: Date) => {
    return events.filter(event => 
      event.date.getDate() === date.getDate() &&
      event.date.getMonth() === date.getMonth() &&
      event.date.getFullYear() === date.getFullYear()
    )
  }

  const navigateMonth = (direction: 'prev' | 'next') => {
    if (direction === 'prev') {
      if (viewMonth === 0) {
        setViewMonth(11)
        setViewYear(viewYear - 1)
      } else {
        setViewMonth(viewMonth - 1)
      }
    } else {
      if (viewMonth === 11) {
        setViewMonth(0)
        setViewYear(viewYear + 1)
      } else {
        setViewMonth(viewMonth + 1)
      }
    }
  }

  const monthNames = [
    "January", "February", "March", "April", "May", "June",
    "July", "August", "September", "October", "November", "December"
  ]

  const daysInMonth = getDaysInMonth(viewMonth, viewYear)
  const firstDay = getFirstDayOfMonth(viewMonth, viewYear)
  const days = Array.from({ length: daysInMonth }, (_, i) => i + 1)
  const emptyDays = Array.from({ length: firstDay }, (_, i) => i)

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <CardTitle className="text-lg font-semibold text-protec-navy flex items-center">
          <Calendar className="mr-2 h-5 w-5" />
          Events Calendar
        </CardTitle>
        <Button variant="ghost" size="sm" asChild>
          <Link href="/events">View All</Link>
        </Button>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Calendar Header */}
        <div className="flex items-center justify-between">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigateMonth('prev')}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <h3 className="font-semibold text-protec-navy">
            {monthNames[viewMonth]} {viewYear}
          </h3>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigateMonth('next')}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>

        {/* Calendar Grid */}
        <div className="grid grid-cols-7 gap-1 text-center">
          {/* Day Headers */}
          {['S', 'M', 'T', 'W', 'T', 'F', 'S'].map((day, index) => (
            <div key={index} className="text-xs font-medium text-muted-foreground p-2">
              {day}
            </div>
          ))}
          
          {/* Empty Days */}
          {emptyDays.map((_, index) => (
            <div key={`empty-${index}`} className="p-2" />
          ))}
          
          {/* Calendar Days */}
          {days.map((day) => {
            const date = new Date(viewYear, viewMonth, day)
            const dayEvents = getEventsForDate(date)
            const isToday = date.toDateString() === currentDate.toDateString()
            const hasEvents = dayEvents.length > 0
            
            return (
              <div
                key={day}
                className={`p-2 text-xs cursor-pointer rounded-md transition-colors ${
                  isToday 
                    ? 'bg-protec-red text-white' 
                    : hasEvents 
                    ? 'bg-blue-50 text-blue-900 hover:bg-blue-100' 
                    : 'hover:bg-gray-50'
                }`}
                onClick={() => setSelectedDate(date)}
              >
                <div className="font-medium">{day}</div>
                {hasEvents && (
                  <div className="flex justify-center mt-1">
                    <div className="w-1 h-1 bg-current rounded-full" />
                  </div>
                )}
              </div>
            )
          })}
        </div>

        {/* Upcoming Events */}
        <div className="space-y-3 pt-4 border-t">
          <h4 className="text-sm font-medium text-protec-navy">Upcoming Events</h4>
          <div className="space-y-2">
            {events.slice(0, 3).map((event) => (
              <div key={event.id} className="flex items-start space-x-3 p-2 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="text-center min-w-[40px]">
                  <div className="text-sm font-bold text-protec-navy">
                    {event.date.getDate()}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {monthNames[event.date.getMonth()].slice(0, 3)}
                  </div>
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-1">
                    <Link 
                      href={`/events/${event.id}`}
                      className="font-medium text-sm text-protec-navy hover:text-protec-red transition-colors truncate"
                    >
                      {event.title}
                    </Link>
                    <Badge 
                      variant="secondary" 
                      className={`text-xs ${categoryColors[event.category as keyof typeof categoryColors]}`}
                    >
                      {event.category}
                    </Badge>
                  </div>
                  
                  <div className="flex items-center space-x-3 text-xs text-muted-foreground">
                    <div className="flex items-center space-x-1">
                      <Clock className="h-3 w-3" />
                      <span>{event.time}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <MapPin className="h-3 w-3" />
                      <span className="truncate">{event.location}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Users className="h-3 w-3" />
                      <span>{event.attendees}</span>
                    </div>
                  </div>
                  
                  {event.isUserAttending && (
                    <Badge variant="outline" className="text-xs mt-1 bg-green-50 text-green-700 border-green-200">
                      Attending
                    </Badge>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Quick Stats */}
        <div className="pt-4 border-t">
          <div className="grid grid-cols-2 gap-4 text-center">
            <div>
              <div className="text-lg font-bold text-protec-navy">8</div>
              <p className="text-xs text-muted-foreground">Events This Month</p>
            </div>
            <div>
              <div className="text-lg font-bold text-protec-navy">3</div>
              <p className="text-xs text-muted-foreground">You're Attending</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
