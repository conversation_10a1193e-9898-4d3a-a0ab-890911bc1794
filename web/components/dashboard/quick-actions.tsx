"use client"

import Link from "next/link"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { 
  Plus, 
  Users, 
  Calendar, 
  Heart, 
  Search,
  MessageSquare,
  UserPlus,
  Gift
} from "lucide-react"

const quickActions = [
  {
    title: "Create Post",
    description: "Share an update with the community",
    icon: Plus,
    href: "/feed/new",
    color: "bg-blue-500 hover:bg-blue-600",
    textColor: "text-white"
  },
  {
    title: "Find Alumni",
    description: "Search and connect with graduates",
    icon: Search,
    href: "/directory",
    color: "bg-green-500 hover:bg-green-600",
    textColor: "text-white"
  },
  {
    title: "Create Event",
    description: "Organize a networking event",
    icon: Calendar,
    href: "/events/new",
    color: "bg-purple-500 hover:bg-purple-600",
    textColor: "text-white"
  },
  {
    title: "Make Donation",
    description: "Support PROTEC and students",
    icon: Heart,
    href: "/donations/new",
    color: "bg-protec-red hover:bg-protec-red/90",
    textColor: "text-white"
  },
  {
    title: "Join Discussion",
    description: "Participate in community talks",
    icon: MessageSquare,
    href: "/feed",
    color: "bg-orange-500 hover:bg-orange-600",
    textColor: "text-white"
  },
  {
    title: "Invite Alumni",
    description: "Invite colleagues to join",
    icon: UserPlus,
    href: "/invite",
    color: "bg-protec-navy hover:bg-protec-navy/90",
    textColor: "text-white"
  }
]

export function QuickActions() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-protec-navy">
          Quick Actions
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 gap-3">
          {quickActions.map((action) => {
            const Icon = action.icon
            return (
              <Button
                key={action.title}
                variant="outline"
                className="h-auto p-4 justify-start hover:shadow-md transition-all group"
                asChild
              >
                <Link href={action.href}>
                  <div className="flex items-center space-x-3 w-full">
                    <div className={`p-2 rounded-lg ${action.color} group-hover:scale-110 transition-transform`}>
                      <Icon className={`h-4 w-4 ${action.textColor}`} />
                    </div>
                    <div className="text-left flex-1">
                      <div className="font-medium text-sm text-protec-navy">
                        {action.title}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {action.description}
                      </div>
                    </div>
                  </div>
                </Link>
              </Button>
            )
          })}
        </div>

        <div className="mt-6 p-4 bg-protec-gray/50 rounded-lg">
          <div className="flex items-center space-x-2 mb-2">
            <Gift className="h-4 w-4 text-protec-red" />
            <span className="font-medium text-sm text-protec-navy">
              Special Offer
            </span>
          </div>
          <p className="text-xs text-muted-foreground mb-3">
            Refer 3 alumni and get exclusive access to premium networking events!
          </p>
          <Button size="sm" variant="outline" className="w-full" asChild>
            <Link href="/referral">
              Learn More
            </Link>
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
