"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { 
  Users, 
  MessageSquare, 
  Calendar, 
  Heart,
  TrendingUp,
  TrendingDown,
  Eye,
  Target
} from "lucide-react"

// Mock data - in real app, this would come from tRPC
const personalData = {
  profileViews: {
    current: 156,
    previous: 134,
    change: 16.4,
    trend: "up"
  },
  connections: {
    current: 127,
    previous: 115,
    change: 10.4,
    trend: "up"
  },
  posts: {
    current: 23,
    previous: 18,
    change: 27.8,
    trend: "up"
  },
  engagement: {
    current: 89,
    previous: 76,
    change: 17.1,
    trend: "up"
  },
  eventsAttended: {
    current: 8,
    previous: 6,
    change: 33.3,
    trend: "up"
  },
  donations: {
    current: 2500,
    previous: 2000,
    change: 25.0,
    trend: "up"
  }
}

interface PersonalMetricCardProps {
  title: string
  value: string | number
  change: number
  trend: "up" | "down"
  icon: React.ElementType
  prefix?: string
  suffix?: string
  description?: string
}

function PersonalMetricCard({ 
  title, 
  value, 
  change, 
  trend, 
  icon: Icon, 
  prefix = "", 
  suffix = "",
  description 
}: PersonalMetricCardProps) {
  const isPositive = trend === "up"
  
  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">
          {title}
        </CardTitle>
        <Icon className={`h-4 w-4 ${isPositive ? 'text-green-600' : 'text-red-600'}`} />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold text-protec-navy">
          {prefix}{typeof value === 'number' ? value.toLocaleString() : value}{suffix}
        </div>
        <div className="flex items-center space-x-2 mt-2">
          {isPositive ? (
            <TrendingUp className="h-4 w-4 text-green-600" />
          ) : (
            <TrendingDown className="h-4 w-4 text-red-600" />
          )}
          <Badge 
            variant="secondary" 
            className={isPositive ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}
          >
            {isPositive ? "+" : ""}{change.toFixed(1)}%
          </Badge>
        </div>
        {description && (
          <p className="text-xs text-muted-foreground mt-1">{description}</p>
        )}
      </CardContent>
    </Card>
  )
}

export function PersonalAnalyticsOverview() {
  // Calculate some derived metrics
  const profileCompleteness = 85 // Mock data
  const networkGrowthRate = ((personalData.connections.current - personalData.connections.previous) / personalData.connections.previous) * 100
  const engagementScore = Math.round((personalData.engagement.current / personalData.posts.current) * 10) / 10

  return (
    <div className="space-y-6">
      {/* Main Metrics Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <PersonalMetricCard
          title="Profile Views"
          value={personalData.profileViews.current}
          change={personalData.profileViews.change}
          trend={personalData.profileViews.trend}
          icon={Eye}
          description="People who viewed your profile"
        />
        
        <PersonalMetricCard
          title="Network Size"
          value={personalData.connections.current}
          change={personalData.connections.change}
          trend={personalData.connections.trend}
          icon={Users}
          description="Total connections"
        />
        
        <PersonalMetricCard
          title="Posts Shared"
          value={personalData.posts.current}
          change={personalData.posts.change}
          trend={personalData.posts.trend}
          icon={MessageSquare}
          description="Content you've shared"
        />
        
        <PersonalMetricCard
          title="Total Engagement"
          value={personalData.engagement.current}
          change={personalData.engagement.change}
          trend={personalData.engagement.trend}
          icon={Heart}
          description="Likes, comments, shares"
        />
        
        <PersonalMetricCard
          title="Events Attended"
          value={personalData.eventsAttended.current}
          change={personalData.eventsAttended.change}
          trend={personalData.eventsAttended.trend}
          icon={Calendar}
          description="This year"
        />
        
        <PersonalMetricCard
          title="Total Donations"
          value={personalData.donations.current}
          change={personalData.donations.change}
          trend={personalData.donations.trend}
          icon={Target}
          prefix="R"
          description="Lifetime giving"
        />
      </div>

      {/* Profile Health Cards */}
      <div className="grid gap-6 md:grid-cols-3">
        {/* Profile Completeness */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Profile Completeness
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-2xl font-bold text-protec-navy">{profileCompleteness}%</span>
              <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                Almost Complete
              </Badge>
            </div>
            <Progress value={profileCompleteness} className="h-2" />
            <p className="text-xs text-muted-foreground">
              Add skills and experience to reach 100%
            </p>
          </CardContent>
        </Card>

        {/* Network Growth Rate */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Network Growth Rate
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-2xl font-bold text-protec-navy">
                {networkGrowthRate.toFixed(1)}%
              </span>
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                <TrendingUp className="mr-1 h-3 w-3" />
                Growing
              </Badge>
            </div>
            <div className="text-xs text-muted-foreground">
              <span className="font-medium">+{personalData.connections.current - personalData.connections.previous}</span> new connections this month
            </div>
          </CardContent>
        </Card>

        {/* Engagement Score */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Engagement Score
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-2xl font-bold text-protec-navy">
                {engagementScore}
              </span>
              <Badge variant="secondary" className="bg-purple-100 text-purple-800">
                Excellent
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground">
              Average engagement per post
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Insights */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-protec-navy">
            Quick Insights
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-lg font-bold text-blue-900">Top 15%</div>
              <p className="text-xs text-blue-700">Most connected alumni</p>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-lg font-bold text-green-900">4.2x</div>
              <p className="text-xs text-green-700">Above avg engagement</p>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-lg font-bold text-purple-900">12</div>
              <p className="text-xs text-purple-700">Students mentored</p>
            </div>
            <div className="text-center p-4 bg-orange-50 rounded-lg">
              <div className="text-lg font-bold text-orange-900">Rank #23</div>
              <p className="text-xs text-orange-700">Top donor this year</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
