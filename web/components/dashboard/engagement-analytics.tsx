"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { 
  Activity, 
  MessageCircle, 
  Calendar, 
  Heart,
  Eye,
  Share,
  TrendingUp,
  Clock,
  Target
} from "lucide-react"

interface EngagementAnalyticsProps {
  className?: string
}

export function EngagementAnalytics({ className }: EngagementAnalyticsProps) {
  // Mock data - replace with real API calls
  const engagementStats = {
    totalActivities: 156,
    weeklyGrowth: 8.5,
    averageSessionTime: 12.5, // minutes
    lastActiveDate: '2024-03-15',
    engagementScore: 85
  }

  const activityBreakdown = [
    { type: 'Messages', count: 45, percentage: 29, icon: MessageCircle, color: 'text-blue-600' },
    { type: 'Profile Views', count: 38, percentage: 24, icon: Eye, color: 'text-green-600' },
    { type: 'Event Interactions', count: 32, percentage: 21, icon: Calendar, color: 'text-purple-600' },
    { type: 'Post Likes', count: 25, percentage: 16, icon: Heart, color: 'text-red-600' },
    { type: 'Content Shares', count: 16, percentage: 10, icon: Share, color: 'text-orange-600' }
  ]

  const weeklyActivity = [
    { day: 'Mon', activities: 12, time: 8 },
    { day: 'Tue', activities: 18, time: 15 },
    { day: 'Wed', activities: 22, time: 18 },
    { day: 'Thu', activities: 15, time: 12 },
    { day: 'Fri', activities: 25, time: 22 },
    { day: 'Sat', activities: 8, time: 5 },
    { day: 'Sun', activities: 6, time: 4 }
  ]

  const engagementTrends = [
    {
      metric: 'Profile Completeness',
      current: 85,
      target: 100,
      change: 5,
      trend: 'up' as const
    },
    {
      metric: 'Network Activity',
      current: 72,
      target: 80,
      change: 12,
      trend: 'up' as const
    },
    {
      metric: 'Event Participation',
      current: 45,
      target: 60,
      change: -3,
      trend: 'down' as const
    },
    {
      metric: 'Content Engagement',
      current: 68,
      target: 75,
      change: 8,
      trend: 'up' as const
    }
  ]

  const recentEngagements = [
    {
      type: 'message',
      description: 'Sent message to Sarah Johnson',
      timestamp: '2 hours ago',
      impact: 'high'
    },
    {
      type: 'event',
      description: 'RSVP\'d to Tech Networking Event',
      timestamp: '4 hours ago',
      impact: 'medium'
    },
    {
      type: 'profile',
      description: 'Updated work experience',
      timestamp: '1 day ago',
      impact: 'high'
    },
    {
      type: 'connection',
      description: 'Connected with Michael Brown',
      timestamp: '2 days ago',
      impact: 'high'
    },
    {
      type: 'like',
      description: 'Liked post about career opportunities',
      timestamp: '3 days ago',
      impact: 'low'
    }
  ]

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high':
        return 'bg-green-100 text-green-800'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800'
      case 'low':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Engagement Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Activities</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{engagementStats.totalActivities}</div>
            <div className="flex items-center space-x-2 text-xs text-muted-foreground">
              <TrendingUp className="h-3 w-3 text-green-500" />
              <span className="text-green-500">+{engagementStats.weeklyGrowth}%</span>
              <span>this week</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Session Time</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{engagementStats.averageSessionTime}m</div>
            <div className="text-xs text-muted-foreground">
              Per session
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Engagement Score</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{engagementStats.engagementScore}</div>
            <div className="text-xs text-muted-foreground">
              Out of 100
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Last Active</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Today</div>
            <div className="text-xs text-muted-foreground">
              {new Date(engagementStats.lastActiveDate).toLocaleDateString()}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Activity Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Activity className="h-5 w-5" />
            <span>Activity Breakdown</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {activityBreakdown.map((activity) => {
              const IconComponent = activity.icon
              return (
                <div key={activity.type} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <IconComponent className={`h-4 w-4 ${activity.color}`} />
                      <span className="font-medium">{activity.type}</span>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {activity.count} ({activity.percentage}%)
                    </div>
                  </div>
                  <Progress value={activity.percentage} className="h-2" />
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Weekly Activity Pattern */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Calendar className="h-5 w-5" />
            <span>Weekly Activity Pattern</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {weeklyActivity.map((day) => (
              <div key={day.day} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <span className="font-medium w-12">{day.day}</span>
                  <div className="flex-1">
                    <div className="text-sm text-muted-foreground">
                      {day.activities} activities
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-medium">{day.time}m</div>
                  <div className="text-sm text-muted-foreground">active time</div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Engagement Trends */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TrendingUp className="h-5 w-5" />
            <span>Engagement Trends</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {engagementTrends.map((trend) => (
              <div key={trend.metric} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium">{trend.metric}</span>
                    <div className="flex items-center space-x-1">
                      <TrendingUp className={`h-3 w-3 ${trend.trend === 'up' ? 'text-green-500' : 'text-red-500'}`} />
                      <span className={`text-xs ${trend.trend === 'up' ? 'text-green-500' : 'text-red-500'}`}>
                        {trend.change > 0 ? '+' : ''}{trend.change}%
                      </span>
                    </div>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {trend.current}% / {trend.target}%
                  </div>
                </div>
                <Progress value={(trend.current / trend.target) * 100} className="h-2" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recent Engagements */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Activity className="h-5 w-5" />
            <span>Recent Engagements</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {recentEngagements.map((engagement, index) => (
              <div key={index} className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50">
                <div className="flex-1">
                  <p className="text-sm font-medium">{engagement.description}</p>
                  <p className="text-xs text-muted-foreground">{engagement.timestamp}</p>
                </div>
                <Badge 
                  variant="secondary" 
                  className={`text-xs ${getImpactColor(engagement.impact)}`}
                >
                  {engagement.impact} impact
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Engagement Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Target className="h-5 w-5" />
            <span>Engagement Recommendations</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-center space-x-2 mb-2">
                <Calendar className="h-4 w-4 text-blue-600" />
                <span className="font-medium text-blue-800">Attend More Events</span>
              </div>
              <p className="text-sm text-blue-700">
                Your event participation is below target. Consider attending 2-3 more events this month.
              </p>
            </div>
            
            <div className="p-4 bg-green-50 rounded-lg border border-green-200">
              <div className="flex items-center space-x-2 mb-2">
                <MessageCircle className="h-4 w-4 text-green-600" />
                <span className="font-medium text-green-800">Great Messaging Activity</span>
              </div>
              <p className="text-sm text-green-700">
                You're doing well with messaging! Keep engaging with your network regularly.
              </p>
            </div>
            
            <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
              <div className="flex items-center space-x-2 mb-2">
                <Heart className="h-4 w-4 text-yellow-600" />
                <span className="font-medium text-yellow-800">Increase Content Engagement</span>
              </div>
              <p className="text-sm text-yellow-700">
                Try liking and commenting on more posts to boost your content engagement score.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
