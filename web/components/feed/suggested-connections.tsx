"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { api } from "@/components/providers/trpc-provider"
import { UserPlus, Users, Loader2 } from "lucide-react"

// Mock data for suggested connections - in real app this would come from tRPC
const suggestedConnections = [
  {
    id: "1",
    name: "<PERSON>",
    photoUrl: "/avatars/sarah.jpg",
    currentRole: "Software Engineer at Google",
    graduationYear: 2018,
    mutualConnections: 5,
    skills: ["React", "TypeScript", "Node.js"]
  },
  {
    id: "2",
    name: "<PERSON>",
    photoUrl: "/avatars/michael.jpg",
    currentRole: "Data Scientist at Amazon",
    graduationYear: 2016,
    mutualConnections: 3,
    skills: ["Python", "Machine Learning", "AWS"]
  },
  {
    id: "3",
    name: "<PERSON><PERSON>",
    photoUrl: "/avatars/priya.jpg",
    currentRole: "Product Manager at Microsoft",
    graduationYear: 2019,
    mutualConnections: 7,
    skills: ["Product Strategy", "Agile", "Analytics"]
  }
]

export function SuggestedConnections() {
  const [connectingIds, setConnectingIds] = useState<Set<string>>(new Set())
  const connectMutation = api.alumni.connect.useMutation()

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const handleConnect = async (alumniId: string) => {
    setConnectingIds(prev => new Set(prev).add(alumniId))
    
    try {
      await connectMutation.mutateAsync({ alumniId })
      // Remove from suggestions after successful connection
      // In real app, this would refetch the suggestions
    } catch (error) {
      console.error("Connection error:", error)
    } finally {
      setConnectingIds(prev => {
        const newSet = new Set(prev)
        newSet.delete(alumniId)
        return newSet
      })
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-protec-navy flex items-center">
          <Users className="mr-2 h-5 w-5" />
          Suggested Connections
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {suggestedConnections.map((person) => (
          <div key={person.id} className="space-y-3">
            <div className="flex items-start space-x-3">
              <Avatar className="h-12 w-12">
                <AvatarImage src={person.photoUrl} alt={person.name} />
                <AvatarFallback className="bg-protec-navy text-white">
                  {getInitials(person.name)}
                </AvatarFallback>
              </Avatar>
              
              <div className="flex-1 min-w-0">
                <Link 
                  href={`/profile/${person.id}`}
                  className="font-medium text-sm text-protec-navy hover:text-protec-red transition-colors"
                >
                  {person.name}
                </Link>
                <p className="text-xs text-muted-foreground truncate">
                  {person.currentRole}
                </p>
                <p className="text-xs text-muted-foreground">
                  Class of {person.graduationYear}
                </p>
                
                {person.mutualConnections > 0 && (
                  <p className="text-xs text-blue-600 mt-1">
                    {person.mutualConnections} mutual connections
                  </p>
                )}
              </div>
            </div>

            {/* Skills */}
            <div className="flex flex-wrap gap-1">
              {person.skills.slice(0, 2).map((skill) => (
                <Badge key={skill} variant="outline" className="text-xs">
                  {skill}
                </Badge>
              ))}
              {person.skills.length > 2 && (
                <Badge variant="outline" className="text-xs">
                  +{person.skills.length - 2}
                </Badge>
              )}
            </div>

            {/* Connect Button */}
            <Button
              size="sm"
              onClick={() => handleConnect(person.id)}
              disabled={connectingIds.has(person.id)}
              className="w-full bg-protec-red hover:bg-protec-red/90"
            >
              {connectingIds.has(person.id) ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Connecting...
                </>
              ) : (
                <>
                  <UserPlus className="mr-2 h-4 w-4" />
                  Connect
                </>
              )}
            </Button>
          </div>
        ))}

        {suggestedConnections.length === 0 && (
          <div className="text-center py-6">
            <Users className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
            <p className="text-sm text-muted-foreground mb-2">
              No suggestions available
            </p>
            <Button variant="outline" size="sm" asChild>
              <Link href="/directory">
                Browse Alumni
              </Link>
            </Button>
          </div>
        )}

        {/* View All Link */}
        <div className="pt-2 border-t">
          <Button variant="ghost" size="sm" className="w-full" asChild>
            <Link href="/directory">
              View All Alumni
            </Link>
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
