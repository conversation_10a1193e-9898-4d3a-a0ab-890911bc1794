"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { api } from "@/components/providers/trpc-provider"
import { TrendingUp, Hash } from "lucide-react"

export function TrendingTags() {
  const { data: trendingTags, isLoading } = api.posts.getTrendingTags.useQuery()

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-protec-navy flex items-center">
            <TrendingUp className="mr-2 h-5 w-5" />
            Trending Topics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="h-6 bg-gray-200 rounded animate-pulse" />
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!trendingTags || trendingTags.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-protec-navy flex items-center">
            <TrendingUp className="mr-2 h-5 w-5" />
            Trending Topics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            No trending topics yet. Start a conversation!
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-protec-navy flex items-center">
          <TrendingUp className="mr-2 h-5 w-5" />
          Trending Topics
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {trendingTags.map((item, index) => (
          <div key={item.tag} className="flex items-center justify-between group">
            <Button
              variant="ghost"
              className="flex-1 justify-start p-0 h-auto text-left hover:text-protec-red"
            >
              <div className="flex items-center space-x-2">
                <Hash className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium">#{item.tag}</span>
              </div>
            </Button>
            <div className="flex items-center space-x-2">
              <Badge variant="secondary" className="text-xs">
                {item.count}
              </Badge>
              {index < 3 && (
                <div className="w-2 h-2 rounded-full bg-protec-red" />
              )}
            </div>
          </div>
        ))}

        {trendingTags.length === 0 && (
          <div className="text-center py-4">
            <Hash className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
            <p className="text-sm text-muted-foreground">
              No trending topics yet
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
