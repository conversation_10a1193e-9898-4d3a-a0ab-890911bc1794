"use client"

import { useState, useEffect, useRef } from "react"
import { useSession } from "next-auth/react"
import { api } from "@/components/providers/trpc-provider"
import { CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Skeleton } from "@/components/ui/skeleton"
import { 
  Send, 
  MoreVertical, 
  Users, 
  X,
  Paperclip,
  Image as ImageIcon,
  File
} from "lucide-react"
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu"
import { toast } from "sonner"
import { cn } from "@/lib/utils"

interface ChatWindowProps {
  conversationId: string
  onClose?: () => void
}

export function ChatWindow({ conversationId, onClose }: ChatWindowProps) {
  const { data: session } = useSession()
  const [message, setMessage] = useState("")
  const [isTyping, setIsTyping] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  const { data: messagesData, isLoading, refetch } = api.messages.getMessages.useQuery({
    conversationId,
    limit: 50,
  })

  const { data: conversationsData } = api.messages.getConversations.useQuery({
    limit: 50,
  })

  const sendMessageMutation = api.messages.sendMessage.useMutation({
    onSuccess: () => {
      setMessage("")
      refetch()
      scrollToBottom()
      toast.success("Message sent!")
    },
    onError: (error) => {
      toast.error(error.message || "Failed to send message")
      setIsTyping(false)
    },
  })

  const markAsReadMutation = api.messages.markAsRead.useMutation()

  // Auto-scroll to bottom when new messages arrive
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messagesData?.messages])

  // Mark conversation as read when opened
  useEffect(() => {
    markAsReadMutation.mutate({ conversationId })
  }, [conversationId])

  // Auto-refresh messages every 10 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      refetch()
    }, 10000)

    return () => clearInterval(interval)
  }, [refetch])

  // Focus input when conversation changes
  useEffect(() => {
    inputRef.current?.focus()
  }, [conversationId])

  const handleSendMessage = async () => {
    if (!message.trim() || sendMessageMutation.isLoading) return

    setIsTyping(true)
    try {
      await sendMessageMutation.mutateAsync({
        conversationId,
        content: message.trim(),
        messageType: "text",
      })
    } finally {
      setIsTyping(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    } else if (e.key === "Escape") {
      setMessage("")
      inputRef.current?.blur()
    }
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const formatMessageTime = (date: string) => {
    return new Date(date).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    })
  }

  const formatMessageDate = (date: string) => {
    const messageDate = new Date(date)
    const today = new Date()
    const yesterday = new Date(today)
    yesterday.setDate(yesterday.getDate() - 1)

    if (messageDate.toDateString() === today.toDateString()) {
      return "Today"
    } else if (messageDate.toDateString() === yesterday.toDateString()) {
      return "Yesterday"
    } else {
      return messageDate.toLocaleDateString()
    }
  }

  // Get conversation info
  const conversation = conversationsData?.conversations.find(c => c.id === conversationId)
  const getConversationTitle = () => {
    if (!conversation) return "Loading..."
    
    if (conversation.title) {
      return conversation.title
    }

    if (conversation.isGroup) {
      return `Group (${conversation.participants.length})`
    }

    const otherParticipant = conversation.participants.find(
      (p: any) => p.alumni.id !== session?.user?.id
    )
    
    return otherParticipant?.alumni.name || "Unknown"
  }

  if (isLoading) {
    return (
      <>
        <CardHeader className="border-b">
          <div className="flex items-center space-x-3">
            <Skeleton className="h-10 w-10 rounded-full" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-3 w-20" />
            </div>
          </div>
        </CardHeader>
        <CardContent className="flex-1 p-4">
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="flex items-start space-x-3">
                <Skeleton className="h-8 w-8 rounded-full" />
                <div className="space-y-2">
                  <Skeleton className="h-4 w-48" />
                  <Skeleton className="h-3 w-24" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </>
    )
  }

  const messages = messagesData?.messages || []

  return (
    <div className="flex flex-col h-full">
      {/* Chat Header */}
      <CardHeader className="border-b bg-muted/30">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <Avatar className="h-10 w-10 ring-2 ring-background">
                {conversation?.isGroup ? (
                  <AvatarFallback className="bg-primary text-primary-foreground">
                    <Users className="h-5 w-5" />
                  </AvatarFallback>
                ) : (
                  <>
                    <AvatarImage
                      src={conversation?.participants.find(p => p.alumni.id !== session?.user?.id)?.alumni.photoUrl || ""}
                      alt={getConversationTitle()}
                    />
                    <AvatarFallback className="bg-primary text-primary-foreground">
                      {getInitials(getConversationTitle())}
                    </AvatarFallback>
                  </>
                )}
              </Avatar>
              {/* Online status indicator */}
              <div className="absolute bottom-0 right-0 h-3 w-3 bg-green-500 border-2 border-background rounded-full"></div>
            </div>
            <div>
              <CardTitle className="text-lg">{getConversationTitle()}</CardTitle>
              {conversation?.isGroup ? (
                <p className="text-sm text-muted-foreground">
                  {conversation.participants.length} participants
                </p>
              ) : (
                <p className="text-sm text-muted-foreground">
                  Online now
                </p>
              )}
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {/* Mobile back button */}
            {onClose && (
              <Button variant="ghost" size="sm" onClick={onClose} className="lg:hidden">
                <X className="h-4 w-4" />
                <span className="sr-only">Back to conversations</span>
              </Button>
            )}

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>View Profile</DropdownMenuItem>
                <DropdownMenuItem>Mute Conversation</DropdownMenuItem>
                <DropdownMenuItem className="text-red-600">
                  Leave Conversation
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            {/* Desktop close button */}
            {onClose && (
              <Button variant="ghost" size="sm" onClick={onClose} className="hidden lg:flex">
                <X className="h-4 w-4" />
                <span className="sr-only">Close chat</span>
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      {/* Messages Area */}
      <CardContent className="flex-1 p-0">
        <ScrollArea className="h-[calc(100vh-400px)] p-4">
          {messages.length === 0 ? (
            <div className="flex items-center justify-center h-full text-center text-muted-foreground p-8">
              <div className="space-y-4">
                <div className="w-16 h-16 mx-auto bg-primary/10 rounded-full flex items-center justify-center animate-pulse">
                  <MessageSquare className="h-8 w-8 text-primary" />
                </div>
                <div className="space-y-2">
                  <p className="text-lg font-medium">No messages yet</p>
                  <p className="text-sm text-muted-foreground">
                    Be the first to send a message in this conversation
                  </p>
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {messages.map((message: any, index: number) => {
                const isOwnMessage = message.sender.id === session?.user?.id
                const showDate = index === 0 || 
                  formatMessageDate(message.createdAt) !== formatMessageDate(messages[index - 1].createdAt)
                
                return (
                  <div key={message.id}>
                    {showDate && (
                      <div className="text-center my-4">
                        <span className="bg-muted px-3 py-1 rounded-full text-xs text-muted-foreground">
                          {formatMessageDate(message.createdAt)}
                        </span>
                      </div>
                    )}
                    
                    <div className={cn(
                      "flex items-start space-x-3",
                      isOwnMessage && "flex-row-reverse space-x-reverse"
                    )}>
                      {!isOwnMessage && (
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={message.sender.photoUrl || ""} alt={message.sender.name} />
                          <AvatarFallback className="bg-protec-navy text-white text-xs">
                            {getInitials(message.sender.name)}
                          </AvatarFallback>
                        </Avatar>
                      )}
                      
                      <div className={cn(
                        "max-w-[70%] space-y-1",
                        isOwnMessage && "items-end"
                      )}>
                        {!isOwnMessage && (
                          <p className="text-xs text-muted-foreground font-medium">
                            {message.sender.name}
                          </p>
                        )}
                        
                        <div className={cn(
                          "rounded-2xl px-4 py-3 text-sm shadow-sm max-w-prose",
                          isOwnMessage
                            ? "bg-primary text-primary-foreground rounded-br-md"
                            : "bg-muted rounded-bl-md"
                        )}>
                          {message.messageType === "text" ? (
                            <p className="whitespace-pre-wrap leading-relaxed">{message.content}</p>
                          ) : (
                            <div className="flex items-center space-x-2">
                              {message.messageType === "image" && <ImageIcon className="h-4 w-4" />}
                              {message.messageType === "file" && <File className="h-4 w-4" />}
                              <span>{message.content}</span>
                            </div>
                          )}
                        </div>
                        
                        <div className={cn(
                          "flex items-center space-x-1 text-xs text-muted-foreground",
                          isOwnMessage && "justify-end"
                        )}>
                          <span>{formatMessageTime(message.createdAt)}</span>
                          {isOwnMessage && (
                            <div className="flex items-center">
                              <div className="w-1 h-1 bg-current rounded-full opacity-60"></div>
                              <div className="w-1 h-1 bg-current rounded-full opacity-60 ml-0.5"></div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                )
              })}
              <div ref={messagesEndRef} />
            </div>
          )}
        </ScrollArea>
      </CardContent>

      {/* Message Input */}
      <div className="border-t bg-muted/30 p-4">
        <div className="flex items-end space-x-3">
          <Button variant="ghost" size="sm" className="mb-2 hover:bg-accent">
            <Paperclip className="h-4 w-4" />
          </Button>

          <div className="flex-1 relative">
            <Input
              ref={inputRef}
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Type a message..."
              disabled={sendMessageMutation.isLoading}
              className="min-h-[44px] rounded-full border-2 focus:border-primary pr-12 resize-none"
            />
            {isTyping && (
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
              </div>
            )}
          </div>

          <Button
            onClick={handleSendMessage}
            disabled={!message.trim() || sendMessageMutation.isLoading}
            size="sm"
            className="h-11 w-11 rounded-full bg-primary hover:bg-primary/90 shadow-lg transition-all duration-200 hover:scale-105"
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  )
}
