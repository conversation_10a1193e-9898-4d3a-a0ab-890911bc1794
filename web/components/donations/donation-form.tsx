"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { api } from "@/components/providers/trpc-provider"
import { Heart, CreditCard, Smartphone, Loader2, Shield } from "lucide-react"
import { toast } from "sonner"

const donationSchema = z.object({
  amount: z.number().min(10, "Minimum donation is R10").max(100000, "Maximum donation is R100,000"),
  customAmount: z.number().optional(),
  gateway: z.enum(['payfast', 'snapscan', 'ozow']),
  purpose: z.enum(['general', 'scholarship', 'infrastructure', 'events', 'mentorship']).default('general'),
})

type DonationFormData = z.infer<typeof donationSchema>

const predefinedAmounts = [50, 100, 250, 500, 1000, 2500]

const purposes = [
  { value: 'general', label: 'General Fund', description: 'Support where needed most' },
  { value: 'scholarship', label: 'Scholarships', description: 'Help students with financial aid' },
  { value: 'infrastructure', label: 'Infrastructure', description: 'Improve facilities and equipment' },
  { value: 'events', label: 'Events & Programs', description: 'Fund alumni and student events' },
  { value: 'mentorship', label: 'Mentorship', description: 'Support mentorship programs' },
]

const gateways = [
  { 
    value: 'payfast', 
    label: 'PayFast', 
    description: 'Credit/Debit Cards, EFT',
    icon: CreditCard,
    fees: '2.9% + R2'
  },
  { 
    value: 'snapscan', 
    label: 'SnapScan', 
    description: 'QR Code Payment',
    icon: Smartphone,
    fees: '2.9%'
  },
  { 
    value: 'ozow', 
    label: 'Ozow', 
    description: 'Instant EFT',
    icon: CreditCard,
    fees: '1.5%'
  },
]

export function DonationForm() {
  const { data: session } = useSession()
  const [selectedAmount, setSelectedAmount] = useState<number | null>(null)
  const [isCustomAmount, setIsCustomAmount] = useState(false)

  const createDonationMutation = api.donations.create.useMutation()

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors }
  } = useForm<DonationFormData>({
    resolver: zodResolver(donationSchema),
    defaultValues: {
      gateway: 'payfast',
      purpose: 'general'
    }
  })

  const gateway = watch('gateway')
  const purpose = watch('purpose')

  const handleAmountSelect = (amount: number) => {
    setSelectedAmount(amount)
    setIsCustomAmount(false)
    setValue('amount', amount)
  }

  const handleCustomAmount = () => {
    setIsCustomAmount(true)
    setSelectedAmount(null)
  }

  const onSubmit = async (data: DonationFormData) => {
    if (!session) {
      toast.error("Please sign in to make a donation")
      return
    }

    try {
      const donation = await createDonationMutation.mutateAsync({
        amountZAR: data.amount,
        gateway: data.gateway,
        purpose: data.purpose,
      })

      toast.success("Redirecting to payment...")
      
      // In a real app, redirect to payment gateway
      // window.location.href = donation.paymentUrl
      
    } catch (error) {
      console.error("Donation error:", error)
      toast.error("Failed to process donation. Please try again.")
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-2xl font-bold text-protec-navy flex items-center">
          <Heart className="mr-2 h-6 w-6 text-protec-red" />
          Make a Donation
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Donation Amount */}
          <div className="space-y-4">
            <Label className="text-base font-semibold">Donation Amount (ZAR)</Label>
            
            {/* Predefined Amounts */}
            <div className="grid grid-cols-3 gap-3">
              {predefinedAmounts.map((amount) => (
                <Button
                  key={amount}
                  type="button"
                  variant={selectedAmount === amount ? "default" : "outline"}
                  className={selectedAmount === amount ? "bg-protec-red hover:bg-protec-red/90" : ""}
                  onClick={() => handleAmountSelect(amount)}
                >
                  R{amount}
                </Button>
              ))}
            </div>

            {/* Custom Amount */}
            <div className="space-y-2">
              <Button
                type="button"
                variant={isCustomAmount ? "default" : "outline"}
                className={`w-full ${isCustomAmount ? "bg-protec-red hover:bg-protec-red/90" : ""}`}
                onClick={handleCustomAmount}
              >
                Custom Amount
              </Button>
              
              {isCustomAmount && (
                <Input
                  type="number"
                  placeholder="Enter amount"
                  min="10"
                  max="100000"
                  {...register("amount", { valueAsNumber: true })}
                  className={errors.amount ? "border-red-500" : ""}
                />
              )}
            </div>

            {errors.amount && (
              <p className="text-sm text-red-500">{errors.amount.message}</p>
            )}
          </div>

          {/* Purpose */}
          <div className="space-y-3">
            <Label className="text-base font-semibold">Donation Purpose</Label>
            <Select onValueChange={(value) => setValue("purpose", value as any)}>
              <SelectTrigger>
                <SelectValue placeholder="Select purpose" />
              </SelectTrigger>
              <SelectContent>
                {purposes.map((purpose) => (
                  <SelectItem key={purpose.value} value={purpose.value}>
                    <div>
                      <div className="font-medium">{purpose.label}</div>
                      <div className="text-xs text-muted-foreground">{purpose.description}</div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Payment Gateway */}
          <div className="space-y-3">
            <Label className="text-base font-semibold">Payment Method</Label>
            <RadioGroup
              value={gateway}
              onValueChange={(value) => setValue("gateway", value as any)}
              className="space-y-3"
            >
              {gateways.map((gw) => {
                const Icon = gw.icon
                return (
                  <div key={gw.value} className="flex items-center space-x-3 border rounded-lg p-3">
                    <RadioGroupItem value={gw.value} id={gw.value} />
                    <div className="flex items-center space-x-3 flex-1">
                      <Icon className="h-5 w-5 text-protec-navy" />
                      <div className="flex-1">
                        <Label htmlFor={gw.value} className="font-medium cursor-pointer">
                          {gw.label}
                        </Label>
                        <p className="text-xs text-muted-foreground">{gw.description}</p>
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {gw.fees}
                      </div>
                    </div>
                  </div>
                )
              })}
            </RadioGroup>
          </div>

          {/* Security Notice */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <Shield className="h-5 w-5 text-blue-600 mt-0.5" />
              <div className="text-sm">
                <p className="font-medium text-blue-900 mb-1">Secure & Tax Deductible</p>
                <p className="text-blue-700">
                  Your donation is processed securely and is tax deductible. 
                  You'll receive a Section 18A certificate for tax purposes.
                </p>
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <Button
            type="submit"
            disabled={!session || createDonationMutation.isLoading || (!selectedAmount && !isCustomAmount)}
            className="w-full bg-protec-red hover:bg-protec-red/90 text-lg py-6"
          >
            {createDonationMutation.isLoading ? (
              <>
                <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                Processing...
              </>
            ) : (
              <>
                <Heart className="mr-2 h-5 w-5" />
                Donate {selectedAmount ? `R${selectedAmount}` : isCustomAmount ? 'Now' : ''}
              </>
            )}
          </Button>

          {!session && (
            <p className="text-center text-sm text-muted-foreground">
              Please <a href="/auth/signin" className="text-protec-red hover:underline">sign in</a> to make a donation
            </p>
          )}
        </form>
      </CardContent>
    </Card>
  )
}
