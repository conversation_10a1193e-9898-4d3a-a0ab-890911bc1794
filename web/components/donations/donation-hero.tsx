import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Heart, GraduationCap, Users, TrendingUp } from "lucide-react"

export function DonationHero() {
  return (
    <section className="relative bg-gradient-to-br from-protec-navy via-protec-navy/90 to-protec-red/20 text-white py-20">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-white/10 [mask-image:linear-gradient(0deg,rgba(255,255,255,0.1),rgba(255,255,255,0.5))]" />
      
      <div className="relative container mx-auto px-6">
        <div className="max-w-4xl mx-auto text-center space-y-8">
          {/* Badge */}
          <Badge variant="outline" className="border-white/20 bg-white/10 text-white">
            <Heart className="mr-2 h-4 w-4" />
            Give Back to PROTEC
          </Badge>

          {/* Heading */}
          <div className="space-y-4">
            <h1 className="text-4xl md:text-6xl font-bold tracking-tight">
              Empower the Next Generation of{" "}
              <span className="text-protec-red">STEM Leaders</span>
            </h1>
            <p className="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto">
              Your donation helps provide scholarships, resources, and opportunities 
              for aspiring engineers, scientists, and technologists across South Africa.
            </p>
          </div>

          {/* Impact Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 py-8">
            <div className="text-center">
              <div className="flex items-center justify-center w-16 h-16 mx-auto mb-4 bg-white/10 rounded-full">
                <GraduationCap className="h-8 w-8" />
              </div>
              <div className="text-3xl font-bold">2,500+</div>
              <div className="text-blue-100">Students Supported</div>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center w-16 h-16 mx-auto mb-4 bg-white/10 rounded-full">
                <Users className="h-8 w-8" />
              </div>
              <div className="text-3xl font-bold">500+</div>
              <div className="text-blue-100">Alumni Donors</div>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center w-16 h-16 mx-auto mb-4 bg-white/10 rounded-full">
                <TrendingUp className="h-8 w-8" />
              </div>
              <div className="text-3xl font-bold">R2M+</div>
              <div className="text-blue-100">Total Raised</div>
            </div>
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
            <Button 
              size="lg" 
              className="bg-protec-red hover:bg-protec-red/90 text-white px-8 py-3 text-lg"
            >
              <Heart className="mr-2 h-5 w-5" />
              Donate Now
            </Button>
            <Button 
              variant="outline" 
              size="lg"
              className="border-white text-white hover:bg-white hover:text-protec-navy px-8 py-3 text-lg"
            >
              Learn More
            </Button>
          </div>

          {/* Trust Indicators */}
          <div className="pt-8 border-t border-white/20">
            <p className="text-sm text-blue-100 mb-4">
              Trusted by thousands of alumni worldwide
            </p>
            <div className="flex items-center justify-center space-x-8 opacity-60">
              <div className="text-sm">🔒 Secure Payments</div>
              <div className="text-sm">📄 Tax Deductible</div>
              <div className="text-sm">💯 100% Transparent</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
