"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { motion, AnimatePresence } from "framer-motion"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { 
  Search, 
  X, 
  ChevronDown,
  ChevronUp,
  MapPin,
  Briefcase,
  GraduationCap,
  Save,
  History
} from "lucide-react"

const searchSchema = z.object({
  query: z.string().optional(),
  graduationYearRange: z.object({
    from: z.number().optional(),
    to: z.number().optional(),
  }).optional(),
  programmes: z.array(z.string()).optional(),
  industries: z.array(z.string()).optional(),
  skills: z.array(z.string()).optional(),
  locations: z.object({
    provinces: z.array(z.string()).optional(),
    cities: z.array(z.string()).optional(),
    countries: z.array(z.string()).optional(),
  }).optional(),
  careerLevel: z.array(z.string()).optional(),
  companySize: z.array(z.string()).optional(),
  interests: z.array(z.string()).optional(),
  availability: z.object({
    mentoring: z.boolean().optional(),
    networking: z.boolean().optional(),
    jobOpportunities: z.boolean().optional(),
  }).optional(),
})

type SearchFormData = z.infer<typeof searchSchema>

interface AdvancedSearchProps {
  onSearch: (filters: SearchFormData) => void
  onSaveSearch?: (name: string, filters: SearchFormData) => void
  savedSearches?: Array<{ id: string; name: string; filters: SearchFormData }>
  isLoading?: boolean
}

// Static data for dropdowns
const PROGRAMMES = [
  "Computer Science", "Engineering", "Mathematics", "Physics", 
  "Chemistry", "Biology", "Medicine", "Business", "Economics", "Law"
]

const INDUSTRIES = [
  "Technology", "Finance", "Healthcare", "Education", "Manufacturing",
  "Consulting", "Media", "Government", "Non-profit", "Retail", "Energy"
]

const PROVINCES = [
  "Western Cape", "Gauteng", "KwaZulu-Natal", "Eastern Cape", 
  "Free State", "Limpopo", "Mpumalanga", "Northern Cape", "North West"
]

const CAREER_LEVELS = [
  "Entry Level", "Mid Level", "Senior Level", "Executive", 
  "Founder/Entrepreneur", "Consultant", "Academic"
]

const COMPANY_SIZES = [
  "Startup (1-10)", "Small (11-50)", "Medium (51-200)", 
  "Large (201-1000)", "Enterprise (1000+)"
]

const SKILLS_SUGGESTIONS = [
  "JavaScript", "Python", "React", "Node.js", "Machine Learning", 
  "Data Science", "Project Management", "Leadership", "Marketing", 
  "Sales", "Design", "Research", "Strategy", "Operations"
]

export function AdvancedSearch({ 
  onSearch, 
  onSaveSearch, 
  savedSearches = [], 
  isLoading = false 
}: AdvancedSearchProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [activeFilters, setActiveFilters] = useState<string[]>([])
  const [skillInput, setSkillInput] = useState("")
  const [showSaveDialog, setShowSaveDialog] = useState(false)
  const [saveSearchName, setSaveSearchName] = useState("")

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors }
  } = useForm<SearchFormData>({
    resolver: zodResolver(searchSchema),
    defaultValues: {
      programmes: [],
      industries: [],
      skills: [],
      locations: { provinces: [], cities: [], countries: [] },
      careerLevel: [],
      companySize: [],
      interests: [],
      availability: {}
    }
  })

  const watchedValues = watch()

  // Update active filters when form values change
  useEffect(() => {
    const filters: string[] = []
    
    if (watchedValues.query) filters.push("Search Query")
    if (watchedValues.graduationYearRange?.from || watchedValues.graduationYearRange?.to) {
      filters.push("Graduation Year")
    }
    if (watchedValues.programmes?.length) filters.push("Programmes")
    if (watchedValues.industries?.length) filters.push("Industries")
    if (watchedValues.skills?.length) filters.push("Skills")
    if (watchedValues.locations?.provinces?.length) filters.push("Location")
    if (watchedValues.careerLevel?.length) filters.push("Career Level")
    if (watchedValues.companySize?.length) filters.push("Company Size")
    if (watchedValues.interests?.length) filters.push("Interests")
    
    setActiveFilters(filters)
  }, [watchedValues])

  const onSubmit = (data: SearchFormData) => {
    onSearch(data)
  }

  const clearAllFilters = () => {
    reset()
    onSearch({})
  }

  const addSkill = (skill: string) => {
    const currentSkills = watchedValues.skills || []
    if (!currentSkills.includes(skill)) {
      setValue('skills', [...currentSkills, skill])
    }
    setSkillInput("")
  }

  const removeSkill = (skillToRemove: string) => {
    const currentSkills = watchedValues.skills || []
    setValue('skills', currentSkills.filter(skill => skill !== skillToRemove))
  }

  const handleArrayFieldChange = (
    fieldName: keyof SearchFormData,
    value: string,
    checked: boolean
  ) => {
    const currentValues = (watchedValues[fieldName] as string[]) || []
    if (checked) {
      setValue(fieldName, [...currentValues, value] as any)
    } else {
      setValue(fieldName, currentValues.filter(v => v !== value) as any)
    }
  }

  const loadSavedSearch = (filters: SearchFormData) => {
    reset(filters)
    onSearch(filters)
  }

  const handleSaveSearch = () => {
    if (saveSearchName.trim() && onSaveSearch) {
      onSaveSearch(saveSearchName.trim(), watchedValues)
      setSaveSearchName("")
      setShowSaveDialog(false)
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Search className="w-5 h-5" />
            <span>Advanced Alumni Search</span>
          </CardTitle>
          <div className="flex items-center space-x-2">
            {activeFilters.length > 0 && (
              <Badge variant="secondary" className="text-xs">
                {activeFilters.length} filter{activeFilters.length !== 1 ? 's' : ''} active
              </Badge>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? (
                <ChevronUp className="w-4 h-4" />
              ) : (
                <ChevronDown className="w-4 h-4" />
              )}
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Search */}
          <div className="space-y-2">
            <Label htmlFor="query">Search</Label>
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                id="query"
                placeholder="Search by name, company, role, or keywords..."
                className="pl-10"
                {...register('query')}
              />
            </div>
          </div>

          {/* Saved Searches */}
          {savedSearches.length > 0 && (
            <div className="space-y-2">
              <Label>Saved Searches</Label>
              <div className="flex flex-wrap gap-2">
                {savedSearches.map((saved) => (
                  <Button
                    key={saved.id}
                    variant="outline"
                    size="sm"
                    onClick={() => loadSavedSearch(saved.filters)}
                    className="text-xs"
                  >
                    <History className="w-3 h-3 mr-1" />
                    {saved.name}
                  </Button>
                ))}
              </div>
            </div>
          )}

          <AnimatePresence>
            {isExpanded && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="space-y-6"
              >
                {/* Graduation Year Range */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Graduation Year From</Label>
                    <Input
                      type="number"
                      placeholder="e.g., 2010"
                      {...register('graduationYearRange.from', { valueAsNumber: true })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Graduation Year To</Label>
                    <Input
                      type="number"
                      placeholder="e.g., 2020"
                      {...register('graduationYearRange.to', { valueAsNumber: true })}
                    />
                  </div>
                </div>

                {/* Programmes */}
                <div className="space-y-2">
                  <Label className="flex items-center space-x-2">
                    <GraduationCap className="w-4 h-4" />
                    <span>Programmes</span>
                  </Label>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                    {PROGRAMMES.map((programme) => (
                      <div key={programme} className="flex items-center space-x-2">
                        <Checkbox
                          id={`programme-${programme}`}
                          checked={watchedValues.programmes?.includes(programme) || false}
                          onCheckedChange={(checked) =>
                            handleArrayFieldChange('programmes', programme, checked as boolean)
                          }
                        />
                        <Label htmlFor={`programme-${programme}`} className="text-sm">
                          {programme}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Industries */}
                <div className="space-y-2">
                  <Label className="flex items-center space-x-2">
                    <Briefcase className="w-4 h-4" />
                    <span>Industries</span>
                  </Label>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                    {INDUSTRIES.map((industry) => (
                      <div key={industry} className="flex items-center space-x-2">
                        <Checkbox
                          id={`industry-${industry}`}
                          checked={watchedValues.industries?.includes(industry) || false}
                          onCheckedChange={(checked) =>
                            handleArrayFieldChange('industries', industry, checked as boolean)
                          }
                        />
                        <Label htmlFor={`industry-${industry}`} className="text-sm">
                          {industry}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Skills */}
                <div className="space-y-2">
                  <Label>Skills</Label>
                  <div className="space-y-2">
                    <div className="flex space-x-2">
                      <Input
                        placeholder="Add a skill..."
                        value={skillInput}
                        onChange={(e) => setSkillInput(e.target.value)}
                        onKeyPress={(e) => {
                          if (e.key === 'Enter') {
                            e.preventDefault()
                            if (skillInput.trim()) {
                              addSkill(skillInput.trim())
                            }
                          }
                        }}
                      />
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => skillInput.trim() && addSkill(skillInput.trim())}
                      >
                        Add
                      </Button>
                    </div>
                    
                    {/* Skill suggestions */}
                    <div className="flex flex-wrap gap-1">
                      {SKILLS_SUGGESTIONS.map((skill) => (
                        <Button
                          key={skill}
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="text-xs h-6"
                          onClick={() => addSkill(skill)}
                        >
                          + {skill}
                        </Button>
                      ))}
                    </div>

                    {/* Selected skills */}
                    {watchedValues.skills && watchedValues.skills.length > 0 && (
                      <div className="flex flex-wrap gap-2">
                        {watchedValues.skills.map((skill) => (
                          <Badge key={skill} variant="secondary" className="text-xs">
                            {skill}
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              className="ml-1 h-4 w-4 p-0"
                              onClick={() => removeSkill(skill)}
                            >
                              <X className="w-3 h-3" />
                            </Button>
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                </div>

                {/* Location */}
                <div className="space-y-2">
                  <Label className="flex items-center space-x-2">
                    <MapPin className="w-4 h-4" />
                    <span>Location</span>
                  </Label>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                    {PROVINCES.map((province) => (
                      <div key={province} className="flex items-center space-x-2">
                        <Checkbox
                          id={`province-${province}`}
                          checked={watchedValues.locations?.provinces?.includes(province) || false}
                          onCheckedChange={(checked) => {
                            const currentProvinces = watchedValues.locations?.provinces || []
                            if (checked) {
                              setValue('locations.provinces', [...currentProvinces, province])
                            } else {
                              setValue('locations.provinces', currentProvinces.filter(p => p !== province))
                            }
                          }}
                        />
                        <Label htmlFor={`province-${province}`} className="text-sm">
                          {province}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-2 justify-between">
            <div className="flex gap-2">
              <Button
                type="submit"
                disabled={isLoading}
                className="bg-protec-red hover:bg-protec-red/90"
              >
                {isLoading ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                    Searching...
                  </>
                ) : (
                  <>
                    <Search className="w-4 h-4 mr-2" />
                    Search Alumni
                  </>
                )}
              </Button>
              
              {activeFilters.length > 0 && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={clearAllFilters}
                >
                  <X className="w-4 h-4 mr-2" />
                  Clear All
                </Button>
              )}
            </div>

            {onSaveSearch && activeFilters.length > 0 && (
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowSaveDialog(true)}
              >
                <Save className="w-4 h-4 mr-2" />
                Save Search
              </Button>
            )}
          </div>

          {/* Save Search Dialog */}
          {showSaveDialog && (
            <div className="space-y-2 p-4 border rounded-lg bg-muted/50">
              <Label>Save this search</Label>
              <div className="flex space-x-2">
                <Input
                  placeholder="Enter search name..."
                  value={saveSearchName}
                  onChange={(e) => setSaveSearchName(e.target.value)}
                />
                <Button onClick={handleSaveSearch}>Save</Button>
                <Button variant="outline" onClick={() => setShowSaveDialog(false)}>
                  Cancel
                </Button>
              </div>
            </div>
          )}
        </form>
      </CardContent>
    </Card>
  )
}
