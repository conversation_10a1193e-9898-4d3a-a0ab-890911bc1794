"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import Link from "next/link"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { api } from "@/components/providers/trpc-provider"
import { 
  MapPin, 
  Briefcase, 
  GraduationCap, 
  Users, 
  MessageSquare, 
  Heart,
  Calendar,
  ExternalLink,
  Mail,
  Linkedin,
  Twitter,
  Github,
  Globe,
  UserPlus,
  UserMinus,
  Edit,
  Share
} from "lucide-react"

interface AlumniProfileProps {
  alumni: {
    id: string
    name: string
    email: string
    photoUrl?: string | null
    bio?: string | null
    graduationYear: number
    programmes: string[]
    currentRole?: string | null
    skills: string[]
    province?: string | null
    city?: string | null
    socialLinks?: {
      linkedin?: string
      twitter?: string
      github?: string
      website?: string
    } | null
    privacy?: {
      showEmail?: boolean
      showPhone?: boolean
      showLocation?: boolean
      showConnections?: boolean
    } | null
    connections: Array<{
      id: string
      name: string
      photoUrl?: string | null
      currentRole?: string | null
      graduationYear: number
    }>
    posts: Array<{
      id: string
      content: string
      createdAt: Date
      likes: string[]
      _count: {
        comments: number
      }
    }>
    _count: {
      connections: number
      posts: number
      donations: number
    }
  }
}

export function AlumniProfile({ alumni }: AlumniProfileProps) {
  const { data: session } = useSession()
  const [isConnected, setIsConnected] = useState(false) // This should come from the data
  
  const connectMutation = api.alumni.connect.useMutation()
  const disconnectMutation = api.alumni.disconnect.useMutation()

  const isOwnProfile = session?.user?.email === alumni.email
  const privacy = alumni.privacy || {}

  const handleConnect = async () => {
    try {
      if (isConnected) {
        await disconnectMutation.mutateAsync({ alumniId: alumni.id })
        setIsConnected(false)
      } else {
        await connectMutation.mutateAsync({ alumniId: alumni.id })
        setIsConnected(true)
      }
    } catch (error) {
      console.error("Connection error:", error)
    }
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  return (
    <div className="space-y-6">
      {/* Profile Header */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row md:items-start md:space-x-6 space-y-4 md:space-y-0">
            <Avatar className="h-32 w-32 mx-auto md:mx-0">
              <AvatarImage src={alumni.photoUrl || ""} alt={alumni.name} />
              <AvatarFallback className="bg-protec-navy text-white text-2xl">
                {getInitials(alumni.name)}
              </AvatarFallback>
            </Avatar>

            <div className="flex-1 text-center md:text-left">
              <h1 className="text-3xl font-bold text-protec-navy mb-2">
                {alumni.name}
              </h1>
              
              {alumni.currentRole && (
                <div className="flex items-center justify-center md:justify-start space-x-2 text-lg text-muted-foreground mb-3">
                  <Briefcase className="h-5 w-5" />
                  <span>{alumni.currentRole}</span>
                </div>
              )}

              <div className="flex flex-wrap items-center justify-center md:justify-start gap-4 text-sm text-muted-foreground mb-4">
                <div className="flex items-center space-x-1">
                  <GraduationCap className="h-4 w-4" />
                  <span>Class of {alumni.graduationYear}</span>
                </div>
                
                {(privacy.showLocation !== false) && (alumni.city || alumni.province) && (
                  <div className="flex items-center space-x-1">
                    <MapPin className="h-4 w-4" />
                    <span>{[alumni.city, alumni.province].filter(Boolean).join(', ')}</span>
                  </div>
                )}

                {privacy.showEmail !== false && alumni.email && (
                  <div className="flex items-center space-x-1">
                    <Mail className="h-4 w-4" />
                    <span>{alumni.email}</span>
                  </div>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex flex-wrap items-center justify-center md:justify-start gap-3">
                {!isOwnProfile && (
                  <Button
                    onClick={handleConnect}
                    disabled={connectMutation.isLoading || disconnectMutation.isLoading}
                    className={isConnected ? "bg-gray-600 hover:bg-gray-700" : "bg-protec-red hover:bg-protec-red/90"}
                  >
                    {isConnected ? (
                      <>
                        <UserMinus className="mr-2 h-4 w-4" />
                        Disconnect
                      </>
                    ) : (
                      <>
                        <UserPlus className="mr-2 h-4 w-4" />
                        Connect
                      </>
                    )}
                  </Button>
                )}

                {isOwnProfile && (
                  <Button variant="outline" asChild>
                    <Link href="/profile/edit">
                      <Edit className="mr-2 h-4 w-4" />
                      Edit Profile
                    </Link>
                  </Button>
                )}

                <Button variant="outline">
                  <MessageSquare className="mr-2 h-4 w-4" />
                  Message
                </Button>

                <Button variant="outline" size="icon">
                  <Share className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid gap-6 md:grid-cols-3">
        {/* Main Content */}
        <div className="md:col-span-2 space-y-6">
          {/* About */}
          {alumni.bio && (
            <Card>
              <CardHeader>
                <CardTitle className="text-protec-navy">About</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground leading-relaxed">
                  {alumni.bio}
                </p>
              </CardContent>
            </Card>
          )}

          {/* Skills */}
          {alumni.skills.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-protec-navy">Skills & Expertise</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {alumni.skills.map((skill) => (
                    <Badge key={skill} variant="secondary" className="text-sm">
                      {skill}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Recent Posts */}
          {alumni.posts.length > 0 && (
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle className="text-protec-navy">Recent Posts</CardTitle>
                <Button variant="ghost" size="sm" asChild>
                  <Link href={`/profile/${alumni.id}/posts`}>
                    View All
                  </Link>
                </Button>
              </CardHeader>
              <CardContent className="space-y-4">
                {alumni.posts.slice(0, 3).map((post) => (
                  <div key={post.id} className="border-b pb-4 last:border-b-0">
                    <p className="text-sm text-muted-foreground mb-2">
                      {formatDate(post.createdAt)}
                    </p>
                    <p className="mb-3">{post.content}</p>
                    <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                      <div className="flex items-center space-x-1">
                        <Heart className="h-4 w-4" />
                        <span>{post.likes.length}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <MessageSquare className="h-4 w-4" />
                        <span>{post._count.comments}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Stats */}
          <Card>
            <CardHeader>
              <CardTitle className="text-protec-navy">Profile Stats</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Users className="h-4 w-4 text-protec-navy" />
                  <span className="text-sm">Connections</span>
                </div>
                <span className="font-semibold">{alumni._count.connections}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <MessageSquare className="h-4 w-4 text-protec-navy" />
                  <span className="text-sm">Posts</span>
                </div>
                <span className="font-semibold">{alumni._count.posts}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Heart className="h-4 w-4 text-protec-navy" />
                  <span className="text-sm">Donations</span>
                </div>
                <span className="font-semibold">{alumni._count.donations}</span>
              </div>
            </CardContent>
          </Card>

          {/* Social Links */}
          {alumni.socialLinks && Object.values(alumni.socialLinks).some(Boolean) && (
            <Card>
              <CardHeader>
                <CardTitle className="text-protec-navy">Connect</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {alumni.socialLinks.linkedin && (
                  <a
                    href={alumni.socialLinks.linkedin}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center space-x-2 text-sm text-muted-foreground hover:text-protec-navy transition-colors"
                  >
                    <Linkedin className="h-4 w-4" />
                    <span>LinkedIn</span>
                    <ExternalLink className="h-3 w-3" />
                  </a>
                )}
                {alumni.socialLinks.twitter && (
                  <a
                    href={alumni.socialLinks.twitter}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center space-x-2 text-sm text-muted-foreground hover:text-protec-navy transition-colors"
                  >
                    <Twitter className="h-4 w-4" />
                    <span>Twitter</span>
                    <ExternalLink className="h-3 w-3" />
                  </a>
                )}
                {alumni.socialLinks.github && (
                  <a
                    href={alumni.socialLinks.github}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center space-x-2 text-sm text-muted-foreground hover:text-protec-navy transition-colors"
                  >
                    <Github className="h-4 w-4" />
                    <span>GitHub</span>
                    <ExternalLink className="h-3 w-3" />
                  </a>
                )}
                {alumni.socialLinks.website && (
                  <a
                    href={alumni.socialLinks.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center space-x-2 text-sm text-muted-foreground hover:text-protec-navy transition-colors"
                  >
                    <Globe className="h-4 w-4" />
                    <span>Website</span>
                    <ExternalLink className="h-3 w-3" />
                  </a>
                )}
              </CardContent>
            </Card>
          )}

          {/* Programmes */}
          {alumni.programmes.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-protec-navy">PROTEC Programmes</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {alumni.programmes.map((programme) => (
                    <Badge key={programme} variant="outline" className="block text-center">
                      {programme}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Mutual Connections */}
          {privacy.showConnections !== false && alumni.connections.length > 0 && (
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle className="text-protec-navy">Connections</CardTitle>
                <Button variant="ghost" size="sm" asChild>
                  <Link href={`/profile/${alumni.id}/connections`}>
                    View All
                  </Link>
                </Button>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {alumni.connections.slice(0, 5).map((connection) => (
                    <div key={connection.id} className="flex items-center space-x-3">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={connection.photoUrl || ""} alt={connection.name} />
                        <AvatarFallback className="bg-protec-navy text-white text-xs">
                          {getInitials(connection.name)}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-protec-navy truncate">
                          {connection.name}
                        </p>
                        <p className="text-xs text-muted-foreground truncate">
                          {connection.currentRole || `Class of ${connection.graduationYear}`}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}
