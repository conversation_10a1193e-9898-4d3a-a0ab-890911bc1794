"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import { motion } from "framer-motion"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { api } from "@/components/providers/trpc-provider"
import { 
  Users, 
  TrendingUp, 
  Star, 
  UserPlus, 
  MessageCircle,
  Sparkles,
  Target,
  Briefcase,
  MapPin,
  GraduationCap,
  Heart,
  RefreshCw,
  Settings
} from "lucide-react"
import Link from "next/link"
import { toast } from "sonner"

interface RecommendationsProps {
  className?: string
}

export function Recommendations({ className }: RecommendationsProps) {
  const { data: session } = useSession()
  const [focusAreas, setFocusAreas] = useState<string[]>([])
  const [minScore, setMinScore] = useState(0.3)
  const [limit, setLimit] = useState(10)

  const { 
    data: recommendations, 
    isLoading: isLoadingRecommendations,
    refetch: refetchRecommendations
  } = api.alumni.getRecommendations.useQuery(
    { 
      limit, 
      minScore, 
      focusAreas: focusAreas as any,
      excludeConnected: true 
    },
    { enabled: !!session }
  )

  const { data: trending, isLoading: isLoadingTrending } = api.alumni.getTrending.useQuery(
    { limit: 5 },
    { enabled: !!session }
  )

  const { data: careerPath, isLoading: isLoadingCareer } = api.alumni.getCareerPathRecommendations.useQuery(
    { limit: 5 },
    { enabled: !!session }
  )

  const sendConnectionRequestMutation = api.alumni.sendConnectionRequest.useMutation({
    onSuccess: () => {
      toast.success("Connection request sent!")
      refetchRecommendations()
    },
    onError: (error) => {
      toast.error(error.message || "Failed to send connection request")
    }
  })

  const handleConnect = (alumniId: string) => {
    sendConnectionRequestMutation.mutate({ alumniId })
  }

  const handleFocusAreaChange = (area: string, checked: boolean) => {
    if (checked) {
      setFocusAreas(prev => [...prev, area])
    } else {
      setFocusAreas(prev => prev.filter(a => a !== area))
    }
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const getScoreColor = (score: number) => {
    if (score >= 0.8) return 'text-green-600 bg-green-100'
    if (score >= 0.6) return 'text-blue-600 bg-blue-100'
    if (score >= 0.4) return 'text-yellow-600 bg-yellow-100'
    return 'text-gray-600 bg-gray-100'
  }

  if (!session) {
    return (
      <Card className={className}>
        <CardContent className="pt-6 text-center">
          <Users className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium mb-2">Sign in for Recommendations</h3>
          <p className="text-muted-foreground">
            Get personalized alumni recommendations based on your profile and interests
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Personalized Recommendations */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <Sparkles className="w-5 h-5 text-protec-red" />
              <span>Recommended for You</span>
            </CardTitle>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => refetchRecommendations()}
                disabled={isLoadingRecommendations}
              >
                <RefreshCw className={`w-4 h-4 ${isLoadingRecommendations ? 'animate-spin' : ''}`} />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Filters */}
          <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-2">
              <Settings className="w-4 h-4" />
              <span className="font-medium text-sm">Recommendation Settings</span>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label>Focus Areas</Label>
                <div className="space-y-2">
                  {[
                    { value: 'skills', label: 'Skills' },
                    { value: 'industry', label: 'Industry' },
                    { value: 'location', label: 'Location' },
                    { value: 'interests', label: 'Interests' },
                    { value: 'career', label: 'Career Level' }
                  ].map((area) => (
                    <div key={area.value} className="flex items-center space-x-2">
                      <Checkbox
                        id={area.value}
                        checked={focusAreas.includes(area.value)}
                        onCheckedChange={(checked) => 
                          handleFocusAreaChange(area.value, checked as boolean)
                        }
                      />
                      <Label htmlFor={area.value} className="text-sm">
                        {area.label}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>
              
              <div className="space-y-2">
                <Label>Minimum Match Score</Label>
                <Select value={minScore.toString()} onValueChange={(value) => setMinScore(parseFloat(value))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0.2">20% - Show more results</SelectItem>
                    <SelectItem value="0.3">30% - Balanced</SelectItem>
                    <SelectItem value="0.5">50% - Good matches</SelectItem>
                    <SelectItem value="0.7">70% - Great matches only</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label>Number of Results</Label>
                <Select value={limit.toString()} onValueChange={(value) => setLimit(parseInt(value))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5">5 recommendations</SelectItem>
                    <SelectItem value="10">10 recommendations</SelectItem>
                    <SelectItem value="20">20 recommendations</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Recommendations List */}
          {isLoadingRecommendations ? (
            <div className="space-y-4">
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="animate-pulse flex items-center space-x-4 p-4 border rounded-lg">
                  <div className="w-12 h-12 bg-gray-200 rounded-full" />
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-3/4" />
                    <div className="h-3 bg-gray-200 rounded w-1/2" />
                  </div>
                  <div className="w-20 h-8 bg-gray-200 rounded" />
                </div>
              ))}
            </div>
          ) : recommendations && recommendations.length > 0 ? (
            <div className="space-y-4">
              {recommendations.map((rec, index) => (
                <motion.div
                  key={rec.alumniId}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-center justify-between p-4 border rounded-lg hover:shadow-md transition-shadow"
                >
                  <div className="flex items-center space-x-4 flex-1">
                    <Avatar className="h-12 w-12">
                      <AvatarImage src={rec.alumni?.photoUrl || undefined} />
                      <AvatarFallback className="bg-protec-navy text-white">
                        {getInitials(rec.alumni?.name || '')}
                      </AvatarFallback>
                    </Avatar>
                    
                    <div className="flex-1 min-w-0">
                      <Link 
                        href={`/profile/${rec.alumniId}`}
                        className="font-medium hover:text-protec-red transition-colors"
                      >
                        {rec.alumni?.name}
                      </Link>
                      <p className="text-sm text-muted-foreground">
                        {rec.alumni?.currentRole} {rec.alumni?.company && `at ${rec.alumni.company}`}
                      </p>
                      <div className="flex items-center space-x-2 mt-1">
                        <Badge 
                          variant="secondary" 
                          className={`text-xs ${getScoreColor(rec.score)}`}
                        >
                          {Math.round(rec.score * 100)}% match
                        </Badge>
                        {rec.reasons.slice(0, 2).map((reason, i) => (
                          <Badge key={i} variant="outline" className="text-xs">
                            {reason}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleConnect(rec.alumniId)}
                      disabled={sendConnectionRequestMutation.isLoading}
                    >
                      <UserPlus className="w-4 h-4 mr-1" />
                      Connect
                    </Button>
                    <Button size="sm" variant="ghost" asChild>
                      <Link href={`/messages?to=${rec.alumniId}`}>
                        <MessageCircle className="w-4 h-4" />
                      </Link>
                    </Button>
                  </div>
                </motion.div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Target className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No Recommendations Found</h3>
              <p className="text-muted-foreground mb-4">
                Try adjusting your filters or complete your profile for better matches
              </p>
              <Button variant="outline" asChild>
                <Link href="/profile/edit">Complete Profile</Link>
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Trending Alumni */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TrendingUp className="w-5 h-5 text-green-600" />
            <span>Trending Alumni</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoadingTrending ? (
            <div className="space-y-3">
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="animate-pulse flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gray-200 rounded-full" />
                  <div className="flex-1 space-y-1">
                    <div className="h-3 bg-gray-200 rounded w-3/4" />
                    <div className="h-2 bg-gray-200 rounded w-1/2" />
                  </div>
                </div>
              ))}
            </div>
          ) : trending && trending.length > 0 ? (
            <div className="space-y-3">
              {trending.map((alumni) => (
                <div key={alumni.id} className="flex items-center space-x-3 p-2 rounded hover:bg-gray-50">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={alumni.photoUrl || undefined} />
                    <AvatarFallback className="bg-protec-navy text-white text-sm">
                      {getInitials(alumni.name)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <Link 
                      href={`/profile/${alumni.id}`}
                      className="font-medium text-sm hover:text-protec-red transition-colors"
                    >
                      {alumni.name}
                    </Link>
                    <p className="text-xs text-muted-foreground">
                      {alumni.currentRole} • {alumni._count.connections} connections
                    </p>
                  </div>
                  <Button size="sm" variant="ghost" onClick={() => handleConnect(alumni.id)}>
                    <UserPlus className="w-3 h-3" />
                  </Button>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-sm text-muted-foreground text-center py-4">
              No trending alumni at the moment
            </p>
          )}
        </CardContent>
      </Card>

      {/* Career Path Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Briefcase className="w-5 h-5 text-blue-600" />
            <span>Similar Career Paths</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoadingCareer ? (
            <div className="space-y-3">
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="animate-pulse flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gray-200 rounded-full" />
                  <div className="flex-1 space-y-1">
                    <div className="h-3 bg-gray-200 rounded w-3/4" />
                    <div className="h-2 bg-gray-200 rounded w-1/2" />
                  </div>
                </div>
              ))}
            </div>
          ) : careerPath && careerPath.length > 0 ? (
            <div className="space-y-3">
              {careerPath.map((alumni) => (
                <div key={alumni.id} className="flex items-center space-x-3 p-2 rounded hover:bg-gray-50">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={alumni.photoUrl || undefined} />
                    <AvatarFallback className="bg-protec-navy text-white text-sm">
                      {getInitials(alumni.name)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <Link 
                      href={`/profile/${alumni.id}`}
                      className="font-medium text-sm hover:text-protec-red transition-colors"
                    >
                      {alumni.name}
                    </Link>
                    <p className="text-xs text-muted-foreground">
                      {alumni.currentRole} • Class of {alumni.graduationYear}
                    </p>
                  </div>
                  <Button size="sm" variant="ghost" onClick={() => handleConnect(alumni.id)}>
                    <UserPlus className="w-3 h-3" />
                  </Button>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-sm text-muted-foreground text-center py-4">
              No similar career paths found
            </p>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
