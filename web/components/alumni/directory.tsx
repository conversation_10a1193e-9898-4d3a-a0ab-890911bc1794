"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Skeleton } from "@/components/ui/skeleton"
import { AlumniCard } from "@/components/alumni/alumni-card"
import {
  Search,
  Filter,
  Users,
  Loader2
} from "lucide-react"

interface AlumniFilters {
  query?: string
  graduationYear?: number
  programme?: string
  province?: string
  city?: string
  skills?: string[]
}

interface AlumniDirectoryProps {
  initialFilters?: AlumniFilters
}

// Mock data for demonstration
const mockAlumni = [
  {
    id: "1",
    name: "<PERSON>",
    photoUrl: "/avatars/sarah.jpg",
    bio: "Software Engineer passionate about AI and machine learning",
    graduationYear: 2018,
    programmes: ["Computer Science"],
    currentRole: "Senior Software Engineer at Google",
    skills: ["React", "Python", "Machine Learning", "TypeScript"],
    province: "Western Cape",
    city: "Cape Town",
    _count: { connections: 156 }
  },
  {
    id: "2",
    name: "<PERSON>",
    photoUrl: "/avatars/michael.jpg",
    bio: "Data Scientist helping businesses make data-driven decisions",
    graduationYear: 2016,
    programmes: ["Mathematics"],
    currentRole: "Data Scientist at Amazon",
    skills: ["Python", "SQL", "Machine Learning", "Statistics"],
    province: "Gauteng",
    city: "Johannesburg",
    _count: { connections: 234 }
  },
  {
    id: "3",
    name: "Priya Patel",
    photoUrl: "/avatars/priya.jpg",
    bio: "Product Manager focused on user experience and innovation",
    graduationYear: 2019,
    programmes: ["Engineering"],
    currentRole: "Product Manager at Microsoft",
    skills: ["Product Management", "UX Design", "Analytics", "Strategy"],
    province: "Western Cape",
    city: "Cape Town",
    _count: { connections: 189 }
  },
  {
    id: "4",
    name: "James Okoye",
    photoUrl: "/avatars/james.jpg",
    bio: "Full-stack developer building scalable web applications",
    graduationYear: 2020,
    programmes: ["Computer Science"],
    currentRole: "Senior Developer at Meta",
    skills: ["JavaScript", "Node.js", "React", "GraphQL"],
    province: "Gauteng",
    city: "Pretoria",
    _count: { connections: 145 }
  },
  {
    id: "5",
    name: "Aisha Patel",
    photoUrl: "/avatars/aisha.jpg",
    bio: "Renewable energy engineer working on sustainable solutions",
    graduationYear: 2017,
    programmes: ["Electrical Engineering"],
    currentRole: "Renewable Energy Consultant",
    skills: ["Solar Energy", "Project Management", "Sustainability", "Engineering"],
    province: "KwaZulu-Natal",
    city: "Durban",
    _count: { connections: 167 }
  },
  {
    id: "6",
    name: "Thabo Mthembu",
    photoUrl: "/avatars/thabo.jpg",
    bio: "Biomedical engineer developing medical devices for rural communities",
    graduationYear: 2015,
    programmes: ["Biomedical Engineering"],
    currentRole: "Biomedical Engineer at UCT",
    skills: ["Medical Devices", "Research", "Innovation", "Healthcare"],
    province: "Western Cape",
    city: "Cape Town",
    _count: { connections: 198 }
  }
]

export function AlumniDirectory({ initialFilters = {} }: AlumniDirectoryProps) {
  const [filters, setFilters] = useState<AlumniFilters>(initialFilters)
  const [searchQuery, setSearchQuery] = useState(filters.query || "")
  const [showFilters, setShowFilters] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  // For demo purposes, we'll use mock data instead of the API
  const allAlumni = mockAlumni.filter(alumni => {
    const query = filters.query?.toLowerCase()
    if (query && !alumni.name.toLowerCase().includes(query) &&
        !alumni.currentRole?.toLowerCase().includes(query) &&
        !alumni.skills.some(skill => skill.toLowerCase().includes(query))) {
      return false
    }
    if (filters.graduationYear && alumni.graduationYear !== filters.graduationYear) {
      return false
    }
    if (filters.province && alumni.province !== filters.province) {
      return false
    }
    if (filters.city && alumni.city !== filters.city) {
      return false
    }
    return true
  })

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setTimeout(() => {
      setFilters(prev => ({ ...prev, query: searchQuery }))
      setIsLoading(false)
    }, 500) // Simulate loading
  }

  const handleFilterChange = (newFilters: Partial<AlumniFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }))
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-protec-navy">
            Alumni Directory
          </h1>
          <p className="text-muted-foreground">
            Connect with 5,000+ PROTEC graduates worldwide
          </p>
        </div>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-6">
          <form onSubmit={handleSearch} className="space-y-4">
            <div className="flex flex-col space-y-4 md:flex-row md:space-y-0 md:space-x-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search by name, company, or skills..."
                    className="pl-10"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
              </div>
              <Button
                type="submit"
                disabled={isLoading}
                className="bg-protec-red hover:bg-protec-red/90"
              >
                {isLoading ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Search className="mr-2 h-4 w-4" />
                )}
                Search
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
                className="border-protec-navy text-protec-navy hover:bg-protec-navy hover:text-white"
              >
                <Filter className="mr-2 h-4 w-4" />
                Filters
              </Button>
            </div>
          </form>

          {/* Advanced Filters */}
          {showFilters && (
            <div className="mt-6 pt-6 border-t">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">Graduation Year</label>
                  <Input
                    type="number"
                    placeholder="e.g. 2020"
                    min="1982"
                    max={new Date().getFullYear()}
                    value={filters.graduationYear || ""}
                    onChange={(e) => handleFilterChange({ 
                      graduationYear: e.target.value ? parseInt(e.target.value) : undefined 
                    })}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium mb-2 block">Province</label>
                  <Input
                    placeholder="e.g. Western Cape"
                    value={filters.province || ""}
                    onChange={(e) => handleFilterChange({ province: e.target.value || undefined })}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium mb-2 block">City</label>
                  <Input
                    placeholder="e.g. Cape Town"
                    value={filters.city || ""}
                    onChange={(e) => handleFilterChange({ city: e.target.value || undefined })}
                  />
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Results */}
      {isLoading ? (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {Array.from({ length: 6 }).map((_, i) => (
            <Card key={i}>
              <CardHeader className="pb-4">
                <div className="flex items-center space-x-4">
                  <Skeleton className="h-16 w-16 rounded-full" />
                  <div className="flex-1 space-y-2">
                    <Skeleton className="h-4 w-3/4" />
                    <Skeleton className="h-3 w-1/2" />
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <Skeleton className="h-3 w-1/3" />
                  <Skeleton className="h-3 w-1/4" />
                </div>
                <div className="flex space-x-2">
                  <Skeleton className="h-6 w-16" />
                  <Skeleton className="h-6 w-20" />
                  <Skeleton className="h-6 w-14" />
                </div>
                <div className="flex justify-between items-center">
                  <Skeleton className="h-3 w-1/3" />
                  <Skeleton className="h-8 w-20" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : allAlumni.length > 0 ? (
        <>
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {allAlumni.map((person) => (
              <AlumniCard key={person.id} alumni={person} />
            ))}
          </div>

          {/* Results Summary */}
          <div className="text-center py-8">
            <p className="text-muted-foreground">
              Showing {allAlumni.length} alumni
              {filters.query && ` matching "${filters.query}"`}
            </p>
          </div>
        </>
      ) : (
        <div className="text-center py-12">
          <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium text-protec-navy mb-2">No alumni found</h3>
          <p className="text-muted-foreground mb-4">
            Try adjusting your search criteria or filters
          </p>
          <Button 
            variant="outline" 
            onClick={() => {
              setFilters({})
              setSearchQuery("")
            }}
          >
            Clear Filters
          </Button>
        </div>
      )}
    </div>
  )
}


