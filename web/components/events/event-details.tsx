"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import Link from "next/link"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { api } from "@/components/providers/trpc-provider"
import { 
  Calendar, 
  MapPin, 
  Users, 
  Clock,
  Video,
  ExternalLink,
  Share,
  Edit,
  Trash2,
  UserCheck,
  UserX,
  UserPlus,
  Loader2
} from "lucide-react"

interface EventDetailsProps {
  event: {
    id: string
    title: string
    description: string
    category: string
    startTime: Date
    endTime: Date
    location: any
    organizer: {
      id: string
      name: string
      photoUrl?: string | null
      currentRole?: string | null
      graduationYear: number
    }
    rsvps: Array<{
      id: string
      status: string
      alumni: {
        id: string
        name: string
        photoUrl?: string | null
        currentRole?: string | null
      }
    }>
  }
}

const categoryColors = {
  networking: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
  workshop: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",
  conference: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
  social: "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300",
  career: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
  mentorship: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300"
}

export function EventDetails({ event }: EventDetailsProps) {
  const { data: session } = useSession()
  const [rsvpStatus, setRsvpStatus] = useState<string | null>(null)
  
  const rsvpMutation = api.events.rsvp.useMutation()
  const deleteMutation = api.events.delete.useMutation()

  const isOrganizer = session?.user?.email && event.organizer.id === session.user.id
  const userRsvp = event.rsvps.find(rsvp => rsvp.alumni.id === session?.user?.id)
  const currentRsvpStatus = userRsvp?.status || rsvpStatus

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const formatTime = (date: Date) => {
    return new Date(date).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    })
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const handleRsvp = async (status: 'attending' | 'not_attending' | 'maybe') => {
    try {
      await rsvpMutation.mutateAsync({
        eventId: event.id,
        status
      })
      setRsvpStatus(status)
    } catch (error) {
      console.error("RSVP error:", error)
    }
  }

  const handleDelete = async () => {
    if (confirm("Are you sure you want to delete this event?")) {
      try {
        await deleteMutation.mutateAsync({ id: event.id })
        // Redirect to events page
        window.location.href = "/events"
      } catch (error) {
        console.error("Delete error:", error)
      }
    }
  }

  const isUpcoming = new Date(event.startTime) > new Date()
  const isPast = new Date(event.endTime) < new Date()
  const attendingCount = event.rsvps.filter(rsvp => rsvp.status === 'attending').length

  return (
    <div className="space-y-6">
      {/* Event Header */}
      <Card>
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Badge 
                variant="secondary" 
                className={categoryColors[event.category as keyof typeof categoryColors]}
              >
                {event.category}
              </Badge>
              {isPast && (
                <Badge variant="outline" className="text-muted-foreground">
                  Past Event
                </Badge>
              )}
              {isUpcoming && (
                <Badge variant="outline" className="text-green-600 border-green-600">
                  Upcoming
                </Badge>
              )}
            </div>

            <h1 className="text-3xl font-bold text-protec-navy">
              {event.title}
            </h1>

            <p className="text-lg text-muted-foreground leading-relaxed">
              {event.description}
            </p>

            {/* Action Buttons */}
            <div className="flex flex-wrap items-center gap-3">
              {session && !isOrganizer && isUpcoming && (
                <div className="flex items-center space-x-2">
                  <Button
                    onClick={() => handleRsvp('attending')}
                    disabled={rsvpMutation.isLoading}
                    variant={currentRsvpStatus === 'attending' ? 'default' : 'outline'}
                    className={currentRsvpStatus === 'attending' ? 'bg-green-600 hover:bg-green-700' : ''}
                  >
                    {rsvpMutation.isLoading ? (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      <UserCheck className="mr-2 h-4 w-4" />
                    )}
                    Attending
                  </Button>
                  
                  <Button
                    onClick={() => handleRsvp('maybe')}
                    disabled={rsvpMutation.isLoading}
                    variant={currentRsvpStatus === 'maybe' ? 'default' : 'outline'}
                    className={currentRsvpStatus === 'maybe' ? 'bg-yellow-600 hover:bg-yellow-700' : ''}
                  >
                    <UserPlus className="mr-2 h-4 w-4" />
                    Maybe
                  </Button>
                  
                  <Button
                    onClick={() => handleRsvp('not_attending')}
                    disabled={rsvpMutation.isLoading}
                    variant={currentRsvpStatus === 'not_attending' ? 'default' : 'outline'}
                    className={currentRsvpStatus === 'not_attending' ? 'bg-red-600 hover:bg-red-700' : ''}
                  >
                    <UserX className="mr-2 h-4 w-4" />
                    Can't Attend
                  </Button>
                </div>
              )}

              {isOrganizer && (
                <div className="flex items-center space-x-2">
                  <Button variant="outline" asChild>
                    <Link href={`/events/${event.id}/edit`}>
                      <Edit className="mr-2 h-4 w-4" />
                      Edit Event
                    </Link>
                  </Button>
                  
                  <Button 
                    variant="outline" 
                    onClick={handleDelete}
                    disabled={deleteMutation.isLoading}
                    className="text-red-600 hover:text-red-700"
                  >
                    {deleteMutation.isLoading ? (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      <Trash2 className="mr-2 h-4 w-4" />
                    )}
                    Delete
                  </Button>
                </div>
              )}

              <Button variant="outline" size="icon">
                <Share className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid gap-6 md:grid-cols-3">
        {/* Main Content */}
        <div className="md:col-span-2 space-y-6">
          {/* Event Details */}
          <Card>
            <CardHeader>
              <CardTitle className="text-protec-navy">Event Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center space-x-3">
                  <Calendar className="h-5 w-5 text-protec-navy" />
                  <div>
                    <p className="font-medium">{formatDate(event.startTime)}</p>
                    <p className="text-sm text-muted-foreground">Date</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Clock className="h-5 w-5 text-protec-navy" />
                  <div>
                    <p className="font-medium">
                      {formatTime(event.startTime)} - {formatTime(event.endTime)}
                    </p>
                    <p className="text-sm text-muted-foreground">Time</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  {event.location.type === 'virtual' ? (
                    <Video className="h-5 w-5 text-protec-navy" />
                  ) : (
                    <MapPin className="h-5 w-5 text-protec-navy" />
                  )}
                  <div>
                    <p className="font-medium">
                      {event.location.type === 'virtual' 
                        ? 'Virtual Event' 
                        : event.location.address || event.location.city || 'Physical Location'
                      }
                    </p>
                    <p className="text-sm text-muted-foreground">Location</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Users className="h-5 w-5 text-protec-navy" />
                  <div>
                    <p className="font-medium">{attendingCount} attending</p>
                    <p className="text-sm text-muted-foreground">Attendees</p>
                  </div>
                </div>
              </div>

              {event.location.type === 'virtual' && event.location.virtualLink && (
                <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                  <p className="text-sm font-medium text-blue-900 mb-2">Join Virtual Event</p>
                  <Button variant="outline" size="sm" asChild>
                    <a href={event.location.virtualLink} target="_blank" rel="noopener noreferrer">
                      <ExternalLink className="mr-2 h-4 w-4" />
                      Join Meeting
                    </a>
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Attendees */}
          {event.rsvps.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-protec-navy">
                  Attendees ({attendingCount})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {event.rsvps
                    .filter(rsvp => rsvp.status === 'attending')
                    .map((rsvp) => (
                      <div key={rsvp.id} className="flex items-center space-x-3">
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={rsvp.alumni.photoUrl || ""} alt={rsvp.alumni.name} />
                          <AvatarFallback className="bg-protec-navy text-white">
                            {getInitials(rsvp.alumni.name)}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <p className="font-medium text-protec-navy truncate">
                            {rsvp.alumni.name}
                          </p>
                          <p className="text-sm text-muted-foreground truncate">
                            {rsvp.alumni.currentRole || "PROTEC Alumni"}
                          </p>
                        </div>
                      </div>
                    ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Organizer */}
          <Card>
            <CardHeader>
              <CardTitle className="text-protec-navy">Organizer</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-3">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={event.organizer.photoUrl || ""} alt={event.organizer.name} />
                  <AvatarFallback className="bg-protec-navy text-white">
                    {getInitials(event.organizer.name)}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <p className="font-medium text-protec-navy">
                    {event.organizer.name}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {event.organizer.currentRole || `Class of ${event.organizer.graduationYear}`}
                  </p>
                </div>
              </div>
              
              <Button variant="outline" size="sm" className="w-full mt-4" asChild>
                <Link href={`/profile/${event.organizer.id}`}>
                  View Profile
                </Link>
              </Button>
            </CardContent>
          </Card>

          {/* RSVP Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="text-protec-navy">RSVP Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">Attending</span>
                <Badge variant="secondary" className="bg-green-100 text-green-800">
                  {event.rsvps.filter(r => r.status === 'attending').length}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Maybe</span>
                <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                  {event.rsvps.filter(r => r.status === 'maybe').length}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Can't Attend</span>
                <Badge variant="secondary" className="bg-red-100 text-red-800">
                  {event.rsvps.filter(r => r.status === 'not_attending').length}
                </Badge>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
