"use client"

import { useState } from "react"
import Link from "next/link"
import { useInfiniteQuery } from "@tanstack/react-query"
import { api } from "@/components/providers/trpc-provider"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Skeleton } from "@/components/ui/skeleton"
import { 
  Calendar, 
  MapPin, 
  Users, 
  Clock,
  Video,
  ExternalLink,
  Loader2
} from "lucide-react"

interface EventFilters {
  category?: string
  startDate?: Date
  endDate?: Date
  location?: string
}

interface EventsListProps {
  filters: EventFilters
}

const categoryColors = {
  networking: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
  workshop: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",
  conference: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
  social: "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300",
  career: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
  mentorship: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300"
}

export function EventsList({ filters }: EventsListProps) {
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    error
  } = api.events.getAll.useInfiniteQuery(
    {
      ...filters,
      limit: 12,
    },
    {
      getNextPageParam: (lastPage) => lastPage.nextCursor,
    }
  )

  const allEvents = data?.pages.flatMap(page => page.events) ?? []

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }

  const formatTime = (date: Date) => {
    return new Date(date).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    })
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600 mb-4">Failed to load events</p>
        <Button onClick={() => window.location.reload()}>
          Try Again
        </Button>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        {Array.from({ length: 6 }).map((_, i) => (
          <Card key={i}>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="space-y-2 flex-1">
                  <Skeleton className="h-6 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                </div>
                <Skeleton className="h-6 w-20" />
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-4">
                <Skeleton className="h-10 w-10 rounded-full" />
                <div className="space-y-1">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-3 w-24" />
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-4 w-20" />
                </div>
                <Skeleton className="h-8 w-24" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (allEvents.length === 0) {
    return (
      <div className="text-center py-12">
        <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
        <h3 className="text-lg font-medium text-protec-navy mb-2">No events found</h3>
        <p className="text-muted-foreground mb-4">
          Try adjusting your filters or check back later for new events
        </p>
        <Button variant="outline" onClick={() => window.location.reload()}>
          Refresh
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {allEvents.map((event) => (
        <EventCard key={event.id} event={event} />
      ))}

      {hasNextPage && (
        <div className="text-center">
          <Button 
            variant="outline" 
            size="lg"
            onClick={() => fetchNextPage()}
            disabled={isFetchingNextPage}
          >
            {isFetchingNextPage ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Loading...
              </>
            ) : (
              "Load More Events"
            )}
          </Button>
        </div>
      )}
    </div>
  )
}

interface EventCardProps {
  event: {
    id: string
    title: string
    description: string
    category: string
    startTime: Date
    endTime: Date
    location: any
    organizer: {
      id: string
      name: string
      photoUrl?: string | null
      currentRole?: string | null
    }
    _count: {
      rsvps: number
    }
  }
}

function EventCard({ event }: EventCardProps) {
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric'
    })
  }

  const formatTime = (date: Date) => {
    return new Date(date).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    })
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const isUpcoming = new Date(event.startTime) > new Date()
  const isPast = new Date(event.endTime) < new Date()

  return (
    <Card className="group hover:shadow-lg transition-all">
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              <Badge 
                variant="secondary" 
                className={categoryColors[event.category as keyof typeof categoryColors]}
              >
                {event.category}
              </Badge>
              {isPast && (
                <Badge variant="outline" className="text-muted-foreground">
                  Past Event
                </Badge>
              )}
            </div>
            <CardTitle className="text-xl group-hover:text-protec-red transition-colors">
              <Link href={`/events/${event.id}`}>
                {event.title}
              </Link>
            </CardTitle>
            <p className="text-muted-foreground mt-2 line-clamp-2">
              {event.description}
            </p>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Organizer */}
        <div className="flex items-center space-x-3">
          <Avatar className="h-10 w-10">
            <AvatarImage src={event.organizer.photoUrl || ""} alt={event.organizer.name} />
            <AvatarFallback className="bg-protec-navy text-white">
              {getInitials(event.organizer.name)}
            </AvatarFallback>
          </Avatar>
          <div>
            <p className="font-medium text-sm">{event.organizer.name}</p>
            <p className="text-xs text-muted-foreground">
              {event.organizer.currentRole || "Event Organizer"}
            </p>
          </div>
        </div>

        {/* Event Details */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div className="flex items-center space-x-2 text-muted-foreground">
            <Calendar className="h-4 w-4" />
            <span>{formatDate(event.startTime)}</span>
          </div>
          
          <div className="flex items-center space-x-2 text-muted-foreground">
            <Clock className="h-4 w-4" />
            <span>{formatTime(event.startTime)} - {formatTime(event.endTime)}</span>
          </div>

          <div className="flex items-center space-x-2 text-muted-foreground">
            {event.location.type === 'virtual' ? (
              <>
                <Video className="h-4 w-4" />
                <span>Virtual Event</span>
              </>
            ) : (
              <>
                <MapPin className="h-4 w-4" />
                <span>
                  {event.location.city || event.location.address || 'Physical Location'}
                </span>
              </>
            )}
          </div>

          <div className="flex items-center space-x-2 text-muted-foreground">
            <Users className="h-4 w-4" />
            <span>{event._count.rsvps} attending</span>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-between pt-2">
          <div className="flex items-center space-x-2">
            {isUpcoming && (
              <Badge variant="outline" className="text-green-600 border-green-600">
                Upcoming
              </Badge>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" asChild>
              <Link href={`/events/${event.id}`}>
                View Details
                <ExternalLink className="ml-2 h-3 w-3" />
              </Link>
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
