"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { 
  Clock, 
  Calendar, 
  AlertTriangle, 
  CheckCircle,
  Users,
  MapPin,
  Edit,
  Eye
} from "lucide-react"

interface UpcomingDeadlinesProps {
  className?: string
}

export function UpcomingDeadlines({ className }: UpcomingDeadlinesProps) {
  // Mock data - replace with real API calls
  const upcomingDeadlines = [
    {
      id: '1',
      eventTitle: 'Annual Alumni Gala',
      deadline: '2024-04-10',
      deadlineType: 'Registration Closes',
      daysLeft: 5,
      urgency: 'high',
      status: 'active',
      details: {
        currentRegistrations: 156,
        capacity: 200,
        location: 'Cape Town Convention Centre'
      }
    },
    {
      id: '2',
      eventTitle: 'Tech Career Workshop',
      deadline: '2024-04-08',
      deadlineType: 'Speaker Confirmation',
      daysLeft: 3,
      urgency: 'critical',
      status: 'pending',
      details: {
        currentRegistrations: 45,
        capacity: 100,
        location: 'PROTEC Campus'
      }
    },
    {
      id: '3',
      eventTitle: 'Networking Mixer',
      deadline: '2024-04-12',
      deadlineType: 'Venue Setup',
      daysLeft: 7,
      urgency: 'medium',
      status: 'in-progress',
      details: {
        currentRegistrations: 67,
        capacity: 80,
        location: 'V&A Waterfront'
      }
    },
    {
      id: '4',
      eventTitle: 'Entrepreneurship Panel',
      deadline: '2024-04-15',
      deadlineType: 'Marketing Campaign Launch',
      daysLeft: 10,
      urgency: 'low',
      status: 'scheduled',
      details: {
        currentRegistrations: 23,
        capacity: 150,
        location: 'Online'
      }
    },
    {
      id: '5',
      eventTitle: 'Alumni Reunion',
      deadline: '2024-04-06',
      deadlineType: 'Catering Confirmation',
      daysLeft: 1,
      urgency: 'critical',
      status: 'overdue',
      details: {
        currentRegistrations: 89,
        capacity: 120,
        location: 'PROTEC Alumni Hall'
      }
    },
    {
      id: '6',
      eventTitle: 'Leadership Seminar',
      deadline: '2024-04-20',
      deadlineType: 'Registration Opens',
      daysLeft: 15,
      urgency: 'low',
      status: 'scheduled',
      details: {
        currentRegistrations: 0,
        capacity: 75,
        location: 'Business District'
      }
    }
  ]

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'critical':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'low':
        return 'bg-green-100 text-green-800 border-green-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'in-progress':
        return 'bg-blue-100 text-blue-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'overdue':
        return 'bg-red-100 text-red-800'
      case 'scheduled':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'in-progress':
        return <Clock className="h-4 w-4 text-blue-600" />
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-600" />
      case 'overdue':
        return <AlertTriangle className="h-4 w-4 text-red-600" />
      case 'scheduled':
        return <Calendar className="h-4 w-4 text-gray-600" />
      default:
        return <Clock className="h-4 w-4 text-gray-600" />
    }
  }

  const getDaysLeftText = (daysLeft: number, status: string) => {
    if (status === 'overdue') {
      return `${Math.abs(daysLeft)} days overdue`
    }
    if (daysLeft === 0) {
      return 'Due today'
    }
    if (daysLeft === 1) {
      return 'Due tomorrow'
    }
    return `${daysLeft} days left`
  }

  // Sort by urgency and days left
  const sortedDeadlines = [...upcomingDeadlines].sort((a, b) => {
    const urgencyOrder = { 'critical': 0, 'high': 1, 'medium': 2, 'low': 3 }
    const urgencyDiff = urgencyOrder[a.urgency as keyof typeof urgencyOrder] - urgencyOrder[b.urgency as keyof typeof urgencyOrder]
    if (urgencyDiff !== 0) return urgencyDiff
    return a.daysLeft - b.daysLeft
  })

  const criticalDeadlines = sortedDeadlines.filter(d => d.urgency === 'critical' || d.daysLeft <= 3)
  const upcomingCount = sortedDeadlines.filter(d => d.daysLeft > 0).length
  const overdueCount = sortedDeadlines.filter(d => d.status === 'overdue').length

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Deadline Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-blue-500" />
              <div>
                <div className="text-2xl font-bold">{sortedDeadlines.length}</div>
                <div className="text-sm text-muted-foreground">Total Deadlines</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-4 w-4 text-red-500" />
              <div>
                <div className="text-2xl font-bold">{criticalDeadlines.length}</div>
                <div className="text-sm text-muted-foreground">Critical</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-green-500" />
              <div>
                <div className="text-2xl font-bold">{upcomingCount}</div>
                <div className="text-sm text-muted-foreground">Upcoming</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-4 w-4 text-orange-500" />
              <div>
                <div className="text-2xl font-bold">{overdueCount}</div>
                <div className="text-sm text-muted-foreground">Overdue</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Critical Deadlines Alert */}
      {criticalDeadlines.length > 0 && (
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-red-800">
              <AlertTriangle className="h-5 w-5" />
              <span>Critical Deadlines Requiring Immediate Attention</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {criticalDeadlines.slice(0, 3).map((deadline) => (
                <div key={deadline.id} className="flex items-center justify-between p-3 bg-white rounded-lg border">
                  <div className="flex items-center space-x-3">
                    {getStatusIcon(deadline.status)}
                    <div>
                      <div className="font-medium">{deadline.eventTitle}</div>
                      <div className="text-sm text-muted-foreground">{deadline.deadlineType}</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium text-red-600">
                      {getDaysLeftText(deadline.daysLeft, deadline.status)}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {new Date(deadline.deadline).toLocaleDateString()}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* All Deadlines */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Calendar className="h-5 w-5" />
            <span>All Upcoming Deadlines</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {sortedDeadlines.map((deadline) => (
              <div key={deadline.id} className={`p-4 border rounded-lg ${getUrgencyColor(deadline.urgency)}`}>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <h3 className="font-semibold">{deadline.eventTitle}</h3>
                      <Badge 
                        variant="secondary" 
                        className={`text-xs ${getStatusColor(deadline.status)}`}
                      >
                        <div className="flex items-center space-x-1">
                          {getStatusIcon(deadline.status)}
                          <span>{deadline.status.replace('-', ' ')}</span>
                        </div>
                      </Badge>
                      <Badge 
                        variant="secondary" 
                        className={`text-xs ${getUrgencyColor(deadline.urgency)}`}
                      >
                        {deadline.urgency} priority
                      </Badge>
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex items-center space-x-4 text-sm">
                        <div className="flex items-center space-x-1">
                          <Clock className="h-4 w-4" />
                          <span className="font-medium">{deadline.deadlineType}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-4 w-4" />
                          <span>{new Date(deadline.deadline).toLocaleDateString()}</span>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                        <div className="flex items-center space-x-1">
                          <Users className="h-4 w-4" />
                          <span>{deadline.details.currentRegistrations}/{deadline.details.capacity} registered</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <MapPin className="h-4 w-4" />
                          <span>{deadline.details.location}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <div className="text-right">
                      <div className={`font-medium ${
                        deadline.status === 'overdue' ? 'text-red-600' : 
                        deadline.daysLeft <= 3 ? 'text-orange-600' : 'text-gray-600'
                      }`}>
                        {getDaysLeftText(deadline.daysLeft, deadline.status)}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {deadline.daysLeft > 0 ? 'remaining' : ''}
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Button size="sm" variant="ghost">
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button size="sm" variant="ghost">
                        <Edit className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Deadline Management Tips */}
      <Card>
        <CardHeader>
          <CardTitle>Deadline Management Tips</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-center space-x-2 mb-2">
                <Clock className="h-4 w-4 text-blue-600" />
                <span className="font-medium text-blue-800">Stay Ahead</span>
              </div>
              <p className="text-sm text-blue-700">
                Set reminders 1-2 weeks before critical deadlines to ensure you have enough time for preparation and coordination.
              </p>
            </div>
            
            <div className="p-4 bg-green-50 rounded-lg border border-green-200">
              <div className="flex items-center space-x-2 mb-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span className="font-medium text-green-800">Prioritize</span>
              </div>
              <p className="text-sm text-green-700">
                Focus on critical and high-priority deadlines first. Delegate or reschedule lower-priority items if necessary.
              </p>
            </div>
            
            <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
              <div className="flex items-center space-x-2 mb-2">
                <AlertTriangle className="h-4 w-4 text-yellow-600" />
                <span className="font-medium text-yellow-800">Communicate</span>
              </div>
              <p className="text-sm text-yellow-700">
                Keep stakeholders informed about deadline status and any potential delays. Early communication prevents last-minute issues.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
