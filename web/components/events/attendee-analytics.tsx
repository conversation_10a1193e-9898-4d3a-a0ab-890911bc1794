"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { 
  Users, 
  TrendingUp, 
  MapPin, 
  GraduationCap,
  Briefcase,
  Calendar,
  Award,
  Target
} from "lucide-react"

interface AttendeeAnalyticsProps {
  className?: string
}

export function AttendeeAnalytics({ className }: AttendeeAnalyticsProps) {
  // Mock data - replace with real API calls
  const attendeeStats = {
    totalAttendees: 342,
    newAttendees: 89,
    returningAttendees: 253,
    averageAge: 28,
    genderDistribution: {
      male: 58,
      female: 42
    }
  }

  const demographicBreakdown = [
    { category: 'Graduation Year', data: [
      { label: '2020-2024', count: 125, percentage: 36.5 },
      { label: '2015-2019', count: 98, percentage: 28.7 },
      { label: '2010-2014', count: 76, percentage: 22.2 },
      { label: '2005-2009', count: 43, percentage: 12.6 }
    ]},
    { category: 'Industry', data: [
      { label: 'Technology', count: 156, percentage: 45.6 },
      { label: 'Finance', count: 89, percentage: 26.0 },
      { label: 'Engineering', count: 67, percentage: 19.6 },
      { label: 'Healthcare', count: 30, percentage: 8.8 }
    ]},
    { category: 'Location', data: [
      { label: 'Cape Town', count: 145, percentage: 42.4 },
      { label: 'Johannesburg', count: 98, percentage: 28.7 },
      { label: 'Durban', count: 56, percentage: 16.4 },
      { label: 'Other', count: 43, percentage: 12.5 }
    ]}
  ]

  const topAttendees = [
    {
      id: '1',
      name: 'Sarah Johnson',
      photoUrl: null,
      eventsAttended: 12,
      lastEvent: '2024-03-15',
      role: 'Software Engineer',
      company: 'TechCorp',
      graduationYear: 2018
    },
    {
      id: '2',
      name: 'Michael Brown',
      photoUrl: null,
      eventsAttended: 10,
      lastEvent: '2024-03-10',
      role: 'Product Manager',
      company: 'InnovateCo',
      graduationYear: 2016
    },
    {
      id: '3',
      name: 'Emily Davis',
      photoUrl: null,
      eventsAttended: 9,
      lastEvent: '2024-03-05',
      role: 'Data Scientist',
      company: 'DataFlow',
      graduationYear: 2019
    },
    {
      id: '4',
      name: 'David Wilson',
      photoUrl: null,
      eventsAttended: 8,
      lastEvent: '2024-02-28',
      role: 'Financial Analyst',
      company: 'FinanceHub',
      graduationYear: 2017
    },
    {
      id: '5',
      name: 'Lisa Anderson',
      photoUrl: null,
      eventsAttended: 7,
      lastEvent: '2024-02-20',
      role: 'Marketing Manager',
      company: 'BrandCo',
      graduationYear: 2015
    }
  ]

  const engagementMetrics = [
    { metric: 'Average Events per Attendee', value: 3.2, change: 12.5 },
    { metric: 'Repeat Attendance Rate', value: 74, change: 8.3 },
    { metric: 'Event Satisfaction Score', value: 4.6, change: 5.7 },
    { metric: 'Network Connections Made', value: 156, change: 22.1 }
  ]

  const attendeeGrowth = [
    { month: 'Jan', newAttendees: 23, returning: 45 },
    { month: 'Feb', newAttendees: 31, returning: 52 },
    { month: 'Mar', newAttendees: 35, returning: 67 },
    { month: 'Apr', newAttendees: 28, returning: 58 },
    { month: 'May', newAttendees: 42, returning: 73 },
    { month: 'Jun', newAttendees: 38, returning: 69 }
  ]

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const getChangeColor = (change: number) => {
    return change > 0 ? 'text-green-600' : change < 0 ? 'text-red-600' : 'text-gray-600'
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Attendee Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Users className="h-4 w-4 text-blue-500" />
              <div>
                <div className="text-2xl font-bold">{attendeeStats.totalAttendees}</div>
                <div className="text-sm text-muted-foreground">Total Attendees</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-4 w-4 text-green-500" />
              <div>
                <div className="text-2xl font-bold">{attendeeStats.newAttendees}</div>
                <div className="text-sm text-muted-foreground">New Attendees</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Award className="h-4 w-4 text-purple-500" />
              <div>
                <div className="text-2xl font-bold">{attendeeStats.returningAttendees}</div>
                <div className="text-sm text-muted-foreground">Returning</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-orange-500" />
              <div>
                <div className="text-2xl font-bold">{attendeeStats.averageAge}</div>
                <div className="text-sm text-muted-foreground">Avg Age</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Demographic Breakdown */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {demographicBreakdown.map((breakdown) => (
          <Card key={breakdown.category}>
            <CardHeader>
              <CardTitle className="text-lg">{breakdown.category}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {breakdown.data.map((item) => (
                  <div key={item.label} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">{item.label}</span>
                      <div className="text-sm text-muted-foreground">
                        {item.count} ({item.percentage}%)
                      </div>
                    </div>
                    <Progress value={item.percentage} className="h-2" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Top Attendees */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Award className="h-5 w-5" />
            <span>Most Active Attendees</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {topAttendees.map((attendee, index) => (
              <div key={attendee.id} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 rounded-full bg-protec-navy text-white flex items-center justify-center text-sm font-medium">
                    {index + 1}
                  </div>
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={attendee.photoUrl || undefined} />
                    <AvatarFallback className="bg-protec-navy text-white text-sm">
                      {getInitials(attendee.name)}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-medium">{attendee.name}</div>
                    <div className="text-sm text-muted-foreground">
                      {attendee.role} at {attendee.company} • Class of {attendee.graduationYear}
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-medium">{attendee.eventsAttended} events</div>
                  <div className="text-sm text-muted-foreground">
                    Last: {new Date(attendee.lastEvent).toLocaleDateString()}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Engagement Metrics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Target className="h-5 w-5" />
            <span>Engagement Metrics</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {engagementMetrics.map((metric) => (
              <div key={metric.metric} className="p-4 border rounded-lg">
                <div className="text-2xl font-bold mb-1">
                  {typeof metric.value === 'number' && metric.value < 10 
                    ? metric.value.toFixed(1) 
                    : metric.value
                  }
                  {metric.metric.includes('Rate') || metric.metric.includes('Score') ? 
                    (metric.metric.includes('Rate') ? '%' : '/5') : ''
                  }
                </div>
                <div className="text-sm text-muted-foreground mb-2">
                  {metric.metric}
                </div>
                <div className={`text-xs flex items-center space-x-1 ${getChangeColor(metric.change)}`}>
                  <TrendingUp className="h-3 w-3" />
                  <span>+{metric.change}% from last period</span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Attendee Growth Trend */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TrendingUp className="h-5 w-5" />
            <span>Attendee Growth Trend</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {attendeeGrowth.map((month) => (
              <div key={month.month} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <span className="font-medium w-12">{month.month}</span>
                  <div className="flex-1">
                    <div className="text-sm text-muted-foreground">
                      {month.newAttendees + month.returning} total attendees
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="flex items-center space-x-4">
                    <div className="text-sm">
                      <span className="font-medium text-green-600">{month.newAttendees}</span>
                      <span className="text-muted-foreground"> new</span>
                    </div>
                    <div className="text-sm">
                      <span className="font-medium text-blue-600">{month.returning}</span>
                      <span className="text-muted-foreground"> returning</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Insights */}
      <Card>
        <CardHeader>
          <CardTitle>Attendee Insights</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="p-4 bg-green-50 rounded-lg border border-green-200">
              <div className="flex items-center space-x-2 mb-2">
                <TrendingUp className="h-4 w-4 text-green-600" />
                <span className="font-medium text-green-800">Strong Retention</span>
              </div>
              <p className="text-sm text-green-700">
                {Math.round((attendeeStats.returningAttendees / attendeeStats.totalAttendees) * 100)}% of your attendees are returning, 
                indicating high event satisfaction and value.
              </p>
            </div>
            
            <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-center space-x-2 mb-2">
                <Users className="h-4 w-4 text-blue-600" />
                <span className="font-medium text-blue-800">Diverse Audience</span>
              </div>
              <p className="text-sm text-blue-700">
                Your events attract attendees from various graduation years and industries, 
                creating valuable cross-generational networking opportunities.
              </p>
            </div>
            
            <div className="p-4 bg-purple-50 rounded-lg border border-purple-200">
              <div className="flex items-center space-x-2 mb-2">
                <Award className="h-4 w-4 text-purple-600" />
                <span className="font-medium text-purple-800">Engagement Champions</span>
              </div>
              <p className="text-sm text-purple-700">
                Consider recognizing your most active attendees like {topAttendees[0].name} who has attended {topAttendees[0].eventsAttended} events.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
