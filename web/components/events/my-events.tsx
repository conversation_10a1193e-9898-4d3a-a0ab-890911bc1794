"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { 
  Calendar, 
  MapPin, 
  Users, 
  Clock,
  Edit,
  Trash2,
  Eye,
  Plus
} from "lucide-react"

interface MyEventsProps {
  className?: string
}

export function MyEvents({ className }: MyEventsProps) {
  // Mock data - replace with real API calls
  const myEvents = [
    {
      id: '1',
      title: 'Annual Alumni Gala',
      description: 'Join us for an evening of networking and celebration',
      date: '2024-04-15',
      time: '18:00',
      location: 'Cape Town Convention Centre',
      type: 'Gala',
      status: 'published',
      attendees: 156,
      capacity: 200,
      isOrganizer: true
    },
    {
      id: '2',
      title: 'Tech Career Workshop',
      description: 'Learn about the latest trends in technology careers',
      date: '2024-04-20',
      time: '14:00',
      location: 'PROTEC Campus',
      type: 'Workshop',
      status: 'draft',
      attendees: 0,
      capacity: 50,
      isOrganizer: true
    },
    {
      id: '3',
      title: 'Networking Mixer',
      description: 'Casual networking event for Cape Town alumni',
      date: '2024-03-25',
      time: '17:30',
      location: 'V&A Waterfront',
      type: 'Networking',
      status: 'published',
      attendees: 45,
      capacity: 60,
      isOrganizer: false
    },
    {
      id: '4',
      title: 'Entrepreneurship Panel',
      description: 'Panel discussion with successful alumni entrepreneurs',
      date: '2024-05-10',
      time: '19:00',
      location: 'Online',
      type: 'Panel',
      status: 'published',
      attendees: 89,
      capacity: 100,
      isOrganizer: true
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published':
        return 'bg-green-100 text-green-800'
      case 'draft':
        return 'bg-yellow-100 text-yellow-800'
      case 'cancelled':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'Gala':
        return 'bg-purple-100 text-purple-800'
      case 'Workshop':
        return 'bg-blue-100 text-blue-800'
      case 'Networking':
        return 'bg-green-100 text-green-800'
      case 'Panel':
        return 'bg-orange-100 text-orange-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const isUpcoming = (date: string) => {
    return new Date(date) > new Date()
  }

  const isPast = (date: string) => {
    return new Date(date) < new Date()
  }

  const upcomingEvents = myEvents.filter(event => isUpcoming(event.date))
  const pastEvents = myEvents.filter(event => isPast(event.date))

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">My Events</h2>
          <p className="text-muted-foreground">
            Events you're organizing or attending
          </p>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Create Event
        </Button>
      </div>

      {/* Event Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-blue-500" />
              <div>
                <div className="text-2xl font-bold">{myEvents.length}</div>
                <div className="text-sm text-muted-foreground">Total Events</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Users className="h-4 w-4 text-green-500" />
              <div>
                <div className="text-2xl font-bold">
                  {myEvents.reduce((sum, event) => sum + event.attendees, 0)}
                </div>
                <div className="text-sm text-muted-foreground">Total Attendees</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-purple-500" />
              <div>
                <div className="text-2xl font-bold">{upcomingEvents.length}</div>
                <div className="text-sm text-muted-foreground">Upcoming</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Edit className="h-4 w-4 text-orange-500" />
              <div>
                <div className="text-2xl font-bold">
                  {myEvents.filter(e => e.isOrganizer).length}
                </div>
                <div className="text-sm text-muted-foreground">Organizing</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Upcoming Events */}
      {upcomingEvents.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Clock className="h-5 w-5" />
              <span>Upcoming Events</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {upcomingEvents.map((event) => (
                <div key={event.id} className="flex items-start space-x-4 p-4 border rounded-lg">
                  <div className="w-12 h-12 bg-protec-red rounded-lg flex items-center justify-center">
                    <Calendar className="h-6 w-6 text-white" />
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-2">
                      <h3 className="font-semibold">{event.title}</h3>
                      <Badge 
                        variant="secondary" 
                        className={`text-xs ${getStatusColor(event.status)}`}
                      >
                        {event.status}
                      </Badge>
                      <Badge 
                        variant="secondary" 
                        className={`text-xs ${getTypeColor(event.type)}`}
                      >
                        {event.type}
                      </Badge>
                      {event.isOrganizer && (
                        <Badge variant="outline" className="text-xs">
                          Organizer
                        </Badge>
                      )}
                    </div>
                    
                    <p className="text-sm text-muted-foreground mb-3">
                      {event.description}
                    </p>
                    
                    <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-4 w-4" />
                        <span>{new Date(event.date).toLocaleDateString()}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Clock className="h-4 w-4" />
                        <span>{event.time}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <MapPin className="h-4 w-4" />
                        <span>{event.location}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Users className="h-4 w-4" />
                        <span>{event.attendees}/{event.capacity}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Button size="sm" variant="ghost">
                      <Eye className="h-4 w-4" />
                    </Button>
                    {event.isOrganizer && (
                      <>
                        <Button size="sm" variant="ghost">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="ghost">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Past Events */}
      {pastEvents.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Calendar className="h-5 w-5" />
              <span>Past Events</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {pastEvents.map((event) => (
                <div key={event.id} className="flex items-start space-x-4 p-4 border rounded-lg opacity-75">
                  <div className="w-12 h-12 bg-gray-400 rounded-lg flex items-center justify-center">
                    <Calendar className="h-6 w-6 text-white" />
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-2">
                      <h3 className="font-semibold">{event.title}</h3>
                      <Badge 
                        variant="secondary" 
                        className={`text-xs ${getTypeColor(event.type)}`}
                      >
                        {event.type}
                      </Badge>
                      {event.isOrganizer && (
                        <Badge variant="outline" className="text-xs">
                          Organizer
                        </Badge>
                      )}
                    </div>
                    
                    <p className="text-sm text-muted-foreground mb-3">
                      {event.description}
                    </p>
                    
                    <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-4 w-4" />
                        <span>{new Date(event.date).toLocaleDateString()}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Clock className="h-4 w-4" />
                        <span>{event.time}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <MapPin className="h-4 w-4" />
                        <span>{event.location}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Users className="h-4 w-4" />
                        <span>{event.attendees} attended</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Button size="sm" variant="ghost">
                      <Eye className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Empty State */}
      {myEvents.length === 0 && (
        <Card>
          <CardContent className="py-12 text-center">
            <Calendar className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No Events Yet</h3>
            <p className="text-muted-foreground mb-4">
              You haven't created or joined any events yet. Start by creating your first event!
            </p>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create Your First Event
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
