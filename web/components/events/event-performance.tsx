"use client"

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { 
  TrendingUp, 
  Users, 
  Calendar, 
  Target,
  Eye,
  Heart,
  MessageCircle,
  Share
} from "lucide-react"

interface EventPerformanceProps {
  className?: string
}

export function EventPerformance({ className }: EventPerformanceProps) {
  // Mock data - replace with real API calls
  const performanceData = [
    {
      id: '1',
      title: 'Annual Alumni Gala',
      date: '2024-03-15',
      metrics: {
        views: 1250,
        registrations: 156,
        attendance: 142,
        capacity: 200,
        engagementRate: 78,
        satisfaction: 4.8
      },
      trends: {
        viewsChange: 12.5,
        registrationsChange: 8.3,
        attendanceChange: 5.2
      }
    },
    {
      id: '2',
      title: 'Tech Career Workshop',
      date: '2024-03-10',
      metrics: {
        views: 890,
        registrations: 89,
        attendance: 76,
        capacity: 100,
        engagementRate: 85,
        satisfaction: 4.6
      },
      trends: {
        viewsChange: 15.7,
        registrationsChang<PERSON>: 22.1,
        attendanceChange: 18.9
      }
    },
    {
      id: '3',
      title: 'Networking Mixer',
      date: '2024-03-05',
      metrics: {
        views: 567,
        registrations: 45,
        attendance: 38,
        capacity: 60,
        engagementRate: 92,
        satisfaction: 4.4
      },
      trends: {
        viewsChange: -5.2,
        registrationsChange: -8.1,
        attendanceChange: -12.3
      }
    }
  ]

  const overallMetrics = {
    totalViews: performanceData.reduce((sum, event) => sum + event.metrics.views, 0),
    totalRegistrations: performanceData.reduce((sum, event) => sum + event.metrics.registrations, 0),
    totalAttendance: performanceData.reduce((sum, event) => sum + event.metrics.attendance, 0),
    averageEngagement: performanceData.reduce((sum, event) => sum + event.metrics.engagementRate, 0) / performanceData.length,
    averageSatisfaction: performanceData.reduce((sum, event) => sum + event.metrics.satisfaction, 0) / performanceData.length
  }

  const getChangeColor = (change: number) => {
    return change > 0 ? 'text-green-600' : change < 0 ? 'text-red-600' : 'text-gray-600'
  }

  const getChangeIcon = (change: number) => {
    return change > 0 ? '↗' : change < 0 ? '↘' : '→'
  }

  const getPerformanceColor = (percentage: number) => {
    if (percentage >= 80) return 'text-green-600'
    if (percentage >= 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Overall Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Eye className="h-4 w-4 text-blue-500" />
              <div>
                <div className="text-2xl font-bold">{overallMetrics.totalViews.toLocaleString()}</div>
                <div className="text-sm text-muted-foreground">Total Views</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Users className="h-4 w-4 text-green-500" />
              <div>
                <div className="text-2xl font-bold">{overallMetrics.totalRegistrations}</div>
                <div className="text-sm text-muted-foreground">Registrations</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-purple-500" />
              <div>
                <div className="text-2xl font-bold">{overallMetrics.totalAttendance}</div>
                <div className="text-sm text-muted-foreground">Attendance</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-4 w-4 text-orange-500" />
              <div>
                <div className="text-2xl font-bold">{Math.round(overallMetrics.averageEngagement)}%</div>
                <div className="text-sm text-muted-foreground">Avg Engagement</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Heart className="h-4 w-4 text-red-500" />
              <div>
                <div className="text-2xl font-bold">{overallMetrics.averageSatisfaction.toFixed(1)}</div>
                <div className="text-sm text-muted-foreground">Satisfaction</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Individual Event Performance */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TrendingUp className="h-5 w-5" />
            <span>Event Performance</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {performanceData.map((event) => {
              const attendanceRate = (event.metrics.attendance / event.metrics.capacity) * 100
              const conversionRate = (event.metrics.registrations / event.metrics.views) * 100
              const showUpRate = (event.metrics.attendance / event.metrics.registrations) * 100

              return (
                <div key={event.id} className="p-6 border rounded-lg">
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <h3 className="text-lg font-semibold">{event.title}</h3>
                      <p className="text-sm text-muted-foreground">
                        {new Date(event.date).toLocaleDateString()}
                      </p>
                    </div>
                    <Badge variant="secondary" className="text-xs">
                      {Math.round(attendanceRate)}% capacity
                    </Badge>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {/* Metrics */}
                    <div className="space-y-4">
                      <h4 className="font-medium">Key Metrics</h4>
                      
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <Eye className="h-4 w-4 text-blue-500" />
                            <span className="text-sm">Views</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className="font-medium">{event.metrics.views.toLocaleString()}</span>
                            <span className={`text-xs ${getChangeColor(event.trends.viewsChange)}`}>
                              {getChangeIcon(event.trends.viewsChange)} {Math.abs(event.trends.viewsChange)}%
                            </span>
                          </div>
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <Users className="h-4 w-4 text-green-500" />
                            <span className="text-sm">Registrations</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className="font-medium">{event.metrics.registrations}</span>
                            <span className={`text-xs ${getChangeColor(event.trends.registrationsChange)}`}>
                              {getChangeIcon(event.trends.registrationsChange)} {Math.abs(event.trends.registrationsChange)}%
                            </span>
                          </div>
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <Calendar className="h-4 w-4 text-purple-500" />
                            <span className="text-sm">Attendance</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className="font-medium">{event.metrics.attendance}</span>
                            <span className={`text-xs ${getChangeColor(event.trends.attendanceChange)}`}>
                              {getChangeIcon(event.trends.attendanceChange)} {Math.abs(event.trends.attendanceChange)}%
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Performance Indicators */}
                    <div className="space-y-4">
                      <h4 className="font-medium">Performance</h4>
                      
                      <div className="space-y-3">
                        <div>
                          <div className="flex items-center justify-between mb-1">
                            <span className="text-sm">Conversion Rate</span>
                            <span className={`text-sm font-medium ${getPerformanceColor(conversionRate)}`}>
                              {conversionRate.toFixed(1)}%
                            </span>
                          </div>
                          <Progress value={conversionRate} className="h-2" />
                        </div>

                        <div>
                          <div className="flex items-center justify-between mb-1">
                            <span className="text-sm">Show-up Rate</span>
                            <span className={`text-sm font-medium ${getPerformanceColor(showUpRate)}`}>
                              {showUpRate.toFixed(1)}%
                            </span>
                          </div>
                          <Progress value={showUpRate} className="h-2" />
                        </div>

                        <div>
                          <div className="flex items-center justify-between mb-1">
                            <span className="text-sm">Engagement</span>
                            <span className={`text-sm font-medium ${getPerformanceColor(event.metrics.engagementRate)}`}>
                              {event.metrics.engagementRate}%
                            </span>
                          </div>
                          <Progress value={event.metrics.engagementRate} className="h-2" />
                        </div>
                      </div>
                    </div>

                    {/* Satisfaction & Feedback */}
                    <div className="space-y-4">
                      <h4 className="font-medium">Feedback</h4>
                      
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <Heart className="h-4 w-4 text-red-500" />
                            <span className="text-sm">Satisfaction</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <span className="font-medium">{event.metrics.satisfaction}</span>
                            <span className="text-sm text-muted-foreground">/5.0</span>
                          </div>
                        </div>

                        <div className="text-center p-4 bg-gray-50 rounded-lg">
                          <div className="text-2xl font-bold text-protec-red">
                            {event.metrics.satisfaction}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            Average Rating
                          </div>
                          <div className="flex justify-center mt-2">
                            {Array.from({ length: 5 }).map((_, i) => (
                              <Heart 
                                key={i} 
                                className={`w-4 h-4 ${
                                  i < Math.floor(event.metrics.satisfaction) 
                                    ? 'text-red-500 fill-current' 
                                    : 'text-gray-300'
                                }`} 
                              />
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Performance Insights */}
      <Card>
        <CardHeader>
          <CardTitle>Performance Insights</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="p-4 bg-green-50 rounded-lg border border-green-200">
              <div className="flex items-center space-x-2 mb-2">
                <TrendingUp className="h-4 w-4 text-green-600" />
                <span className="font-medium text-green-800">Strong Performance</span>
              </div>
              <p className="text-sm text-green-700">
                Your events are showing excellent engagement rates with an average of {Math.round(overallMetrics.averageEngagement)}%. 
                Keep up the great work!
              </p>
            </div>
            
            <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-center space-x-2 mb-2">
                <Target className="h-4 w-4 text-blue-600" />
                <span className="font-medium text-blue-800">Optimization Opportunity</span>
              </div>
              <p className="text-sm text-blue-700">
                Consider promoting events earlier to increase view-to-registration conversion rates. 
                Your current average is {((overallMetrics.totalRegistrations / overallMetrics.totalViews) * 100).toFixed(1)}%.
              </p>
            </div>
            
            <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
              <div className="flex items-center space-x-2 mb-2">
                <MessageCircle className="h-4 w-4 text-yellow-600" />
                <span className="font-medium text-yellow-800">Feedback Collection</span>
              </div>
              <p className="text-sm text-yellow-700">
                Your satisfaction scores are good ({overallMetrics.averageSatisfaction.toFixed(1)}/5.0), 
                but consider collecting more detailed feedback to identify specific improvement areas.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
