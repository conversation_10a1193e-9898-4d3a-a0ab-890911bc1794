"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { 
  Calendar, 
  Users, 
  TrendingUp, 
  MapPin,
  Clock,
  Star,
  Award,
  Target
} from "lucide-react"

interface EventAnalyticsProps {
  className?: string
}

export function EventAnalytics({ className }: EventAnalyticsProps) {
  // Mock data - replace with real API calls
  const metrics = {
    totalEvents: {
      count: 45,
      change: 15.2,
      trend: 'up' as const
    },
    totalAttendees: {
      count: 1234,
      change: 8.7,
      trend: 'up' as const
    },
    averageAttendance: {
      percentage: 78,
      change: 5.3,
      trend: 'up' as const
    },
    upcomingEvents: {
      count: 12,
      change: 20.0,
      trend: 'up' as const
    }
  }

  const eventsByCategory = [
    { category: 'Networking', count: 15, percentage: 33, attendees: 456 },
    { category: 'Workshop', count: 12, percentage: 27, attendees: 378 },
    { category: 'Webinar', count: 10, percentage: 22, attendees: 289 },
    { category: 'Conference', count: 5, percentage: 11, attendees: 78 },
    { category: 'Social', count: 3, percentage: 7, attendees: 33 }
  ]

  const topEvents = [
    { 
      name: 'Annual Alumni Gala', 
      attendees: 156, 
      capacity: 200, 
      rating: 4.8,
      date: '2024-03-15'
    },
    { 
      name: 'Tech Career Workshop', 
      attendees: 89, 
      capacity: 100, 
      rating: 4.6,
      date: '2024-03-10'
    },
    { 
      name: 'Entrepreneurship Panel', 
      attendees: 67, 
      capacity: 80, 
      rating: 4.7,
      date: '2024-03-05'
    },
    { 
      name: 'Networking Mixer', 
      attendees: 45, 
      capacity: 60, 
      rating: 4.4,
      date: '2024-02-28'
    },
    { 
      name: 'Leadership Seminar', 
      attendees: 34, 
      capacity: 40, 
      rating: 4.9,
      date: '2024-02-20'
    }
  ]

  const attendanceByMonth = [
    { month: 'Jan', events: 6, attendees: 189, avgAttendance: 31.5 },
    { month: 'Feb', events: 8, attendees: 245, avgAttendance: 30.6 },
    { month: 'Mar', events: 10, attendees: 312, avgAttendance: 31.2 },
    { month: 'Apr', events: 7, attendees: 198, avgAttendance: 28.3 },
    { month: 'May', events: 9, attendees: 267, avgAttendance: 29.7 },
    { month: 'Jun', events: 5, attendees: 123, avgAttendance: 24.6 }
  ]

  const eventLocations = [
    { location: 'Cape Town', count: 18, percentage: 40 },
    { location: 'Johannesburg', count: 12, percentage: 27 },
    { location: 'Durban', count: 8, percentage: 18 },
    { location: 'Online', count: 7, percentage: 15 }
  ]

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Events</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.totalEvents.count}</div>
            <div className="flex items-center space-x-2 text-xs text-muted-foreground">
              <TrendingUp className={`h-3 w-3 ${metrics.totalEvents.trend === 'up' ? 'text-green-500' : 'text-red-500'}`} />
              <span className={metrics.totalEvents.trend === 'up' ? 'text-green-500' : 'text-red-500'}>
                {metrics.totalEvents.change > 0 ? '+' : ''}{metrics.totalEvents.change}%
              </span>
              <span>from last period</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Attendees</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.totalAttendees.count.toLocaleString()}</div>
            <div className="flex items-center space-x-2 text-xs text-muted-foreground">
              <TrendingUp className={`h-3 w-3 ${metrics.totalAttendees.trend === 'up' ? 'text-green-500' : 'text-red-500'}`} />
              <span className={metrics.totalAttendees.trend === 'up' ? 'text-green-500' : 'text-red-500'}>
                {metrics.totalAttendees.change > 0 ? '+' : ''}{metrics.totalAttendees.change}%
              </span>
              <span>from last period</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Attendance</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.averageAttendance.percentage}%</div>
            <div className="flex items-center space-x-2 text-xs text-muted-foreground">
              <TrendingUp className={`h-3 w-3 ${metrics.averageAttendance.trend === 'up' ? 'text-green-500' : 'text-red-500'}`} />
              <span className={metrics.averageAttendance.trend === 'up' ? 'text-green-500' : 'text-red-500'}>
                {metrics.averageAttendance.change > 0 ? '+' : ''}{metrics.averageAttendance.change}%
              </span>
              <span>from last period</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Upcoming Events</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.upcomingEvents.count}</div>
            <div className="flex items-center space-x-2 text-xs text-muted-foreground">
              <TrendingUp className={`h-3 w-3 ${metrics.upcomingEvents.trend === 'up' ? 'text-green-500' : 'text-red-500'}`} />
              <span className={metrics.upcomingEvents.trend === 'up' ? 'text-green-500' : 'text-red-500'}>
                {metrics.upcomingEvents.change > 0 ? '+' : ''}{metrics.upcomingEvents.change}%
              </span>
              <span>from last period</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Events by Category */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Calendar className="h-5 w-5" />
            <span>Events by Category</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {eventsByCategory.map((category) => (
              <div key={category.category} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium">{category.category}</span>
                    <Badge variant="secondary">{category.count} events</Badge>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {category.attendees} attendees ({category.percentage}%)
                  </div>
                </div>
                <Progress value={category.percentage} className="h-2" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Top Performing Events */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Award className="h-5 w-5" />
            <span>Top Performing Events</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {topEvents.map((event, index) => (
              <div key={event.name} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 rounded-full bg-protec-navy text-white flex items-center justify-center text-sm font-medium">
                    {index + 1}
                  </div>
                  <div>
                    <div className="font-medium">{event.name}</div>
                    <div className="text-sm text-muted-foreground">
                      {new Date(event.date).toLocaleDateString()}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="text-right">
                    <div className="text-sm font-medium">
                      {event.attendees}/{event.capacity}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {Math.round((event.attendees / event.capacity) * 100)}% capacity
                    </div>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Star className="w-4 h-4 text-yellow-500 fill-current" />
                    <span className="text-sm font-medium">{event.rating}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Monthly Attendance Trends */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TrendingUp className="h-5 w-5" />
            <span>Monthly Attendance Trends</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {attendanceByMonth.map((month) => (
              <div key={month.month} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <span className="font-medium w-12">{month.month}</span>
                  <div className="flex-1">
                    <div className="text-sm text-muted-foreground">
                      {month.events} events
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-medium">{month.attendees} attendees</div>
                  <div className="text-sm text-muted-foreground">
                    {month.avgAttendance.toFixed(1)} avg per event
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Events by Location */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <MapPin className="h-5 w-5" />
            <span>Events by Location</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {eventLocations.map((location) => (
              <div key={location.location} className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="font-medium">{location.location}</span>
                  <div className="text-sm text-muted-foreground">
                    {location.count} events ({location.percentage}%)
                  </div>
                </div>
                <Progress value={location.percentage} className="h-2" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
