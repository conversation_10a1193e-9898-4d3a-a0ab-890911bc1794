"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { 
  Users, 
  MessageCircle, 
  Calendar, 
  TrendingUp,
  Activity,
  Clock,
  Target,
  Award
} from "lucide-react"

interface EngagementMetricsProps {
  className?: string
}

export function EngagementMetrics({ className }: EngagementMetricsProps) {
  // Mock data - replace with real API calls
  const metrics = {
    activeUsers: {
      total: 1247,
      change: 12.5,
      trend: 'up' as const
    },
    messagesSent: {
      total: 3456,
      change: 8.3,
      trend: 'up' as const
    },
    eventsAttended: {
      total: 234,
      change: -2.1,
      trend: 'down' as const
    },
    profileCompleteness: {
      average: 78,
      change: 5.2,
      trend: 'up' as const
    },
    connectionsMade: {
      total: 567,
      change: 15.7,
      trend: 'up' as const
    },
    sessionDuration: {
      average: 12.5, // minutes
      change: 3.4,
      trend: 'up' as const
    }
  }

  const engagementLevels = [
    { level: 'High', count: 342, percentage: 27, color: 'bg-green-500' },
    { level: 'Medium', count: 589, percentage: 47, color: 'bg-yellow-500' },
    { level: 'Low', count: 316, percentage: 26, color: 'bg-red-500' }
  ]

  const topActivities = [
    { activity: 'Profile Updates', count: 234, change: 12 },
    { activity: 'Messages Sent', count: 189, change: 8 },
    { activity: 'Event RSVPs', count: 156, change: -3 },
    { activity: 'Connections Made', count: 134, change: 15 },
    { activity: 'Posts Created', count: 98, change: 22 }
  ]

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.activeUsers.total.toLocaleString()}</div>
            <div className="flex items-center space-x-2 text-xs text-muted-foreground">
              <TrendingUp className={`h-3 w-3 ${metrics.activeUsers.trend === 'up' ? 'text-green-500' : 'text-red-500'}`} />
              <span className={metrics.activeUsers.trend === 'up' ? 'text-green-500' : 'text-red-500'}>
                {metrics.activeUsers.change > 0 ? '+' : ''}{metrics.activeUsers.change}%
              </span>
              <span>from last month</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Messages Sent</CardTitle>
            <MessageCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.messagesSent.total.toLocaleString()}</div>
            <div className="flex items-center space-x-2 text-xs text-muted-foreground">
              <TrendingUp className={`h-3 w-3 ${metrics.messagesSent.trend === 'up' ? 'text-green-500' : 'text-red-500'}`} />
              <span className={metrics.messagesSent.trend === 'up' ? 'text-green-500' : 'text-red-500'}>
                {metrics.messagesSent.change > 0 ? '+' : ''}{metrics.messagesSent.change}%
              </span>
              <span>from last month</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Events Attended</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.eventsAttended.total.toLocaleString()}</div>
            <div className="flex items-center space-x-2 text-xs text-muted-foreground">
              <TrendingUp className={`h-3 w-3 ${metrics.eventsAttended.trend === 'up' ? 'text-green-500' : 'text-red-500'}`} />
              <span className={metrics.eventsAttended.trend === 'up' ? 'text-green-500' : 'text-red-500'}>
                {metrics.eventsAttended.change > 0 ? '+' : ''}{metrics.eventsAttended.change}%
              </span>
              <span>from last month</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Profile Completeness</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.profileCompleteness.average}%</div>
            <div className="flex items-center space-x-2 text-xs text-muted-foreground">
              <TrendingUp className={`h-3 w-3 ${metrics.profileCompleteness.trend === 'up' ? 'text-green-500' : 'text-red-500'}`} />
              <span className={metrics.profileCompleteness.trend === 'up' ? 'text-green-500' : 'text-red-500'}>
                {metrics.profileCompleteness.change > 0 ? '+' : ''}{metrics.profileCompleteness.change}%
              </span>
              <span>from last month</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Connections Made</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.connectionsMade.total.toLocaleString()}</div>
            <div className="flex items-center space-x-2 text-xs text-muted-foreground">
              <TrendingUp className={`h-3 w-3 ${metrics.connectionsMade.trend === 'up' ? 'text-green-500' : 'text-red-500'}`} />
              <span className={metrics.connectionsMade.trend === 'up' ? 'text-green-500' : 'text-red-500'}>
                {metrics.connectionsMade.change > 0 ? '+' : ''}{metrics.connectionsMade.change}%
              </span>
              <span>from last month</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Session Duration</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.sessionDuration.average}m</div>
            <div className="flex items-center space-x-2 text-xs text-muted-foreground">
              <TrendingUp className={`h-3 w-3 ${metrics.sessionDuration.trend === 'up' ? 'text-green-500' : 'text-red-500'}`} />
              <span className={metrics.sessionDuration.trend === 'up' ? 'text-green-500' : 'text-red-500'}>
                {metrics.sessionDuration.change > 0 ? '+' : ''}{metrics.sessionDuration.change}%
              </span>
              <span>from last month</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Engagement Levels */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Activity className="h-5 w-5" />
            <span>User Engagement Levels</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {engagementLevels.map((level) => (
              <div key={level.level} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className={`w-3 h-3 rounded-full ${level.color}`} />
                    <span className="font-medium">{level.level} Engagement</span>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {level.count} users ({level.percentage}%)
                  </div>
                </div>
                <Progress value={level.percentage} className="h-2" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Top Activities */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Award className="h-5 w-5" />
            <span>Top Activities</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {topActivities.map((activity, index) => (
              <div key={activity.activity} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 rounded-full bg-protec-navy text-white flex items-center justify-center text-sm font-medium">
                    {index + 1}
                  </div>
                  <span className="font-medium">{activity.activity}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-muted-foreground">{activity.count}</span>
                  <Badge 
                    variant={activity.change > 0 ? "default" : "destructive"}
                    className="text-xs"
                  >
                    {activity.change > 0 ? '+' : ''}{activity.change}%
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
