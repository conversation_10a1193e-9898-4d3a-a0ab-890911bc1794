"use client"

import { api } from "@/components/providers/trpc-provider"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { 
  <PERSON>, 
  UserCheck, 
  UserPlus, 
  TrendingUp,
  Shield,
  Calendar,
  Heart,
  MessageSquare
} from "lucide-react"
import { UserRole, ROLE_DISPLAY_NAMES } from "@/lib/types/user"

export function UserStats() {
  const { data: stats, isLoading } = api.users.getStats.useQuery({
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
    endDate: new Date(),
  })

  if (isLoading) {
    return (
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="space-y-2">
                <div className="h-4 bg-gray-200 rounded animate-pulse" />
                <div className="h-8 bg-gray-200 rounded animate-pulse" />
                <div className="h-3 bg-gray-200 rounded animate-pulse w-2/3" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (!stats) {
    return null
  }

  const activeUserPercentage = stats.totalUsers > 0 
    ? (stats.activeUsers / stats.totalUsers) * 100 
    : 0

  const getRoleColor = (role: UserRole) => {
    switch (role) {
      case UserRole.ADMIN:
        return "bg-red-100 text-red-800"
      case UserRole.DONOR_COORDINATOR:
        return "bg-green-100 text-green-800"
      case UserRole.EVENT_ORGANIZER:
        return "bg-blue-100 text-blue-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <div className="space-y-6">
      {/* Main Stats */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card className="border-l-4 border-l-protec-navy">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-protec-navy" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-protec-navy">
              {stats.totalUsers.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              Registered alumni
            </p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-green-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Users</CardTitle>
            <UserCheck className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {stats.activeUsers.toLocaleString()}
            </div>
            <div className="flex items-center space-x-2">
              <Progress value={activeUserPercentage} className="flex-1" />
              <span className="text-xs text-muted-foreground">
                {activeUserPercentage.toFixed(1)}%
              </span>
            </div>
            <p className="text-xs text-muted-foreground">
              Active in last 30 days
            </p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-protec-red">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">New Users</CardTitle>
            <UserPlus className="h-4 w-4 text-protec-red" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-protec-red">
              {stats.newUsers.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              Last 30 days
            </p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-blue-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Growth Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {stats.totalUsers > 0 
                ? ((stats.newUsers / stats.totalUsers) * 100).toFixed(1)
                : "0"
              }%
            </div>
            <p className="text-xs text-muted-foreground">
              Monthly growth
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Role Distribution and Top Contributors */}
      <div className="grid gap-6 lg:grid-cols-2">
        {/* Role Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Shield className="mr-2 h-5 w-5" />
              Role Distribution
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {stats.roleDistribution.map((item) => {
              const percentage = stats.totalUsers > 0 
                ? (item.count / stats.totalUsers) * 100 
                : 0

              return (
                <div key={item.role} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Badge 
                        variant="secondary" 
                        className={getRoleColor(item.role)}
                      >
                        {ROLE_DISPLAY_NAMES[item.role]}
                      </Badge>
                      <span className="text-sm text-muted-foreground">
                        {item.count} users
                      </span>
                    </div>
                    <span className="text-sm font-medium">
                      {percentage.toFixed(1)}%
                    </span>
                  </div>
                  <Progress value={percentage} className="h-2" />
                </div>
              )
            })}
          </CardContent>
        </Card>

        {/* Top Contributors */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Heart className="mr-2 h-5 w-5" />
              Top Contributors
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {stats.topContributors.slice(0, 5).map((contributor, index) => (
                <div key={contributor.id} className="flex items-center space-x-3">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 rounded-full bg-protec-navy text-white flex items-center justify-center text-sm font-medium">
                      {index + 1}
                    </div>
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">
                      {contributor.name}
                    </p>
                    <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                      <span className="flex items-center">
                        <Heart className="mr-1 h-3 w-3" />
                        {contributor._count.donations} donations
                      </span>
                      <span className="flex items-center">
                        <MessageSquare className="mr-1 h-3 w-3" />
                        {contributor._count.posts} posts
                      </span>
                      <span className="flex items-center">
                        <Calendar className="mr-1 h-3 w-3" />
                        {contributor._count.organizedEvents} events
                      </span>
                    </div>
                  </div>
                </div>
              ))}
              
              {stats.topContributors.length === 0 && (
                <div className="text-center py-4 text-muted-foreground">
                  No contributors data available
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
