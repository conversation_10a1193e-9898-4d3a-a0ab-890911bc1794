"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { 
  DollarSign, 
  TrendingUp, 
  Users, 
  Calendar,
  Target,
  CreditCard,
  Repeat,
  Award
} from "lucide-react"

interface DonationAnalyticsProps {
  className?: string
}

export function DonationAnalytics({ className }: DonationAnalyticsProps) {
  // Mock data - replace with real API calls
  const metrics = {
    totalRevenue: {
      amount: 125000,
      change: 18.5,
      trend: 'up' as const
    },
    totalDonors: {
      count: 342,
      change: 12.3,
      trend: 'up' as const
    },
    averageDonation: {
      amount: 365,
      change: 5.7,
      trend: 'up' as const
    },
    recurringDonations: {
      count: 89,
      percentage: 26,
      change: 22.1,
      trend: 'up' as const
    }
  }

  const donationsByGateway = [
    { gateway: 'PayFast', amount: 75000, percentage: 60, count: 205 },
    { gateway: 'PayPal', amount: 35000, percentage: 28, count: 98 },
    { gateway: 'Bank Transfer', amount: 15000, percentage: 12, count: 39 }
  ]

  const donationsByPurpose = [
    { purpose: 'General Fund', amount: 45000, percentage: 36, count: 156 },
    { purpose: 'Scholarships', amount: 35000, percentage: 28, count: 89 },
    { purpose: 'Infrastructure', amount: 25000, percentage: 20, count: 67 },
    { purpose: 'Programs', amount: 20000, percentage: 16, count: 30 }
  ]

  const topDonors = [
    { name: 'John Smith', amount: 5000, donations: 12, type: 'recurring' },
    { name: 'Sarah Johnson', amount: 3500, donations: 8, type: 'one-time' },
    { name: 'Michael Brown', amount: 3000, donations: 15, type: 'recurring' },
    { name: 'Emily Davis', amount: 2800, donations: 6, type: 'one-time' },
    { name: 'David Wilson', amount: 2500, donations: 10, type: 'recurring' }
  ]

  const monthlyTrends = [
    { month: 'Jan', amount: 8500, donors: 28 },
    { month: 'Feb', amount: 9200, donors: 31 },
    { month: 'Mar', amount: 11000, donors: 35 },
    { month: 'Apr', amount: 10500, donors: 33 },
    { month: 'May', amount: 12800, donors: 42 },
    { month: 'Jun', amount: 15200, donors: 48 }
  ]

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">R{metrics.totalRevenue.amount.toLocaleString()}</div>
            <div className="flex items-center space-x-2 text-xs text-muted-foreground">
              <TrendingUp className={`h-3 w-3 ${metrics.totalRevenue.trend === 'up' ? 'text-green-500' : 'text-red-500'}`} />
              <span className={metrics.totalRevenue.trend === 'up' ? 'text-green-500' : 'text-red-500'}>
                {metrics.totalRevenue.change > 0 ? '+' : ''}{metrics.totalRevenue.change}%
              </span>
              <span>from last month</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Donors</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.totalDonors.count}</div>
            <div className="flex items-center space-x-2 text-xs text-muted-foreground">
              <TrendingUp className={`h-3 w-3 ${metrics.totalDonors.trend === 'up' ? 'text-green-500' : 'text-red-500'}`} />
              <span className={metrics.totalDonors.trend === 'up' ? 'text-green-500' : 'text-red-500'}>
                {metrics.totalDonors.change > 0 ? '+' : ''}{metrics.totalDonors.change}%
              </span>
              <span>from last month</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Donation</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">R{metrics.averageDonation.amount}</div>
            <div className="flex items-center space-x-2 text-xs text-muted-foreground">
              <TrendingUp className={`h-3 w-3 ${metrics.averageDonation.trend === 'up' ? 'text-green-500' : 'text-red-500'}`} />
              <span className={metrics.averageDonation.trend === 'up' ? 'text-green-500' : 'text-red-500'}>
                {metrics.averageDonation.change > 0 ? '+' : ''}{metrics.averageDonation.change}%
              </span>
              <span>from last month</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Recurring Donors</CardTitle>
            <Repeat className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.recurringDonations.count}</div>
            <div className="flex items-center space-x-2 text-xs text-muted-foreground">
              <span className="text-green-500">{metrics.recurringDonations.percentage}% of total</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Donations by Gateway */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <CreditCard className="h-5 w-5" />
            <span>Donations by Payment Gateway</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {donationsByGateway.map((gateway) => (
              <div key={gateway.gateway} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium">{gateway.gateway}</span>
                    <Badge variant="secondary">{gateway.count} donations</Badge>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    R{gateway.amount.toLocaleString()} ({gateway.percentage}%)
                  </div>
                </div>
                <Progress value={gateway.percentage} className="h-2" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Donations by Purpose */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Target className="h-5 w-5" />
            <span>Donations by Purpose</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {donationsByPurpose.map((purpose) => (
              <div key={purpose.purpose} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium">{purpose.purpose}</span>
                    <Badge variant="outline">{purpose.count} donations</Badge>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    R{purpose.amount.toLocaleString()} ({purpose.percentage}%)
                  </div>
                </div>
                <Progress value={purpose.percentage} className="h-2" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Top Donors */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Award className="h-5 w-5" />
            <span>Top Donors</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {topDonors.map((donor, index) => (
              <div key={donor.name} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 rounded-full bg-protec-navy text-white flex items-center justify-center text-sm font-medium">
                    {index + 1}
                  </div>
                  <div>
                    <div className="font-medium">{donor.name}</div>
                    <div className="text-sm text-muted-foreground">
                      {donor.donations} donations
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="font-medium">R{donor.amount.toLocaleString()}</span>
                  <Badge 
                    variant={donor.type === 'recurring' ? "default" : "secondary"}
                    className="text-xs"
                  >
                    {donor.type}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Monthly Trends */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Calendar className="h-5 w-5" />
            <span>Monthly Trends</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {monthlyTrends.map((month) => (
              <div key={month.month} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <span className="font-medium w-12">{month.month}</span>
                  <div className="flex-1">
                    <div className="text-sm text-muted-foreground">
                      {month.donors} donors
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-medium">R{month.amount.toLocaleString()}</div>
                  <div className="text-sm text-muted-foreground">
                    R{Math.round(month.amount / month.donors)} avg
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
