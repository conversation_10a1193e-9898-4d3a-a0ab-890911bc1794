"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { 
  Mail, 
  Send, 
  Eye, 
  Code, 
  Loader2,
  CheckCircle,
  AlertTriangle,
  Copy,
  Download
} from "lucide-react"
import { toast } from "sonner"
import { getAvailableTemplates, generateTemplatePreview, TemplateType } from "@/lib/email-templates"

export function TemplateManager() {
  const [selectedTemplate, setSelectedTemplate] = useState<TemplateType>('magic-link')
  const [isLoading, setIsLoading] = useState(false)
  const [previewData, setPreviewData] = useState<any>({})
  const [testEmail, setTestEmail] = useState("")
  const [results, setResults] = useState<any[]>([])
  const [previewTemplate, setPreviewTemplate] = useState<any>(null)

  const availableTemplates = getAvailableTemplates()

  const templateFields = {
    'magic-link': [
      { name: 'name', label: 'User Name', type: 'text', required: true },
      { name: 'email', label: 'Email Address', type: 'email', required: true },
      { name: 'url', label: 'Magic Link URL', type: 'url', required: true },
      { name: 'host', label: 'Host Domain', type: 'text', required: true },
      { name: 'expiresIn', label: 'Expires In', type: 'text', defaultValue: '24 hours' }
    ],
    'welcome': [
      { name: 'name', label: 'User Name', type: 'text', required: true },
      { name: 'email', label: 'Email Address', type: 'email', required: true },
      { name: 'dashboardUrl', label: 'Dashboard URL', type: 'url', defaultValue: '/dashboard' }
    ],
    'welcome-back': [
      { name: 'name', label: 'User Name', type: 'text', required: true },
      { name: 'dashboardUrl', label: 'Dashboard URL', type: 'url', defaultValue: '/dashboard' }
    ],
    'event-invitation': [
      { name: 'name', label: 'User Name', type: 'text', required: true },
      { name: 'eventTitle', label: 'Event Title', type: 'text', required: true },
      { name: 'eventDate', label: 'Event Date', type: 'text', required: true },
      { name: 'eventTime', label: 'Event Time', type: 'text' },
      { name: 'eventLocation', label: 'Event Location', type: 'text', required: true },
      { name: 'eventDescription', label: 'Event Description', type: 'textarea', required: true },
      { name: 'rsvpUrl', label: 'RSVP URL', type: 'url', required: true },
      { name: 'eventType', label: 'Event Type', type: 'select', options: ['networking', 'workshop', 'webinar', 'conference', 'social'] },
      { name: 'isVirtual', label: 'Virtual Event', type: 'checkbox' },
      { name: 'organizer', label: 'Organizer', type: 'text' },
      { name: 'capacity', label: 'Capacity', type: 'number' },
      { name: 'spotsRemaining', label: 'Spots Remaining', type: 'number' }
    ],
    'event-reminder': [
      { name: 'name', label: 'User Name', type: 'text', required: true },
      { name: 'eventTitle', label: 'Event Title', type: 'text', required: true },
      { name: 'eventDate', label: 'Event Date', type: 'text', required: true },
      { name: 'eventTime', label: 'Event Time', type: 'text' },
      { name: 'eventLocation', label: 'Event Location', type: 'text', required: true },
      { name: 'rsvpUrl', label: 'RSVP URL', type: 'url', required: true },
      { name: 'isVirtual', label: 'Virtual Event', type: 'checkbox' }
    ],
    'system-notification': [
      { name: 'name', label: 'User Name', type: 'text', required: true },
      { name: 'title', label: 'Notification Title', type: 'text', required: true },
      { name: 'message', label: 'Message', type: 'textarea', required: true },
      { name: 'priority', label: 'Priority', type: 'select', options: ['low', 'medium', 'high'] },
      { name: 'actionUrl', label: 'Action URL', type: 'url' },
      { name: 'actionText', label: 'Action Text', type: 'text' },
      { name: 'category', label: 'Category', type: 'select', options: ['system', 'security', 'feature', 'maintenance'] }
    ],
    'security-alert': [
      { name: 'name', label: 'User Name', type: 'text', required: true },
      { name: 'title', label: 'Alert Title', type: 'text', required: true },
      { name: 'message', label: 'Alert Message', type: 'textarea', required: true },
      { name: 'actionUrl', label: 'Action URL', type: 'url' },
      { name: 'actionText', label: 'Action Text', type: 'text' },
      { name: 'ipAddress', label: 'IP Address', type: 'text' },
      { name: 'location', label: 'Location', type: 'text' },
      { name: 'device', label: 'Device', type: 'text' },
      { name: 'timestamp', label: 'Timestamp', type: 'text' }
    ],
    'maintenance-notification': [
      { name: 'name', label: 'User Name', type: 'text', required: true },
      { name: 'title', label: 'Maintenance Title', type: 'text', required: true },
      { name: 'message', label: 'Message', type: 'textarea', required: true },
      { name: 'startTime', label: 'Start Time', type: 'text' },
      { name: 'endTime', label: 'End Time', type: 'text' },
      { name: 'affectedServices', label: 'Affected Services (comma-separated)', type: 'text' }
    ],
    'feature-announcement': [
      { name: 'name', label: 'User Name', type: 'text', required: true },
      { name: 'title', label: 'Feature Title', type: 'text', required: true },
      { name: 'message', label: 'Message', type: 'textarea', required: true },
      { name: 'featureName', label: 'Feature Name', type: 'text' },
      { name: 'benefits', label: 'Benefits (comma-separated)', type: 'text' },
      { name: 'learnMoreUrl', label: 'Learn More URL', type: 'url' },
      { name: 'actionUrl', label: 'Action URL', type: 'url' },
      { name: 'actionText', label: 'Action Text', type: 'text' }
    ]
  }

  const handleFieldChange = (fieldName: string, value: any) => {
    setPreviewData(prev => ({
      ...prev,
      [fieldName]: value
    }))
  }

  const generatePreview = () => {
    try {
      const processedData = { ...previewData }
      
      // Process special fields
      if (processedData.affectedServices && typeof processedData.affectedServices === 'string') {
        processedData.affectedServices = processedData.affectedServices.split(',').map(s => s.trim())
      }
      if (processedData.benefits && typeof processedData.benefits === 'string') {
        processedData.benefits = processedData.benefits.split(',').map(s => s.trim())
      }
      
      const template = generateTemplatePreview(selectedTemplate, processedData)
      setPreviewTemplate(template)
      toast.success("Preview generated successfully")
    } catch (error) {
      toast.error("Failed to generate preview")
      console.error(error)
    }
  }

  const sendTestEmail = async () => {
    if (!testEmail) {
      toast.error("Please enter a test email address")
      return
    }

    setIsLoading(true)
    try {
      const processedData = { ...previewData }
      
      // Process special fields
      if (processedData.affectedServices && typeof processedData.affectedServices === 'string') {
        processedData.affectedServices = processedData.affectedServices.split(',').map(s => s.trim())
      }
      if (processedData.benefits && typeof processedData.benefits === 'string') {
        processedData.benefits = processedData.benefits.split(',').map(s => s.trim())
      }

      const response = await fetch('/api/test-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'templated',
          email: testEmail,
          templateType: selectedTemplate,
          templateData: processedData
        })
      })

      const data = await response.json()
      
      if (data.success) {
        toast.success("Test email sent successfully")
        setResults(prev => [...prev, {
          type: selectedTemplate,
          timestamp: new Date().toISOString(),
          success: true,
          message: data.message,
          messageId: data.messageId
        }])
      } else {
        toast.error(data.error || "Test failed")
        setResults(prev => [...prev, {
          type: selectedTemplate,
          timestamp: new Date().toISOString(),
          success: false,
          error: data.error
        }])
      }
    } catch (error) {
      toast.error("Test failed")
      setResults(prev => [...prev, {
        type: selectedTemplate,
        timestamp: new Date().toISOString(),
        success: false,
        error: "Network error"
      }])
    } finally {
      setIsLoading(false)
    }
  }

  const copyToClipboard = (content: string, type: 'html' | 'text') => {
    navigator.clipboard.writeText(content)
    toast.success(`${type.toUpperCase()} copied to clipboard`)
  }

  const downloadTemplate = (content: string, type: 'html' | 'text') => {
    const blob = new Blob([content], { type: type === 'html' ? 'text/html' : 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${selectedTemplate}-template.${type}`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    toast.success(`Template downloaded as ${type.toUpperCase()}`)
  }

  const currentFields = templateFields[selectedTemplate] || []

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            Email Template Manager
          </CardTitle>
          <CardDescription>
            Create, preview, and test modular email templates
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="builder" className="space-y-4">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="builder">Template Builder</TabsTrigger>
              <TabsTrigger value="preview">Preview</TabsTrigger>
              <TabsTrigger value="test">Send Test</TabsTrigger>
            </TabsList>

            <TabsContent value="builder" className="space-y-4">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="template-select">Select Template Type</Label>
                  <Select value={selectedTemplate} onValueChange={(value) => {
                    setSelectedTemplate(value as TemplateType)
                    setPreviewData({})
                    setPreviewTemplate(null)
                  }}>
                    <SelectTrigger>
                      <SelectValue placeholder="Choose a template" />
                    </SelectTrigger>
                    <SelectContent>
                      {availableTemplates.map((template) => (
                        <SelectItem key={template.type} value={template.type}>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline" className="text-xs">
                              {template.category}
                            </Badge>
                            <span>{template.name}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <p className="text-sm text-gray-500 mt-1">
                    {availableTemplates.find(t => t.type === selectedTemplate)?.description}
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {currentFields.map((field) => (
                    <div key={field.name} className="space-y-2">
                      <Label htmlFor={field.name}>
                        {field.label}
                        {field.required && <span className="text-red-500 ml-1">*</span>}
                      </Label>
                      
                      {field.type === 'textarea' ? (
                        <Textarea
                          id={field.name}
                          placeholder={field.defaultValue}
                          value={previewData[field.name] || ''}
                          onChange={(e) => handleFieldChange(field.name, e.target.value)}
                          rows={3}
                        />
                      ) : field.type === 'select' ? (
                        <Select
                          value={previewData[field.name] || ''}
                          onValueChange={(value) => handleFieldChange(field.name, value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder={`Select ${field.label.toLowerCase()}`} />
                          </SelectTrigger>
                          <SelectContent>
                            {field.options?.map((option) => (
                              <SelectItem key={option} value={option}>
                                {option}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      ) : field.type === 'checkbox' ? (
                        <div className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            id={field.name}
                            checked={previewData[field.name] || false}
                            onChange={(e) => handleFieldChange(field.name, e.target.checked)}
                            className="rounded border-gray-300"
                          />
                          <Label htmlFor={field.name} className="text-sm">
                            {field.label}
                          </Label>
                        </div>
                      ) : (
                        <Input
                          id={field.name}
                          type={field.type}
                          placeholder={field.defaultValue}
                          value={previewData[field.name] || ''}
                          onChange={(e) => handleFieldChange(field.name, e.target.value)}
                        />
                      )}
                    </div>
                  ))}
                </div>

                <Button onClick={generatePreview} className="w-full">
                  <Eye className="mr-2 h-4 w-4" />
                  Generate Preview
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="preview" className="space-y-4">
              {previewTemplate ? (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold">Template Preview</h3>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyToClipboard(previewTemplate.html, 'html')}
                      >
                        <Copy className="mr-1 h-3 w-3" />
                        Copy HTML
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => downloadTemplate(previewTemplate.html, 'html')}
                      >
                        <Download className="mr-1 h-3 w-3" />
                        Download
                      </Button>
                    </div>
                  </div>

                  <Alert>
                    <AlertDescription>
                      <strong>Subject:</strong> {previewTemplate.subject}
                    </AlertDescription>
                  </Alert>

                  <Tabs defaultValue="rendered" className="space-y-4">
                    <TabsList>
                      <TabsTrigger value="rendered">Rendered</TabsTrigger>
                      <TabsTrigger value="html">HTML Source</TabsTrigger>
                      <TabsTrigger value="text">Text Version</TabsTrigger>
                    </TabsList>

                    <TabsContent value="rendered">
                      <div className="border rounded-lg p-4 bg-white max-h-96 overflow-y-auto">
                        <iframe
                          srcDoc={previewTemplate.html}
                          className="w-full h-96 border-0"
                          title="Email Preview"
                        />
                      </div>
                    </TabsContent>

                    <TabsContent value="html">
                      <div className="relative">
                        <Button
                          variant="outline"
                          size="sm"
                          className="absolute top-2 right-2 z-10"
                          onClick={() => copyToClipboard(previewTemplate.html, 'html')}
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                        <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto text-sm max-h-96 overflow-y-auto">
                          <code>{previewTemplate.html}</code>
                        </pre>
                      </div>
                    </TabsContent>

                    <TabsContent value="text">
                      <div className="relative">
                        <Button
                          variant="outline"
                          size="sm"
                          className="absolute top-2 right-2 z-10"
                          onClick={() => copyToClipboard(previewTemplate.text, 'text')}
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                        <pre className="bg-gray-50 p-4 rounded-lg overflow-x-auto text-sm max-h-96 overflow-y-auto border">
                          {previewTemplate.text}
                        </pre>
                      </div>
                    </TabsContent>
                  </Tabs>
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Eye className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Generate a preview to see your template</p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="test" className="space-y-4">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="test-email">Test Email Address</Label>
                  <Input
                    id="test-email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={testEmail}
                    onChange={(e) => setTestEmail(e.target.value)}
                  />
                </div>

                <Button 
                  onClick={sendTestEmail}
                  disabled={isLoading || !testEmail || !previewTemplate}
                  className="w-full"
                >
                  {isLoading ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <Send className="mr-2 h-4 w-4" />
                  )}
                  Send Test Email
                </Button>

                {!previewTemplate && (
                  <Alert>
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      Please generate a preview first before sending a test email.
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Results */}
      {results.length > 0 && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Test Results</CardTitle>
              <Button variant="outline" size="sm" onClick={() => setResults([])}>
                Clear Results
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 max-h-64 overflow-y-auto">
              {results.map((result, index) => (
                <Alert key={index} variant={result.success ? "default" : "destructive"}>
                  <div className="flex items-start gap-3">
                    {result.success ? (
                      <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
                    ) : (
                      <AlertTriangle className="h-4 w-4 text-red-500 mt-0.5" />
                    )}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <Badge variant="outline" className="text-xs">
                          {result.type}
                        </Badge>
                        <span className="text-xs text-gray-500">
                          {new Date(result.timestamp).toLocaleTimeString()}
                        </span>
                      </div>
                      <AlertDescription>
                        {result.success ? result.message : result.error}
                        {result.messageId && (
                          <div className="text-xs text-gray-500 mt-1">
                            Message ID: {result.messageId}
                          </div>
                        )}
                      </AlertDescription>
                    </div>
                  </div>
                </Alert>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}