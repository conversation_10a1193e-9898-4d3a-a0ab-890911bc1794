"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { TrendingUp, Users, UserPlus } from "lucide-react"

// Mock data - in real app, this would come from tRPC and use a proper charting library
const growthData = [
  { month: "Jan", newUsers: 145, totalUsers: 4200 },
  { month: "Feb", newUsers: 189, totalUsers: 4389 },
  { month: "Mar", newUsers: 234, totalUsers: 4623 },
  { month: "Apr", newUsers: 198, totalUsers: 4821 },
  { month: "May", newUsers: 267, totalUsers: 5088 },
  { month: "Jun", newUsers: 159, totalUsers: 5247 },
]

export function UserGrowthChart() {
  const maxUsers = Math.max(...growthData.map(d => d.totalUsers))
  const maxNewUsers = Math.max(...growthData.map(d => d.newUsers))
  const totalNewUsers = growthData.reduce((sum, d) => sum + d.newUsers, 0)
  const avgNewUsers = Math.round(totalNewUsers / growthData.length)

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <CardTitle className="text-lg font-semibold text-protec-navy flex items-center">
          <TrendingUp className="mr-2 h-5 w-5" />
          User Growth
        </CardTitle>
        <Badge variant="secondary" className="bg-green-100 text-green-800">
          <UserPlus className="mr-1 h-3 w-3" />
          +{totalNewUsers} this period
        </Badge>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Key Metrics */}
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-protec-navy">
              {growthData[growthData.length - 1].totalUsers.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">Total Users</p>
          </div>
          <div>
            <div className="text-2xl font-bold text-green-600">
              {avgNewUsers}
            </div>
            <p className="text-xs text-muted-foreground">Avg/Month</p>
          </div>
          <div>
            <div className="text-2xl font-bold text-blue-600">
              {((totalNewUsers / growthData[0].totalUsers) * 100).toFixed(1)}%
            </div>
            <p className="text-xs text-muted-foreground">Growth Rate</p>
          </div>
        </div>

        {/* Simple Bar Chart */}
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-protec-navy">Monthly New Registrations</h4>
          <div className="space-y-3">
            {growthData.map((data, index) => (
              <div key={data.month} className="space-y-1">
                <div className="flex items-center justify-between text-sm">
                  <span className="font-medium">{data.month}</span>
                  <span className="text-muted-foreground">{data.newUsers} new users</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-protec-red h-2 rounded-full transition-all duration-300"
                    style={{ width: `${(data.newUsers / maxNewUsers) * 100}%` }}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Total Users Trend */}
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-protec-navy">Total Users Growth</h4>
          <div className="space-y-3">
            {growthData.map((data, index) => (
              <div key={`total-${data.month}`} className="space-y-1">
                <div className="flex items-center justify-between text-sm">
                  <span className="font-medium">{data.month}</span>
                  <span className="text-muted-foreground">{data.totalUsers.toLocaleString()} total</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${(data.totalUsers / maxUsers) * 100}%` }}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Growth Insights */}
        <div className="pt-4 border-t">
          <h4 className="text-sm font-medium text-protec-navy mb-3">Growth Insights</h4>
          <div className="space-y-2 text-sm">
            <div className="flex items-center justify-between p-2 bg-green-50 rounded-lg">
              <span className="text-green-800">Best performing month</span>
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                May (+267 users)
              </Badge>
            </div>
            <div className="flex items-center justify-between p-2 bg-blue-50 rounded-lg">
              <span className="text-blue-800">Average monthly growth</span>
              <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                {avgNewUsers} users/month
              </Badge>
            </div>
            <div className="flex items-center justify-between p-2 bg-purple-50 rounded-lg">
              <span className="text-purple-800">Growth acceleration</span>
              <Badge variant="secondary" className="bg-purple-100 text-purple-800">
                +23% vs last period
              </Badge>
            </div>
          </div>
        </div>

        {/* User Acquisition Sources */}
        <div className="pt-4 border-t">
          <h4 className="text-sm font-medium text-protec-navy mb-3">Top Acquisition Sources</h4>
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>Referrals</span>
              <div className="flex items-center space-x-2">
                <div className="w-20 bg-gray-200 rounded-full h-2">
                  <div className="bg-protec-red h-2 rounded-full" style={{ width: "65%" }} />
                </div>
                <span className="text-muted-foreground">65%</span>
              </div>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span>Social Media</span>
              <div className="flex items-center space-x-2">
                <div className="w-20 bg-gray-200 rounded-full h-2">
                  <div className="bg-blue-600 h-2 rounded-full" style={{ width: "25%" }} />
                </div>
                <span className="text-muted-foreground">25%</span>
              </div>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span>Direct</span>
              <div className="flex items-center space-x-2">
                <div className="w-20 bg-gray-200 rounded-full h-2">
                  <div className="bg-green-600 h-2 rounded-full" style={{ width: "10%" }} />
                </div>
                <span className="text-muted-foreground">10%</span>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
