"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  Users, 
  Calendar, 
  MessageSquare, 
  Heart,
  TrendingUp,
  TrendingDown,
  Activity,
  DollarSign
} from "lucide-react"

// Mock data - in real app, this would come from tRPC
const overviewData = {
  totalUsers: {
    current: 5247,
    previous: 4892,
    change: 7.3,
    trend: "up"
  },
  activeUsers: {
    current: 3156,
    previous: 2987,
    change: 5.7,
    trend: "up"
  },
  totalEvents: {
    current: 89,
    previous: 76,
    change: 17.1,
    trend: "up"
  },
  totalPosts: {
    current: 1247,
    previous: 1089,
    change: 14.5,
    trend: "up"
  },
  totalDonations: {
    current: 2150000,
    previous: 1890000,
    change: 13.8,
    trend: "up"
  },
  engagement: {
    current: 68.5,
    previous: 64.2,
    change: 6.7,
    trend: "up"
  }
}

interface MetricCardProps {
  title: string
  value: string | number
  change: number
  trend: "up" | "down"
  icon: React.ElementType
  prefix?: string
  suffix?: string
}

function MetricCard({ title, value, change, trend, icon: Icon, prefix = "", suffix = "" }: MetricCardProps) {
  const isPositive = trend === "up"
  
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">
          {title}
        </CardTitle>
        <Icon className={`h-4 w-4 ${isPositive ? 'text-green-600' : 'text-red-600'}`} />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold text-protec-navy">
          {prefix}{typeof value === 'number' ? value.toLocaleString() : value}{suffix}
        </div>
        <div className="flex items-center space-x-2 mt-2">
          {isPositive ? (
            <TrendingUp className="h-4 w-4 text-green-600" />
          ) : (
            <TrendingDown className="h-4 w-4 text-red-600" />
          )}
          <Badge 
            variant="secondary" 
            className={isPositive ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}
          >
            {isPositive ? "+" : ""}{change.toFixed(1)}%
          </Badge>
          <span className="text-xs text-muted-foreground">vs last period</span>
        </div>
      </CardContent>
    </Card>
  )
}

export function AnalyticsOverview() {
  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      <MetricCard
        title="Total Alumni"
        value={overviewData.totalUsers.current}
        change={overviewData.totalUsers.change}
        trend={overviewData.totalUsers.trend}
        icon={Users}
      />
      
      <MetricCard
        title="Active Users (30d)"
        value={overviewData.activeUsers.current}
        change={overviewData.activeUsers.change}
        trend={overviewData.activeUsers.trend}
        icon={Activity}
      />
      
      <MetricCard
        title="Total Events"
        value={overviewData.totalEvents.current}
        change={overviewData.totalEvents.change}
        trend={overviewData.totalEvents.trend}
        icon={Calendar}
      />
      
      <MetricCard
        title="Community Posts"
        value={overviewData.totalPosts.current}
        change={overviewData.totalPosts.change}
        trend={overviewData.totalPosts.trend}
        icon={MessageSquare}
      />
      
      <MetricCard
        title="Total Donations"
        value={overviewData.totalDonations.current}
        change={overviewData.totalDonations.change}
        trend={overviewData.totalDonations.trend}
        icon={DollarSign}
        prefix="R"
      />
      
      <MetricCard
        title="Engagement Rate"
        value={overviewData.engagement.current}
        change={overviewData.engagement.change}
        trend={overviewData.engagement.trend}
        icon={Heart}
        suffix="%"
      />
    </div>
  )
}
