"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { api } from "@/components/providers/trpc-provider"
import { 
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  Area,
  AreaChart
} from "recharts"
import { 
  DollarSign, 
  TrendingUp, 
  Users, 
  Calendar,
  Download,
  Filter,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Clock,
  CreditCard
} from "lucide-react"

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8']

interface PaymentAnalyticsDashboardProps {
  className?: string
}

export function PaymentAnalyticsDashboard({ className }: PaymentAnalyticsDashboardProps) {
  const [timeRange, setTimeRange] = useState('30d')
  const [selectedGateway, setSelectedGateway] = useState('all')

  const { data: stats, isLoading: statsLoading } = api.donations.getStats.useQuery()
  const { data: analytics, isLoading: analyticsLoading } = api.donations.getAnalytics.useQuery({
    timeRange,
    gateway: selectedGateway === 'all' ? undefined : selectedGateway
  })

  // Mock data for demonstration
  const mockRevenueData = [
    { month: 'Jan', revenue: 45000, donations: 120 },
    { month: 'Feb', revenue: 52000, donations: 140 },
    { month: 'Mar', revenue: 48000, donations: 130 },
    { month: 'Apr', revenue: 61000, donations: 165 },
    { month: 'May', revenue: 55000, donations: 150 },
    { month: 'Jun', revenue: 67000, donations: 180 },
  ]

  const mockGatewayData = [
    { name: 'PayFast', value: 65, amount: 450000 },
    { name: 'PayPal', value: 35, amount: 240000 },
  ]

  const mockPurposeData = [
    { name: 'Scholarships', value: 40, amount: 276000 },
    { name: 'Infrastructure', value: 25, amount: 172500 },
    { name: 'General Fund', value: 20, amount: 138000 },
    { name: 'Events', value: 10, amount: 69000 },
    { name: 'Mentorship', value: 5, amount: 34500 },
  ]

  const mockTransactionData = [
    { date: '2024-01-15', amount: 2500, status: 'completed', gateway: 'payfast', donor: 'John Doe' },
    { date: '2024-01-14', amount: 1000, status: 'completed', gateway: 'paypal', donor: 'Jane Smith' },
    { date: '2024-01-14', amount: 500, status: 'failed', gateway: 'payfast', donor: 'Bob Johnson' },
    { date: '2024-01-13', amount: 750, status: 'completed', gateway: 'paypal', donor: 'Alice Brown' },
    { date: '2024-01-13', amount: 1200, status: 'pending', gateway: 'payfast', donor: 'Charlie Wilson' },
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-500" />
      case 'failed':
        return <AlertTriangle className="w-4 h-4 text-red-500" />
      default:
        return <Clock className="w-4 h-4 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'failed':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-protec-navy">Payment Analytics</h2>
          <p className="text-muted-foreground">
            Comprehensive payment and donation insights
          </p>
        </div>
        <div className="flex items-center space-x-4">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={selectedGateway} onValueChange={setSelectedGateway}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Gateways</SelectItem>
              <SelectItem value="payfast">PayFast</SelectItem>
              <SelectItem value="paypal">PayPal</SelectItem>
            </SelectContent>
          </Select>
          
          <Button variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-protec-red" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-protec-navy">
              R{stats?.totalAmount.toLocaleString() || '0'}
            </div>
            <p className="text-xs text-muted-foreground">
              +12.5% from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Donations</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-protec-navy">
              {stats?.totalDonations.toLocaleString() || '0'}
            </div>
            <p className="text-xs text-muted-foreground">
              +8.2% from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Donors</CardTitle>
            <Users className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-protec-navy">
              {stats?.recentDonations || '0'}
            </div>
            <p className="text-xs text-muted-foreground">
              This month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Donation</CardTitle>
            <Calendar className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-protec-navy">
              R{stats?.totalDonations ? Math.round((stats.totalAmount || 0) / stats.totalDonations).toLocaleString() : '0'}
            </div>
            <p className="text-xs text-muted-foreground">
              Per donation
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid gap-6 lg:grid-cols-2">
        {/* Revenue Trend */}
        <Card>
          <CardHeader>
            <CardTitle>Revenue Trend</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={mockRevenueData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip formatter={(value) => [`R${value.toLocaleString()}`, 'Revenue']} />
                <Area 
                  type="monotone" 
                  dataKey="revenue" 
                  stroke="#dc2626" 
                  fill="#dc2626" 
                  fillOpacity={0.1}
                />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Payment Gateway Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Payment Gateway Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={mockGatewayData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {mockGatewayData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip formatter={(value, name) => [`${value}%`, name]} />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Donation Purposes */}
      <Card>
        <CardHeader>
          <CardTitle>Donations by Purpose</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={mockPurposeData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip formatter={(value) => [`R${value.toLocaleString()}`, 'Amount']} />
              <Bar dataKey="amount" fill="#dc2626" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Recent Transactions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <CreditCard className="w-5 h-5" />
            <span>Recent Transactions</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {mockTransactionData.map((transaction, index) => (
              <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  {getStatusIcon(transaction.status)}
                  <div>
                    <p className="font-medium">{transaction.donor}</p>
                    <p className="text-sm text-muted-foreground">
                      {new Date(transaction.date).toLocaleDateString()} • {transaction.gateway}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-medium">R{transaction.amount.toLocaleString()}</p>
                  <Badge className={getStatusColor(transaction.status)}>
                    {transaction.status}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
          
          <div className="mt-4 text-center">
            <Button variant="outline">
              View All Transactions
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Gateway Performance */}
      <div className="grid gap-6 lg:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Gateway Success Rates</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span>PayFast</span>
                </div>
                <div className="text-right">
                  <p className="font-medium">98.5%</p>
                  <p className="text-xs text-muted-foreground">Success rate</p>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span>PayPal</span>
                </div>
                <div className="text-right">
                  <p className="font-medium">97.2%</p>
                  <p className="text-xs text-muted-foreground">Success rate</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Processing Times</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span>PayFast</span>
                </div>
                <div className="text-right">
                  <p className="font-medium">2.3s</p>
                  <p className="text-xs text-muted-foreground">Avg. processing</p>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span>PayPal</span>
                </div>
                <div className="text-right">
                  <p className="font-medium">3.1s</p>
                  <p className="text-xs text-muted-foreground">Avg. processing</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
