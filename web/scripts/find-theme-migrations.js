#!/usr/bin/env node

/**
 * PROTEC Theme Migration Helper
 * 
 * This script scans your codebase for components that need to be migrated
 * from custom PROTEC color classes to shadcn/ui semantic classes.
 */

const fs = require('fs');
const path = require('path');

// Patterns to find
const patterns = [
  { old: 'bg-protec-navy', new: 'bg-primary', type: 'background' },
  { old: 'bg-protec-red', new: 'bg-accent', type: 'background' },
  { old: 'text-protec-navy', new: 'text-primary', type: 'text' },
  { old: 'text-protec-red', new: 'text-accent', type: 'text' },
  { old: 'border-protec-navy', new: 'border-primary', type: 'border' },
  { old: 'border-protec-red', new: 'border-accent', type: 'border' },
  { old: 'border-l-protec-red', new: 'border-l-accent', type: 'border' },
  { old: 'hover:bg-protec-navy', new: 'hover:bg-primary', type: 'hover' },
  { old: 'hover:bg-protec-red', new: 'hover:bg-accent', type: 'hover' },
  { old: 'hover:text-protec-navy', new: 'hover:text-primary', type: 'hover' },
  { old: 'hover:text-protec-red', new: 'hover:text-accent', type: 'hover' },
  { old: 'bg-protec-red/90', new: 'bg-accent/90', type: 'background' },
  { old: 'hover:bg-protec-red/90', new: 'hover:bg-accent/90', type: 'hover' },
  { old: 'bg-blue-50', new: 'bg-muted', type: 'background' },
];

// Directories to scan
const directories = [
  'components',
  'app',
  'pages' // if using pages router
];

function scanFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const findings = [];

    patterns.forEach(pattern => {
      const regex = new RegExp(pattern.old.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
      let match;
      let lineNumber = 1;
      const lines = content.split('\n');
      
      lines.forEach((line, index) => {
        if (regex.test(line)) {
          findings.push({
            file: filePath,
            line: index + 1,
            content: line.trim(),
            pattern: pattern,
            suggestion: line.replace(new RegExp(pattern.old, 'g'), pattern.new)
          });
        }
      });
    });

    return findings;
  } catch (error) {
    console.error(`Error reading file ${filePath}:`, error.message);
    return [];
  }
}

function scanDirectory(dir) {
  const results = [];
  
  try {
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        results.push(...scanDirectory(fullPath));
      } else if (stat.isFile() && (item.endsWith('.tsx') || item.endsWith('.ts') || item.endsWith('.jsx') || item.endsWith('.js'))) {
        results.push(...scanFile(fullPath));
      }
    });
  } catch (error) {
    console.error(`Error scanning directory ${dir}:`, error.message);
  }
  
  return results;
}

function main() {
  console.log('🎨 PROTEC Theme Migration Scanner\n');
  console.log('Scanning for components that need migration to shadcn/ui semantic classes...\n');

  let allFindings = [];

  directories.forEach(dir => {
    if (fs.existsSync(dir)) {
      console.log(`📁 Scanning ${dir}/...`);
      const findings = scanDirectory(dir);
      allFindings.push(...findings);
    }
  });

  if (allFindings.length === 0) {
    console.log('✅ No migration needed! All components are using semantic classes.');
    return;
  }

  console.log(`\n📊 Found ${allFindings.length} instances that need migration:\n`);

  // Group by file
  const byFile = allFindings.reduce((acc, finding) => {
    if (!acc[finding.file]) {
      acc[finding.file] = [];
    }
    acc[finding.file].push(finding);
    return acc;
  }, {});

  Object.entries(byFile).forEach(([file, findings]) => {
    console.log(`📄 ${file}`);
    findings.forEach(finding => {
      console.log(`   Line ${finding.line}: ${finding.pattern.old} → ${finding.pattern.new}`);
      console.log(`   Current: ${finding.content}`);
      console.log(`   Suggested: ${finding.suggestion.trim()}`);
      console.log('');
    });
  });

  // Summary by pattern type
  console.log('📈 Migration Summary:');
  const byType = allFindings.reduce((acc, finding) => {
    const type = finding.pattern.type;
    acc[type] = (acc[type] || 0) + 1;
    return acc;
  }, {});

  Object.entries(byType).forEach(([type, count]) => {
    console.log(`   ${type}: ${count} instances`);
  });

  console.log('\n📚 Next Steps:');
  console.log('1. Review the migration guide: docs/theme-migration-guide.md');
  console.log('2. Update components one by one');
  console.log('3. Test in both light and dark modes');
  console.log('4. Run this script again to track progress');
}

if (require.main === module) {
  main();
}

module.exports = { scanFile, scanDirectory, patterns };