import { prisma } from '@/lib/prisma'

export interface RecommendationScore {
  alumniId: string
  score: number
  reasons: string[]
  matchingFactors: {
    sharedSkills: string[]
    industryMatch: boolean
    locationMatch: boolean
    graduationYearProximity: number
    sharedInterests: string[]
    careerLevelCompatibility: boolean
    mutualConnections: number
  }
}

export interface RecommendationRequest {
  userId: string
  limit?: number
  excludeConnected?: boolean
  minScore?: number
  focusAreas?: ('skills' | 'industry' | 'location' | 'interests' | 'career')[]
}

export class RecommendationEngine {
  private static instance: RecommendationEngine

  static getInstance(): RecommendationEngine {
    if (!RecommendationEngine.instance) {
      RecommendationEngine.instance = new RecommendationEngine()
    }
    return RecommendationEngine.instance
  }

  /**
   * Generate personalized alumni recommendations
   */
  async generateRecommendations(request: RecommendationRequest): Promise<RecommendationScore[]> {
    const { userId, limit = 10, excludeConnected = true, minScore = 0.3, focusAreas } = request

    // Get the current user's profile
    const currentUser = await prisma.alumni.findUnique({
      where: { id: userId },
      include: {
        connections: { select: { id: true } },
        careerHistory: true,
        _count: {
          select: {
            connections: true
          }
        }
      }
    })

    if (!currentUser) {
      throw new Error('User not found')
    }

    // Get potential connections (exclude self and existing connections)
    const excludeIds = [userId]
    if (excludeConnected) {
      excludeIds.push(...currentUser.connections.map(c => c.id))
    }

    const potentialConnections = await prisma.alumni.findMany({
      where: {
        id: { notIn: excludeIds },
        isActive: true
      },
      include: {
        connections: { select: { id: true } },
        careerHistory: true,
        _count: {
          select: {
            connections: true
          }
        }
      }
    })

    // Calculate scores for each potential connection
    const recommendations: RecommendationScore[] = []

    for (const alumni of potentialConnections) {
      const score = await this.calculateCompatibilityScore(currentUser, alumni, focusAreas)
      
      if (score.score >= minScore) {
        recommendations.push(score)
      }
    }

    // Sort by score and return top recommendations
    return recommendations
      .sort((a, b) => b.score - a.score)
      .slice(0, limit)
  }

  /**
   * Calculate compatibility score between two alumni
   */
  private async calculateCompatibilityScore(
    user: any,
    candidate: any,
    focusAreas?: string[]
  ): Promise<RecommendationScore> {
    const weights = this.getWeights(focusAreas)
    let totalScore = 0
    const reasons: string[] = []
    const matchingFactors: any = {
      sharedSkills: [],
      industryMatch: false,
      locationMatch: false,
      graduationYearProximity: 0,
      sharedInterests: [],
      careerLevelCompatibility: false,
      mutualConnections: 0
    }

    // 1. Skills similarity (25% weight)
    const skillsScore = this.calculateSkillsSimilarity(user.skills || [], candidate.skills || [])
    matchingFactors.sharedSkills = this.getSharedItems(user.skills || [], candidate.skills || [])
    totalScore += skillsScore * weights.skills
    
    if (skillsScore > 0.3) {
      reasons.push(`${matchingFactors.sharedSkills.length} shared skills`)
    }

    // 2. Industry match (20% weight)
    const industryScore = this.calculateIndustryMatch(user.industry, candidate.industry)
    matchingFactors.industryMatch = industryScore > 0
    totalScore += industryScore * weights.industry
    
    if (industryScore > 0) {
      reasons.push(`Same industry: ${user.industry}`)
    }

    // 3. Location proximity (15% weight)
    const locationScore = this.calculateLocationProximity(user, candidate)
    matchingFactors.locationMatch = locationScore > 0.5
    totalScore += locationScore * weights.location
    
    if (locationScore > 0.5) {
      reasons.push(`Same location: ${user.city || user.province}`)
    }

    // 4. Graduation year proximity (10% weight)
    const graduationScore = this.calculateGraduationYearProximity(user.graduationYear, candidate.graduationYear)
    matchingFactors.graduationYearProximity = Math.abs(user.graduationYear - candidate.graduationYear)
    totalScore += graduationScore * weights.graduationYear
    
    if (graduationScore > 0.7) {
      reasons.push(`Similar graduation years`)
    }

    // 5. Shared interests (15% weight)
    const interestsScore = this.calculateInterestsSimilarity(user.interests || [], candidate.interests || [])
    matchingFactors.sharedInterests = this.getSharedItems(user.interests || [], candidate.interests || [])
    totalScore += interestsScore * weights.interests
    
    if (interestsScore > 0.3) {
      reasons.push(`${matchingFactors.sharedInterests.length} shared interests`)
    }

    // 6. Career level compatibility (10% weight)
    const careerScore = this.calculateCareerCompatibility(user, candidate)
    matchingFactors.careerLevelCompatibility = careerScore > 0.5
    totalScore += careerScore * weights.career
    
    if (careerScore > 0.5) {
      reasons.push(`Compatible career levels`)
    }

    // 7. Mutual connections (5% weight)
    const mutualScore = await this.calculateMutualConnections(user.id, candidate.id)
    matchingFactors.mutualConnections = mutualScore.count
    totalScore += mutualScore.score * weights.mutualConnections
    
    if (mutualScore.count > 0) {
      reasons.push(`${mutualScore.count} mutual connections`)
    }

    return {
      alumniId: candidate.id,
      score: Math.min(totalScore, 1), // Cap at 1.0
      reasons,
      matchingFactors
    }
  }

  private getWeights(focusAreas?: string[]) {
    const defaultWeights = {
      skills: 0.25,
      industry: 0.20,
      location: 0.15,
      graduationYear: 0.10,
      interests: 0.15,
      career: 0.10,
      mutualConnections: 0.05
    }

    if (!focusAreas || focusAreas.length === 0) {
      return defaultWeights
    }

    // Adjust weights based on focus areas
    const adjustedWeights = { ...defaultWeights }
    const focusWeight = 0.8 / focusAreas.length
    const remainingWeight = 0.2 / (Object.keys(defaultWeights).length - focusAreas.length)

    Object.keys(adjustedWeights).forEach(key => {
      if (focusAreas.includes(key as any)) {
        adjustedWeights[key as keyof typeof adjustedWeights] = focusWeight
      } else {
        adjustedWeights[key as keyof typeof adjustedWeights] = remainingWeight
      }
    })

    return adjustedWeights
  }

  private calculateSkillsSimilarity(skills1: string[], skills2: string[]): number {
    if (skills1.length === 0 || skills2.length === 0) return 0
    
    const shared = this.getSharedItems(skills1, skills2)
    const union = new Set([...skills1, ...skills2])
    
    return shared.length / union.size // Jaccard similarity
  }

  private calculateIndustryMatch(industry1?: string, industry2?: string): number {
    if (!industry1 || !industry2) return 0
    return industry1.toLowerCase() === industry2.toLowerCase() ? 1 : 0
  }

  private calculateLocationProximity(user: any, candidate: any): number {
    let score = 0
    
    // Same country
    if (user.country && candidate.country && user.country === candidate.country) {
      score += 0.3
    }
    
    // Same province
    if (user.province && candidate.province && user.province === candidate.province) {
      score += 0.4
    }
    
    // Same city
    if (user.city && candidate.city && user.city === candidate.city) {
      score += 0.3
    }
    
    return Math.min(score, 1)
  }

  private calculateGraduationYearProximity(year1: number, year2: number): number {
    const diff = Math.abs(year1 - year2)
    if (diff === 0) return 1
    if (diff <= 2) return 0.8
    if (diff <= 5) return 0.6
    if (diff <= 10) return 0.3
    return 0
  }

  private calculateInterestsSimilarity(interests1: string[], interests2: string[]): number {
    if (interests1.length === 0 || interests2.length === 0) return 0
    
    const shared = this.getSharedItems(interests1, interests2)
    const union = new Set([...interests1, ...interests2])
    
    return shared.length / union.size
  }

  private calculateCareerCompatibility(user: any, candidate: any): number {
    // This is a simplified career compatibility calculation
    // In a real system, you'd have more sophisticated career level mapping
    
    const userSeniority = this.estimateSeniority(user)
    const candidateSeniority = this.estimateSeniority(candidate)
    
    const diff = Math.abs(userSeniority - candidateSeniority)
    if (diff <= 1) return 1
    if (diff <= 2) return 0.7
    if (diff <= 3) return 0.4
    return 0.1
  }

  private estimateSeniority(alumni: any): number {
    // Estimate seniority based on graduation year and role
    const yearsExperience = new Date().getFullYear() - alumni.graduationYear
    let seniority = Math.min(yearsExperience / 5, 5) // 0-5 scale
    
    // Adjust based on role keywords
    const role = (alumni.currentRole || '').toLowerCase()
    if (role.includes('senior') || role.includes('lead')) seniority += 1
    if (role.includes('manager') || role.includes('director')) seniority += 2
    if (role.includes('vp') || role.includes('ceo') || role.includes('founder')) seniority += 3
    
    return Math.min(seniority, 5)
  }

  private async calculateMutualConnections(userId: string, candidateId: string): Promise<{ count: number; score: number }> {
    // Find mutual connections
    const mutualConnections = await prisma.alumni.findMany({
      where: {
        AND: [
          { connections: { some: { id: userId } } },
          { connections: { some: { id: candidateId } } }
        ]
      },
      select: { id: true }
    })

    const count = mutualConnections.length
    const score = Math.min(count / 10, 1) // Normalize to 0-1, max at 10 mutual connections
    
    return { count, score }
  }

  private getSharedItems(array1: string[], array2: string[]): string[] {
    const set1 = new Set(array1.map(item => item.toLowerCase()))
    return array2.filter(item => set1.has(item.toLowerCase()))
  }

  /**
   * Get trending alumni (most connected, recently active, etc.)
   */
  async getTrendingAlumni(limit: number = 10): Promise<any[]> {
    return await prisma.alumni.findMany({
      where: { isActive: true },
      orderBy: [
        { connections: { _count: 'desc' } },
        { lastLoginAt: 'desc' }
      ],
      take: limit,
      select: {
        id: true,
        name: true,
        photoUrl: true,
        currentRole: true,
        company: true,
        graduationYear: true,
        _count: {
          select: {
            connections: true
          }
        }
      }
    })
  }

  /**
   * Get alumni recommendations based on similar career paths
   */
  async getCareerPathRecommendations(userId: string, limit: number = 5): Promise<any[]> {
    const user = await prisma.alumni.findUnique({
      where: { id: userId },
      include: { careerHistory: true }
    })

    if (!user) return []

    // Find alumni with similar career progression
    return await prisma.alumni.findMany({
      where: {
        id: { not: userId },
        isActive: true,
        OR: [
          { industry: user.industry },
          { currentRole: { contains: user.currentRole || '', mode: 'insensitive' } }
        ]
      },
      orderBy: { graduationYear: 'asc' },
      take: limit,
      select: {
        id: true,
        name: true,
        photoUrl: true,
        currentRole: true,
        company: true,
        industry: true,
        graduationYear: true,
        careerHistory: true
      }
    })
  }
}

export const recommendationEngine = RecommendationEngine.getInstance()
