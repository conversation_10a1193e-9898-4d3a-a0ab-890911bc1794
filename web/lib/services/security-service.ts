import { createHash, createHmac, randomBytes } from 'crypto'
import { prisma } from '@/lib/prisma'

export interface SecurityEvent {
  type: 'PAYMENT_ATTEMPT' | 'PAYMENT_SUCCESS' | 'PAYMENT_FAILURE' | 'SUSPICIOUS_ACTIVITY' | 'WEBHOOK_RECEIVED'
  userId?: string
  ipAddress: string
  userAgent: string
  metadata: Record<string, any>
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
}

export interface FraudCheckResult {
  isValid: boolean
  riskScore: number
  flags: string[]
  recommendation: 'ALLOW' | 'REVIEW' | 'BLOCK'
}

export class SecurityService {
  private static instance: SecurityService
  private readonly maxFailedAttempts = 5
  private readonly lockoutDuration = 15 * 60 * 1000 // 15 minutes

  static getInstance(): SecurityService {
    if (!SecurityService.instance) {
      SecurityService.instance = new SecurityService()
    }
    return SecurityService.instance
  }

  /**
   * Log security events for audit trail
   */
  async logSecurityEvent(event: SecurityEvent): Promise<void> {
    try {
      await prisma.securityLog.create({
        data: {
          type: event.type,
          userId: event.userId,
          ipAddress: event.ipAddress,
          userAgent: event.userAgent,
          metadata: event.metadata,
          severity: event.severity,
          timestamp: new Date(),
        }
      })

      // Alert on high severity events
      if (event.severity === 'HIGH' || event.severity === 'CRITICAL') {
        await this.sendSecurityAlert(event)
      }
    } catch (error) {
      console.error('Failed to log security event:', error)
    }
  }

  /**
   * Perform fraud detection checks on payment attempts
   */
  async performFraudCheck(
    amount: number,
    currency: string,
    userEmail: string,
    ipAddress: string,
    userAgent: string
  ): Promise<FraudCheckResult> {
    const flags: string[] = []
    let riskScore = 0

    // Check 1: Amount-based risk
    if (amount > 10000) {
      flags.push('HIGH_AMOUNT')
      riskScore += 30
    } else if (amount > 5000) {
      flags.push('MEDIUM_AMOUNT')
      riskScore += 15
    }

    // Check 2: Frequency-based risk
    const recentPayments = await this.getRecentPaymentAttempts(userEmail, 24 * 60 * 60 * 1000) // 24 hours
    if (recentPayments.length > 5) {
      flags.push('HIGH_FREQUENCY')
      riskScore += 40
    } else if (recentPayments.length > 3) {
      flags.push('MEDIUM_FREQUENCY')
      riskScore += 20
    }

    // Check 3: IP-based risk
    const ipRisk = await this.checkIpReputation(ipAddress)
    if (ipRisk.isHighRisk) {
      flags.push('SUSPICIOUS_IP')
      riskScore += 50
    }

    // Check 4: User behavior analysis
    const userRisk = await this.analyzeUserBehavior(userEmail, amount)
    if (userRisk.isAnomalous) {
      flags.push('ANOMALOUS_BEHAVIOR')
      riskScore += userRisk.riskScore
    }

    // Check 5: Geographic risk
    const geoRisk = await this.checkGeographicRisk(ipAddress, userEmail)
    if (geoRisk.isHighRisk) {
      flags.push('GEOGRAPHIC_ANOMALY')
      riskScore += 25
    }

    // Determine recommendation
    let recommendation: 'ALLOW' | 'REVIEW' | 'BLOCK'
    if (riskScore >= 80) {
      recommendation = 'BLOCK'
    } else if (riskScore >= 50) {
      recommendation = 'REVIEW'
    } else {
      recommendation = 'ALLOW'
    }

    return {
      isValid: recommendation === 'ALLOW',
      riskScore,
      flags,
      recommendation
    }
  }

  /**
   * Validate webhook signatures to prevent tampering
   */
  validateWebhookSignature(
    payload: string,
    signature: string,
    secret: string,
    algorithm: 'sha256' | 'md5' = 'sha256'
  ): boolean {
    try {
      const expectedSignature = createHmac(algorithm, secret)
        .update(payload)
        .digest('hex')

      // Use timing-safe comparison
      return this.timingSafeEqual(signature, expectedSignature)
    } catch (error) {
      console.error('Webhook signature validation failed:', error)
      return false
    }
  }

  /**
   * Generate secure tokens for payment sessions
   */
  generateSecureToken(length: number = 32): string {
    return randomBytes(length).toString('hex')
  }

  /**
   * Hash sensitive data for storage
   */
  hashSensitiveData(data: string, salt?: string): { hash: string; salt: string } {
    const usedSalt = salt || randomBytes(16).toString('hex')
    const hash = createHash('sha256')
      .update(data + usedSalt)
      .digest('hex')
    
    return { hash, salt: usedSalt }
  }

  /**
   * Rate limiting for payment attempts
   */
  async checkRateLimit(identifier: string, windowMs: number = 60000, maxAttempts: number = 5): Promise<boolean> {
    const key = `rate_limit:${identifier}`
    const now = Date.now()
    
    try {
      // Get recent attempts (this would use Redis in production)
      const attempts = await this.getRecentAttempts(key, windowMs)
      
      if (attempts.length >= maxAttempts) {
        await this.logSecurityEvent({
          type: 'SUSPICIOUS_ACTIVITY',
          ipAddress: identifier,
          userAgent: 'unknown',
          metadata: { reason: 'RATE_LIMIT_EXCEEDED', attempts: attempts.length },
          severity: 'MEDIUM'
        })
        return false
      }

      // Record this attempt
      await this.recordAttempt(key, now)
      return true
    } catch (error) {
      console.error('Rate limit check failed:', error)
      return true // Fail open for availability
    }
  }

  /**
   * Sanitize and validate input data
   */
  sanitizeInput(input: any): any {
    if (typeof input === 'string') {
      // Remove potentially dangerous characters
      return input.replace(/[<>\"'%;()&+]/g, '')
    }
    
    if (typeof input === 'object' && input !== null) {
      const sanitized: any = {}
      for (const [key, value] of Object.entries(input)) {
        sanitized[this.sanitizeInput(key)] = this.sanitizeInput(value)
      }
      return sanitized
    }
    
    return input
  }

  /**
   * Check if user account is locked due to failed attempts
   */
  async isAccountLocked(userEmail: string): Promise<boolean> {
    try {
      const failedAttempts = await this.getFailedAttempts(userEmail)
      const lastAttempt = failedAttempts[0]
      
      if (failedAttempts.length >= this.maxFailedAttempts) {
        const timeSinceLastAttempt = Date.now() - new Date(lastAttempt.timestamp).getTime()
        return timeSinceLastAttempt < this.lockoutDuration
      }
      
      return false
    } catch (error) {
      console.error('Account lock check failed:', error)
      return false
    }
  }

  /**
   * Record failed payment attempt
   */
  async recordFailedAttempt(userEmail: string, reason: string, metadata: Record<string, any>): Promise<void> {
    await this.logSecurityEvent({
      type: 'PAYMENT_FAILURE',
      userId: userEmail,
      ipAddress: metadata.ipAddress || 'unknown',
      userAgent: metadata.userAgent || 'unknown',
      metadata: { reason, ...metadata },
      severity: 'MEDIUM'
    })
  }

  // Private helper methods
  private async getRecentPaymentAttempts(userEmail: string, timeWindowMs: number): Promise<any[]> {
    const since = new Date(Date.now() - timeWindowMs)
    
    return await prisma.securityLog.findMany({
      where: {
        userId: userEmail,
        type: 'PAYMENT_ATTEMPT',
        timestamp: { gte: since }
      },
      orderBy: { timestamp: 'desc' }
    })
  }

  private async checkIpReputation(ipAddress: string): Promise<{ isHighRisk: boolean }> {
    // In production, this would check against threat intelligence feeds
    // For now, implement basic checks
    const suspiciousPatterns = [
      /^10\./, // Private networks (suspicious for public payments)
      /^192\.168\./, // Private networks
      /^172\.(1[6-9]|2[0-9]|3[01])\./ // Private networks
    ]

    const isPrivateIp = suspiciousPatterns.some(pattern => pattern.test(ipAddress))
    
    return { isHighRisk: isPrivateIp }
  }

  private async analyzeUserBehavior(userEmail: string, amount: number): Promise<{ isAnomalous: boolean; riskScore: number }> {
    // Get user's historical donation patterns
    const historicalDonations = await prisma.donation.findMany({
      where: {
        alumni: { email: userEmail },
        status: 'completed'
      },
      select: { amountZAR: true },
      orderBy: { createdAt: 'desc' },
      take: 10
    })

    if (historicalDonations.length === 0) {
      // New user - moderate risk
      return { isAnomalous: false, riskScore: 10 }
    }

    const avgAmount = historicalDonations.reduce((sum, d) => sum + d.amountZAR, 0) / historicalDonations.length
    const maxAmount = Math.max(...historicalDonations.map(d => d.amountZAR))

    // Check if current amount is significantly higher than usual
    if (amount > avgAmount * 5 || amount > maxAmount * 2) {
      return { isAnomalous: true, riskScore: 30 }
    }

    return { isAnomalous: false, riskScore: 0 }
  }

  private async checkGeographicRisk(ipAddress: string, userEmail: string): Promise<{ isHighRisk: boolean }> {
    // In production, this would use IP geolocation services
    // and compare against user's known locations
    return { isHighRisk: false }
  }

  private timingSafeEqual(a: string, b: string): boolean {
    if (a.length !== b.length) {
      return false
    }

    let result = 0
    for (let i = 0; i < a.length; i++) {
      result |= a.charCodeAt(i) ^ b.charCodeAt(i)
    }

    return result === 0
  }

  private async getRecentAttempts(key: string, windowMs: number): Promise<any[]> {
    // In production, this would use Redis or similar
    // For now, simulate with in-memory storage
    return []
  }

  private async recordAttempt(key: string, timestamp: number): Promise<void> {
    // In production, this would use Redis or similar
    // For now, this is a no-op
  }

  private async getFailedAttempts(userEmail: string): Promise<any[]> {
    const since = new Date(Date.now() - this.lockoutDuration)
    
    return await prisma.securityLog.findMany({
      where: {
        userId: userEmail,
        type: 'PAYMENT_FAILURE',
        timestamp: { gte: since }
      },
      orderBy: { timestamp: 'desc' }
    })
  }

  private async sendSecurityAlert(event: SecurityEvent): Promise<void> {
    // In production, this would send alerts to security team
    console.warn('Security Alert:', event)
  }
}

export const securityService = SecurityService.getInstance()
