// Client-safe user types and enums
export enum UserRole {
  ALUMNI = 'ALUMNI',
  ADMIN = 'ADMIN',
  EVENT_ORGANIZER = 'EVENT_ORGANIZER',
  DONOR_COORDINATOR = 'DONOR_COORDINATOR',
}

export enum MessageStatus {
  SENT = 'SENT',
  DELIVERED = 'DELIVERED',
  READ = 'READ',
}

export enum DonationFrequency {
  ONE_TIME = 'ONE_TIME',
  MONTHLY = 'MONTHLY',
  QUARTERLY = 'QUARTERLY',
  ANNUALLY = 'ANNUALLY',
}

// Role display names
export const ROLE_DISPLAY_NAMES: Record<UserRole, string> = {
  [UserRole.ADMIN]: 'Administrator',
  [UserRole.DONOR_COORDINATOR]: 'Donor Coordinator',
  [UserRole.EVENT_ORGANIZER]: 'Event Organizer',
  [UserRole.ALUMNI]: 'Alumni',
}

// Role descriptions
export const ROLE_DESCRIPTIONS: Record<UserRole, string> = {
  [UserRole.ADMIN]: 'Full system access with content moderation and user management capabilities',
  [UserRole.DONOR_COORDINATOR]: 'Manages donation campaigns and tracks fundraising metrics',
  [UserRole.EVENT_ORGANIZER]: 'Creates and manages events for the alumni community',
  [UserRole.ALUMNI]: 'Standard alumni access to networking, events, and community features',
}

// User interface for client components
export interface User {
  id: string
  email: string
  name: string
  photoUrl?: string
  role: UserRole
  isActive: boolean
}

// Session user interface
export interface SessionUser {
  id: string
  email: string
  name: string
  image?: string
  role: UserRole
  isActive: boolean
}

// Helper functions for role checking
export function isAdmin(role: UserRole | string): boolean {
  return role === UserRole.ADMIN
}

export function isEventOrganizer(role: UserRole | string): boolean {
  return role === UserRole.EVENT_ORGANIZER || isAdmin(role)
}

export function isDonorCoordinator(role: UserRole | string): boolean {
  return role === UserRole.DONOR_COORDINATOR || isAdmin(role)
}

export function canManageUsers(role: UserRole | string): boolean {
  return isAdmin(role)
}

export function canManageEvents(role: UserRole | string): boolean {
  return isEventOrganizer(role)
}

export function canManageDonations(role: UserRole | string): boolean {
  return isDonorCoordinator(role)
}
