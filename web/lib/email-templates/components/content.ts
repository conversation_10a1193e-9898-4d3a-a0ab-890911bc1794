// Content components for email templates

export function createHeading(
  text: string,
  level: 1 | 2 | 3 | 4 = 1,
  color: string = '#012A5B'
): string {
  const sizes = {
    1: '32px',
    2: '28px',
    3: '24px',
    4: '20px'
  }

  const margins = {
    1: '0 0 24px 0',
    2: '0 0 20px 0',
    3: '0 0 16px 0',
    4: '0 0 12px 0'
  }

  return `
    <h${level} style="
      margin: ${margins[level]};
      font-size: ${sizes[level]};
      font-weight: bold;
      color: ${color};
      line-height: 1.2;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    ">
      ${text}
    </h${level}>
  `
}

export function createParagraph(
  text: string,
  options: {
    fontSize?: string
    color?: string
    textAlign?: 'left' | 'center' | 'right'
    margin?: string
  } = {}
): string {
  const {
    fontSize = '16px',
    color = '#374151',
    textAlign = 'left',
    margin = '0 0 16px 0'
  } = options

  return `
    <p style="
      margin: ${margin};
      font-size: ${fontSize};
      line-height: 1.6;
      color: ${color};
      text-align: ${textAlign};
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    ">
      ${text}
    </p>
  `
}

export function createDivider(
  color: string = '#e5e7eb',
  margin: string = '32px 0'
): string {
  return `
    <table role="presentation" cellspacing="0" cellpadding="0" border="0" style="width: 100%; margin: ${margin};">
      <tr>
        <td>
          <div style="
            height: 1px;
            background-color: ${color};
            width: 100%;
          "></div>
        </td>
      </tr>
    </table>
  `
}

export function createSpacer(height: string = '20px'): string {
  return `
    <table role="presentation" cellspacing="0" cellpadding="0" border="0" style="width: 100%;">
      <tr>
        <td style="height: ${height}; line-height: ${height};">&nbsp;</td>
      </tr>
    </table>
  `
}

export function createQuote(
  text: string,
  author?: string,
  authorTitle?: string
): string {
  return `
    <table role="presentation" cellspacing="0" cellpadding="0" border="0" style="width: 100%; margin: 24px 0;">
      <tr>
        <td style="
          border-left: 4px solid #C41E3A;
          padding-left: 20px;
          background-color: #f9fafb;
          padding: 20px 20px 20px 24px;
          border-radius: 0 8px 8px 0;
        ">
          <p style="
            margin: 0 0 ${author ? '12px' : '0'} 0;
            font-size: 18px;
            line-height: 1.5;
            color: #374151;
            font-style: italic;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
          ">
            "${text}"
          </p>
          ${author ? `
            <div style="
              font-size: 14px;
              color: #6b7280;
              font-weight: 600;
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            ">
              — ${author}${authorTitle ? `, ${authorTitle}` : ''}
            </div>
          ` : ''}
        </td>
      </tr>
    </table>
  `
}

export function createCallout(
  title: string,
  content: string,
  backgroundColor: string = '#f0f9ff',
  borderColor: string = '#0ea5e9'
): string {
  return `
    <table role="presentation" cellspacing="0" cellpadding="0" border="0" style="width: 100%; margin: 24px 0;">
      <tr>
        <td style="
          background-color: ${backgroundColor};
          border: 1px solid ${borderColor};
          border-radius: 8px;
          padding: 20px;
        ">
          <h3 style="
            margin: 0 0 12px 0;
            font-size: 18px;
            font-weight: 600;
            color: #012A5B;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
          ">
            ${title}
          </h3>
          <p style="
            margin: 0;
            font-size: 14px;
            line-height: 1.5;
            color: #374151;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
          ">
            ${content}
          </p>
        </td>
      </tr>
    </table>
  `
}

export function createImageBlock(
  src: string,
  alt: string,
  options: {
    width?: string
    height?: string
    align?: 'left' | 'center' | 'right'
    caption?: string
  } = {}
): string {
  const {
    width = 'auto',
    height = 'auto',
    align = 'center',
    caption
  } = options

  return `
    <table role="presentation" cellspacing="0" cellpadding="0" border="0" style="width: 100%; margin: 20px 0;">
      <tr>
        <td style="text-align: ${align};">
          <img src="${src}" 
               alt="${alt}" 
               style="
                 max-width: 100%;
                 height: ${height};
                 width: ${width};
                 border-radius: 8px;
                 display: block;
                 ${align === 'center' ? 'margin: 0 auto;' : ''}
               " />
          ${caption ? `
            <p style="
              margin: 8px 0 0 0;
              font-size: 12px;
              color: #6b7280;
              text-align: ${align};
              font-style: italic;
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            ">
              ${caption}
            </p>
          ` : ''}
        </td>
      </tr>
    </table>
  `
}

export function createTwoColumnLayout(
  leftContent: string,
  rightContent: string,
  leftWidth: string = '50%',
  rightWidth: string = '50%'
): string {
  return `
    <table role="presentation" cellspacing="0" cellpadding="0" border="0" style="width: 100%; margin: 20px 0;">
      <tr>
        <td style="width: ${leftWidth}; vertical-align: top; padding-right: 20px;">
          ${leftContent}
        </td>
        <td style="width: ${rightWidth}; vertical-align: top; padding-left: 20px;">
          ${rightContent}
        </td>
      </tr>
    </table>
  `
}

export function createCard(
  title: string,
  content: string,
  options: {
    backgroundColor?: string
    borderColor?: string
    padding?: string
    borderRadius?: string
  } = {}
): string {
  const {
    backgroundColor = '#ffffff',
    borderColor = '#e5e7eb',
    padding = '24px',
    borderRadius = '8px'
  } = options

  return `
    <table role="presentation" cellspacing="0" cellpadding="0" border="0" style="width: 100%; margin: 20px 0;">
      <tr>
        <td style="
          background-color: ${backgroundColor};
          border: 1px solid ${borderColor};
          border-radius: ${borderRadius};
          padding: ${padding};
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        ">
          <h3 style="
            margin: 0 0 16px 0;
            font-size: 20px;
            font-weight: 600;
            color: #012A5B;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
          ">
            ${title}
          </h3>
          <div>
            ${content}
          </div>
        </td>
      </tr>
    </table>
  `
}

export function createCodeBlock(code: string, language?: string): string {
  return `
    <table role="presentation" cellspacing="0" cellpadding="0" border="0" style="width: 100%; margin: 20px 0;">
      <tr>
        <td style="
          background-color: #1f2937;
          border-radius: 8px;
          padding: 16px;
          overflow-x: auto;
        ">
          ${language ? `
            <div style="
              font-size: 12px;
              color: #9ca3af;
              margin-bottom: 8px;
              font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            ">
              ${language}
            </div>
          ` : ''}
          <pre style="
            margin: 0;
            font-size: 14px;
            line-height: 1.4;
            color: #e5e7eb;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            white-space: pre-wrap;
            word-wrap: break-word;
          "><code>${code}</code></pre>
        </td>
      </tr>
    </table>
  `
}