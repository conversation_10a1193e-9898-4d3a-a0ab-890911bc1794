import { FeatureListProps } from '../types'

export function createFeatureList({ items }: FeatureListProps): string {
  return `
    <table role="presentation" cellspacing="0" cellpadding="0" border="0" style="width: 100%; margin: 20px 0;">
      ${items.map(item => `
        <tr>
          <td style="padding: 16px 0; border-bottom: 1px solid #f3f4f6;">
            <table role="presentation" cellspacing="0" cellpadding="0" border="0" style="width: 100%;">
              <tr>
                <td style="width: 48px; vertical-align: top; padding-right: 16px;">
                  <div style="
                    width: 40px;
                    height: 40px;
                    background: linear-gradient(135deg, #C41E3A, #012A5B);
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 18px;
                    font-weight: bold;
                    text-align: center;
                    line-height: 40px;
                  ">
                    ${item.icon}
                  </div>
                </td>
                <td style="vertical-align: top;">
                  <h3 style="
                    margin: 0 0 8px 0;
                    font-size: 18px;
                    font-weight: 600;
                    color: #012A5B;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
                  ">
                    ${item.title}
                  </h3>
                  <p style="
                    margin: 0;
                    font-size: 14px;
                    line-height: 1.5;
                    color: #6b7280;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
                  ">
                    ${item.description}
                  </p>
                </td>
              </tr>
            </table>
          </td>
        </tr>
      `).join('')}
    </table>
  `
}

export function createTextFeatureList({ items }: FeatureListProps): string {
  return items.map(item => `${item.icon} ${item.title}: ${item.description}`).join('\n')
}

export function createSimpleList(items: string[]): string {
  return `
    <table role="presentation" cellspacing="0" cellpadding="0" border="0" style="width: 100%; margin: 20px 0;">
      ${items.map(item => `
        <tr>
          <td style="padding: 8px 0;">
            <table role="presentation" cellspacing="0" cellpadding="0" border="0">
              <tr>
                <td style="width: 20px; vertical-align: top; padding-right: 12px;">
                  <div style="
                    width: 6px;
                    height: 6px;
                    background-color: #C41E3A;
                    border-radius: 50%;
                    margin-top: 8px;
                  "></div>
                </td>
                <td style="
                  font-size: 14px;
                  line-height: 1.5;
                  color: #374151;
                  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
                ">
                  ${item}
                </td>
              </tr>
            </table>
          </td>
        </tr>
      `).join('')}
    </table>
  `
}

export function createTextSimpleList(items: string[]): string {
  return items.map(item => `• ${item}`).join('\n')
}

export function createCheckList(items: Array<{ text: string; checked: boolean }>): string {
  return `
    <table role="presentation" cellspacing="0" cellpadding="0" border="0" style="width: 100%; margin: 20px 0;">
      ${items.map(item => `
        <tr>
          <td style="padding: 8px 0;">
            <table role="presentation" cellspacing="0" cellpadding="0" border="0">
              <tr>
                <td style="width: 24px; vertical-align: top; padding-right: 12px;">
                  <span style="
                    font-size: 16px;
                    color: ${item.checked ? '#22c55e' : '#d1d5db'};
                  ">
                    ${item.checked ? '✅' : '☐'}
                  </span>
                </td>
                <td style="
                  font-size: 14px;
                  line-height: 1.5;
                  color: ${item.checked ? '#374151' : '#9ca3af'};
                  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
                  ${item.checked ? '' : 'text-decoration: line-through;'}
                ">
                  ${item.text}
                </td>
              </tr>
            </table>
          </td>
        </tr>
      `).join('')}
    </table>
  `
}

export function createStepsList(steps: Array<{ title: string; description: string; completed?: boolean }>): string {
  return `
    <table role="presentation" cellspacing="0" cellpadding="0" border="0" style="width: 100%; margin: 20px 0;">
      ${steps.map((step, index) => `
        <tr>
          <td style="padding: 16px 0; ${index < steps.length - 1 ? 'border-bottom: 1px solid #f3f4f6;' : ''}">
            <table role="presentation" cellspacing="0" cellpadding="0" border="0" style="width: 100%;">
              <tr>
                <td style="width: 40px; vertical-align: top; padding-right: 16px;">
                  <div style="
                    width: 32px;
                    height: 32px;
                    background-color: ${step.completed ? '#22c55e' : '#C41E3A'};
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 14px;
                    font-weight: bold;
                    text-align: center;
                    line-height: 32px;
                  ">
                    ${step.completed ? '✓' : index + 1}
                  </div>
                </td>
                <td style="vertical-align: top;">
                  <h4 style="
                    margin: 0 0 4px 0;
                    font-size: 16px;
                    font-weight: 600;
                    color: #012A5B;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
                  ">
                    ${step.title}
                  </h4>
                  <p style="
                    margin: 0;
                    font-size: 14px;
                    line-height: 1.5;
                    color: #6b7280;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
                  ">
                    ${step.description}
                  </p>
                </td>
              </tr>
            </table>
          </td>
        </tr>
      `).join('')}
    </table>
  `
}