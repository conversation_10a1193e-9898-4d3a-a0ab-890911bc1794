import { EmailLayoutProps } from '../types'

export function createEmailLayout({
  title,
  preheader,
  content,
  branding,
  footer
}: EmailLayoutProps): string {
  return `
<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="x-apple-disable-message-reformatting">
  <title>${title}</title>
  
  ${preheader ? `
  <div style="display: none; font-size: 1px; color: #fefefe; line-height: 1px; font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif; max-height: 0px; max-width: 0px; opacity: 0; overflow: hidden;">
    ${preheader}
  </div>
  ` : ''}
  
  <!--[if mso]>
  <noscript>
    <xml>
      <o:OfficeDocumentSettings>
        <o:PixelsPerInch>96</o:PixelsPerInch>
      </o:OfficeDocumentSettings>
    </xml>
  </noscript>
  <![endif]-->
  
  <style>
    /* Reset styles */
    body, table, td, a { -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; }
    table, td { mso-table-lspace: 0pt; mso-table-rspace: 0pt; }
    img { -ms-interpolation-mode: bicubic; border: 0; outline: none; text-decoration: none; }
    
    /* Base styles */
    body {
      margin: 0 !important;
      padding: 0 !important;
      background-color: #f8fafc;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      font-size: 16px;
      line-height: 1.6;
      color: #374151;
    }
    
    .email-container {
      max-width: 600px;
      margin: 0 auto;
      background-color: #ffffff;
    }
    
    .email-header {
      background: linear-gradient(135deg, ${branding.primaryColor}, ${branding.secondaryColor});
      padding: 40px 20px;
      text-align: center;
    }
    
    .email-logo {
      display: inline-flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 20px;
    }
    
    .email-logo-icon {
      width: 48px;
      height: 48px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: bold;
      font-size: 24px;
      backdrop-filter: blur(10px);
    }
    
    .email-logo-text {
      font-size: 28px;
      font-weight: bold;
      color: white;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .email-content {
      padding: 40px 30px;
    }
    
    .email-footer {
      background-color: #f9fafb;
      padding: 30px;
      text-align: center;
      border-top: 1px solid #e5e7eb;
    }
    
    .email-footer-content {
      color: #6b7280;
      font-size: 14px;
      line-height: 1.5;
    }
    
    .email-footer-links {
      margin: 20px 0;
    }
    
    .email-footer-links a {
      color: ${branding.secondaryColor};
      text-decoration: none;
      margin: 0 10px;
      font-weight: 500;
    }
    
    .email-footer-links a:hover {
      text-decoration: underline;
    }
    
    .social-links {
      margin: 20px 0;
    }
    
    .social-links a {
      display: inline-block;
      margin: 0 8px;
      width: 36px;
      height: 36px;
      background-color: ${branding.primaryColor};
      border-radius: 50%;
      text-align: center;
      line-height: 36px;
      color: white;
      text-decoration: none;
      font-size: 16px;
    }
    
    .social-links a:hover {
      background-color: ${branding.secondaryColor};
    }
    
    /* Responsive styles */
    @media only screen and (max-width: 600px) {
      .email-container {
        width: 100% !important;
        max-width: 100% !important;
      }
      
      .email-header {
        padding: 30px 20px !important;
      }
      
      .email-content {
        padding: 30px 20px !important;
      }
      
      .email-footer {
        padding: 20px !important;
      }
      
      .email-logo-text {
        font-size: 24px !important;
      }
    }
    
    /* Dark mode support */
    @media (prefers-color-scheme: dark) {
      .email-container {
        background-color: #1f2937 !important;
      }
      
      .email-content {
        color: #e5e7eb !important;
      }
      
      .email-footer {
        background-color: #111827 !important;
      }
    }
  </style>
</head>

<body>
  <div class="email-container">
    <!-- Header -->
    <div class="email-header">
      <div class="email-logo">
        <div class="email-logo-icon">P</div>
        <div class="email-logo-text">${branding.organizationName}</div>
      </div>
    </div>
    
    <!-- Content -->
    <div class="email-content">
      ${content}
    </div>
    
    <!-- Footer -->
    <div class="email-footer">
      <div class="email-footer-content">
        ${footer?.customText || `
          <p><strong>${branding.organizationName}</strong></p>
          <p>Building careers in STEM since 1982</p>
        `}
        
        <div class="email-footer-links">
          <a href="${branding.websiteUrl}">Website</a>
          <a href="${branding.websiteUrl}/contact">Contact</a>
          <a href="${branding.websiteUrl}/privacy">Privacy</a>
          <a href="${branding.websiteUrl}/terms">Terms</a>
        </div>
        
        ${branding.socialLinks ? `
        <div class="social-links">
          ${branding.socialLinks.facebook ? `<a href="${branding.socialLinks.facebook}">f</a>` : ''}
          ${branding.socialLinks.twitter ? `<a href="${branding.socialLinks.twitter}">t</a>` : ''}
          ${branding.socialLinks.linkedin ? `<a href="${branding.socialLinks.linkedin}">in</a>` : ''}
          ${branding.socialLinks.instagram ? `<a href="${branding.socialLinks.instagram}">ig</a>` : ''}
        </div>
        ` : ''}
        
        <p style="margin-top: 20px; font-size: 12px; color: #9ca3af;">
          This email was sent to you because you are a member of the ${branding.organizationName}.
          <br>
          If you have any questions, contact us at 
          <a href="mailto:${branding.supportEmail}" style="color: ${branding.secondaryColor};">${branding.supportEmail}</a>
        </p>
        
        ${footer?.unsubscribeUrl ? `
        <p style="margin-top: 15px; font-size: 12px;">
          <a href="${footer.unsubscribeUrl}" style="color: #9ca3af;">Unsubscribe from these emails</a>
        </p>
        ` : ''}
      </div>
    </div>
  </div>
</body>
</html>
  `.trim()
}

export function createTextLayout(content: string, branding: any): string {
  return `
${branding.organizationName}
${'='.repeat(branding.organizationName.length)}

${content}

---

${branding.organizationName}
Building careers in STEM since 1982

Website: ${branding.websiteUrl}
Support: ${branding.supportEmail}

This email was sent to you because you are a member of the ${branding.organizationName}.
If you have any questions, contact us at ${branding.supportEmail}
  `.trim()
}