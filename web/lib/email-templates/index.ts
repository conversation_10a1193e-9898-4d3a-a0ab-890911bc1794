// Main email template system entry point

export * from './types'
export * from './config'

// Components
export * from './components/layout'
export * from './components/button'
export * from './components/alert'
export * from './components/stats'
export * from './components/feature-list'
export * from './components/content'

// Templates
export * from './templates/magic-link'
export * from './templates/welcome'
export * from './templates/events'
export * from './templates/notifications'

// Template factory
import { EmailTemplate, EmailTemplateData } from './types'
import { createMagicLinkTemplate } from './templates/magic-link'
import { createWelcomeTemplate, createWelcomeBackTemplate } from './templates/welcome'
import { 
  createEventInvitationTemplate, 
  createEventReminderTemplate, 
  createEventFollowUpTemplate 
} from './templates/events'
import { 
  createSystemNotificationTemplate,
  createSecurityAlertTemplate,
  createMaintenanceNotificationTemplate,
  createFeatureAnnouncementTemplate
} from './templates/notifications'

export type TemplateType = 
  | 'magic-link'
  | 'welcome'
  | 'welcome-back'
  | 'event-invitation'
  | 'event-reminder'
  | 'event-follow-up'
  | 'system-notification'
  | 'security-alert'
  | 'maintenance-notification'
  | 'feature-announcement'

export interface TemplateFactory {
  create(type: TemplateType, data: EmailTemplateData): EmailTemplate
  getAvailableTemplates(): Array<{
    type: TemplateType
    name: string
    description: string
    category: string
  }>
}

export class EmailTemplateFactory implements TemplateFactory {
  private templates = {
    'magic-link': createMagicLinkTemplate,
    'welcome': createWelcomeTemplate,
    'welcome-back': createWelcomeBackTemplate,
    'event-invitation': createEventInvitationTemplate,
    'event-reminder': createEventReminderTemplate,
    'event-follow-up': createEventFollowUpTemplate,
    'system-notification': createSystemNotificationTemplate,
    'security-alert': createSecurityAlertTemplate,
    'maintenance-notification': createMaintenanceNotificationTemplate,
    'feature-announcement': createFeatureAnnouncementTemplate,
  }

  create(type: TemplateType, data: EmailTemplateData): EmailTemplate {
    const templateFunction = this.templates[type]
    if (!templateFunction) {
      throw new Error(`Template type "${type}" not found`)
    }
    return templateFunction(data as any)
  }

  getAvailableTemplates() {
    return [
      {
        type: 'magic-link' as TemplateType,
        name: 'Magic Link Authentication',
        description: 'Passwordless sign-in email with secure link',
        category: 'Authentication'
      },
      {
        type: 'welcome' as TemplateType,
        name: 'Welcome Email',
        description: 'Welcome new users to the platform',
        category: 'Onboarding'
      },
      {
        type: 'welcome-back' as TemplateType,
        name: 'Welcome Back Email',
        description: 'Welcome returning users with new features',
        category: 'Onboarding'
      },
      {
        type: 'event-invitation' as TemplateType,
        name: 'Event Invitation',
        description: 'Invite users to events and activities',
        category: 'Events'
      },
      {
        type: 'event-reminder' as TemplateType,
        name: 'Event Reminder',
        description: 'Remind users about upcoming events',
        category: 'Events'
      },
      {
        type: 'event-follow-up' as TemplateType,
        name: 'Event Follow-up',
        description: 'Thank users for attending events',
        category: 'Events'
      },
      {
        type: 'system-notification' as TemplateType,
        name: 'System Notification',
        description: 'General system notifications and announcements',
        category: 'Notifications'
      },
      {
        type: 'security-alert' as TemplateType,
        name: 'Security Alert',
        description: 'Security-related notifications and alerts',
        category: 'Security'
      },
      {
        type: 'maintenance-notification' as TemplateType,
        name: 'Maintenance Notification',
        description: 'Scheduled maintenance and downtime notices',
        category: 'System'
      },
      {
        type: 'feature-announcement' as TemplateType,
        name: 'Feature Announcement',
        description: 'Announce new features and improvements',
        category: 'Product'
      }
    ]
  }
}

// Default factory instance
export const emailTemplateFactory = new EmailTemplateFactory()

// Convenience functions
export function createEmailTemplate(type: TemplateType, data: EmailTemplateData): EmailTemplate {
  return emailTemplateFactory.create(type, data)
}

export function getAvailableTemplates() {
  return emailTemplateFactory.getAvailableTemplates()
}

// Template validation
export function validateTemplateData(type: TemplateType, data: EmailTemplateData): {
  isValid: boolean
  errors: string[]
} {
  const errors: string[] = []

  // Common validations
  if (!data.name || typeof data.name !== 'string') {
    errors.push('Name is required and must be a string')
  }

  // Type-specific validations
  switch (type) {
    case 'magic-link':
      if (!data.url || typeof data.url !== 'string') {
        errors.push('URL is required for magic link template')
      }
      if (!data.host || typeof data.host !== 'string') {
        errors.push('Host is required for magic link template')
      }
      if (!data.email || typeof data.email !== 'string') {
        errors.push('Email is required for magic link template')
      }
      break

    case 'event-invitation':
    case 'event-reminder':
    case 'event-follow-up':
      if (!data.eventTitle || typeof data.eventTitle !== 'string') {
        errors.push('Event title is required for event templates')
      }
      if (!data.eventDate || typeof data.eventDate !== 'string') {
        errors.push('Event date is required for event templates')
      }
      if (!data.eventLocation || typeof data.eventLocation !== 'string') {
        errors.push('Event location is required for event templates')
      }
      break

    case 'system-notification':
    case 'security-alert':
    case 'maintenance-notification':
    case 'feature-announcement':
      if (!data.title || typeof data.title !== 'string') {
        errors.push('Title is required for notification templates')
      }
      if (!data.message || typeof data.message !== 'string') {
        errors.push('Message is required for notification templates')
      }
      break
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

// Template preview generator
export function generateTemplatePreview(type: TemplateType, sampleData?: Partial<EmailTemplateData>): EmailTemplate {
  const defaultSampleData = {
    name: 'John Doe',
    email: '<EMAIL>',
    url: 'https://example.com/auth/verify?token=sample',
    host: 'example.com',
    eventTitle: 'PROTEC Alumni Networking Event',
    eventDate: 'March 15, 2024',
    eventTime: '6:00 PM',
    eventLocation: 'Cape Town Convention Centre',
    eventDescription: 'Join us for an evening of networking and professional development.',
    rsvpUrl: 'https://example.com/events/123/rsvp',
    title: 'System Maintenance Scheduled',
    message: 'We will be performing scheduled maintenance on our systems.',
    dashboardUrl: 'https://example.com/dashboard'
  }

  const mergedData = { ...defaultSampleData, ...sampleData }
  return emailTemplateFactory.create(type, mergedData)
}