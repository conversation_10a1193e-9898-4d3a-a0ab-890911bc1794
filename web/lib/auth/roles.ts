import { UserRole } from '@/lib/types/user'

// Role hierarchy and permissions
export const ROLE_HIERARCHY = {
  ADMIN: 4,
  DONOR_COORDINATOR: 3,
  EVENT_ORGANIZER: 2,
  ALUMNI: 1,
} as const

// Permission definitions
export const PERMISSIONS = {
  // User management
  VIEW_ALL_USERS: 'view_all_users',
  EDIT_USER_ROLES: 'edit_user_roles',
  DEACTIVATE_USERS: 'deactivate_users',
  
  // Content moderation
  MODERATE_POSTS: 'moderate_posts',
  MODERATE_COMMENTS: 'moderate_comments',
  DELETE_CONTENT: 'delete_content',
  PIN_POSTS: 'pin_posts',
  
  // Event management
  CREATE_EVENTS: 'create_events',
  EDIT_ALL_EVENTS: 'edit_all_events',
  DELETE_EVENTS: 'delete_events',
  MANAGE_RSVPS: 'manage_rsvps',
  
  // Donation management
  VIEW_ALL_DONATIONS: 'view_all_donations',
  <PERSON><PERSON><PERSON>_CAMPAIGNS: 'manage_campaigns',
  EXPORT_DONATION_DATA: 'export_donation_data',
  
  // Analytics
  VIEW_ANALYTICS: 'view_analytics',
  EXPORT_REPORTS: 'export_reports',
  
  // Basic permissions
  CREATE_POSTS: 'create_posts',
  EDIT_OWN_POSTS: 'edit_own_posts',
  COMMENT: 'comment',
  CONNECT_WITH_ALUMNI: 'connect_with_alumni',
  SEND_MESSAGES: 'send_messages',
  DONATE: 'donate',
  RSVP_EVENTS: 'rsvp_events',
} as const

// Role-based permissions mapping
export const ROLE_PERMISSIONS: Record<UserRole, string[]> = {
  ADMIN: [
    // All permissions
    PERMISSIONS.VIEW_ALL_USERS,
    PERMISSIONS.EDIT_USER_ROLES,
    PERMISSIONS.DEACTIVATE_USERS,
    PERMISSIONS.MODERATE_POSTS,
    PERMISSIONS.MODERATE_COMMENTS,
    PERMISSIONS.DELETE_CONTENT,
    PERMISSIONS.PIN_POSTS,
    PERMISSIONS.CREATE_EVENTS,
    PERMISSIONS.EDIT_ALL_EVENTS,
    PERMISSIONS.DELETE_EVENTS,
    PERMISSIONS.MANAGE_RSVPS,
    PERMISSIONS.VIEW_ALL_DONATIONS,
    PERMISSIONS.MANAGE_CAMPAIGNS,
    PERMISSIONS.EXPORT_DONATION_DATA,
    PERMISSIONS.VIEW_ANALYTICS,
    PERMISSIONS.EXPORT_REPORTS,
    PERMISSIONS.CREATE_POSTS,
    PERMISSIONS.EDIT_OWN_POSTS,
    PERMISSIONS.COMMENT,
    PERMISSIONS.CONNECT_WITH_ALUMNI,
    PERMISSIONS.SEND_MESSAGES,
    PERMISSIONS.DONATE,
    PERMISSIONS.RSVP_EVENTS,
  ],
  
  DONOR_COORDINATOR: [
    PERMISSIONS.VIEW_ALL_DONATIONS,
    PERMISSIONS.MANAGE_CAMPAIGNS,
    PERMISSIONS.EXPORT_DONATION_DATA,
    PERMISSIONS.VIEW_ANALYTICS,
    PERMISSIONS.CREATE_POSTS,
    PERMISSIONS.EDIT_OWN_POSTS,
    PERMISSIONS.COMMENT,
    PERMISSIONS.CONNECT_WITH_ALUMNI,
    PERMISSIONS.SEND_MESSAGES,
    PERMISSIONS.DONATE,
    PERMISSIONS.RSVP_EVENTS,
  ],
  
  EVENT_ORGANIZER: [
    PERMISSIONS.CREATE_EVENTS,
    PERMISSIONS.EDIT_ALL_EVENTS,
    PERMISSIONS.MANAGE_RSVPS,
    PERMISSIONS.CREATE_POSTS,
    PERMISSIONS.EDIT_OWN_POSTS,
    PERMISSIONS.COMMENT,
    PERMISSIONS.CONNECT_WITH_ALUMNI,
    PERMISSIONS.SEND_MESSAGES,
    PERMISSIONS.DONATE,
    PERMISSIONS.RSVP_EVENTS,
  ],
  
  ALUMNI: [
    PERMISSIONS.CREATE_POSTS,
    PERMISSIONS.EDIT_OWN_POSTS,
    PERMISSIONS.COMMENT,
    PERMISSIONS.CONNECT_WITH_ALUMNI,
    PERMISSIONS.SEND_MESSAGES,
    PERMISSIONS.DONATE,
    PERMISSIONS.RSVP_EVENTS,
  ],
}

// Helper functions
export function hasPermission(userRole: UserRole, permission: string): boolean {
  return ROLE_PERMISSIONS[userRole]?.includes(permission) ?? false
}

export function hasAnyPermission(userRole: UserRole, permissions: string[]): boolean {
  return permissions.some(permission => hasPermission(userRole, permission))
}

export function hasAllPermissions(userRole: UserRole, permissions: string[]): boolean {
  return permissions.every(permission => hasPermission(userRole, permission))
}

export function canAccessRole(currentRole: UserRole, targetRole: UserRole): boolean {
  return ROLE_HIERARCHY[currentRole] >= ROLE_HIERARCHY[targetRole]
}

export function isAdmin(userRole: UserRole): boolean {
  return userRole === UserRole.ADMIN
}

export function isEventOrganizer(userRole: UserRole): boolean {
  return userRole === UserRole.EVENT_ORGANIZER || isAdmin(userRole)
}

export function isDonorCoordinator(userRole: UserRole): boolean {
  return userRole === UserRole.DONOR_COORDINATOR || isAdmin(userRole)
}

// Role display names
export const ROLE_DISPLAY_NAMES: Record<UserRole, string> = {
  ADMIN: 'Administrator',
  DONOR_COORDINATOR: 'Donor Coordinator',
  EVENT_ORGANIZER: 'Event Organizer',
  ALUMNI: 'Alumni',
}

// Role descriptions
export const ROLE_DESCRIPTIONS: Record<UserRole, string> = {
  ADMIN: 'Full system access with content moderation and user management capabilities',
  DONOR_COORDINATOR: 'Manages donation campaigns and tracks fundraising metrics',
  EVENT_ORGANIZER: 'Creates and manages events for the alumni community',
  ALUMNI: 'Standard alumni access to networking, events, and community features',
}
