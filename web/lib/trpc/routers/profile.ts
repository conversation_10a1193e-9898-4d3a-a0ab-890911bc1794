import { z } from 'zod'
import { createTRPCRouter, protectedProcedure } from '../server'
import { TRPCError } from '@trpc/server'

// Enhanced profile schemas
const careerHistorySchema = z.object({
  id: z.string().uuid().optional(),
  company: z.string().min(1).max(100),
  position: z.string().min(1).max(100),
  industry: z.string().max(50).optional(),
  startDate: z.date(),
  endDate: z.date().optional(),
  isCurrent: z.boolean().default(false),
  description: z.string().max(500).optional(),
  location: z.string().max(100).optional(),
})

const educationSchema = z.object({
  id: z.string().uuid().optional(),
  institution: z.string().min(1).max(100),
  degree: z.string().min(1).max(100),
  fieldOfStudy: z.string().max(100).optional(),
  startDate: z.date(),
  endDate: z.date().optional(),
  isCurrent: z.boolean().default(false),
  grade: z.string().max(20).optional(),
  description: z.string().max(500).optional(),
})

const protecInvolvementSchema = z.object({
  id: z.string().uuid().optional(),
  programme: z.string().min(1).max(100),
  year: z.number().int().min(1982).max(new Date().getFullYear()),
  role: z.string().max(100).optional(),
  description: z.string().max(500).optional(),
  achievements: z.array(z.string()).default([]),
})

const updateBasicProfileSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  bio: z.string().max(1000).optional(),
  photoUrl: z.string().url().optional(),
  currentRole: z.string().max(100).optional(),
  company: z.string().max(100).optional(),
  industry: z.string().max(50).optional(),
  skills: z.array(z.string()).optional(),
  interests: z.array(z.string()).optional(),
  province: z.string().max(50).optional(),
  city: z.string().max(50).optional(),
  country: z.string().max(50).optional(),
  socialLinks: z.object({
    linkedin: z.string().url().optional(),
    twitter: z.string().url().optional(),
    github: z.string().url().optional(),
    website: z.string().url().optional(),
  }).optional(),
})

const updatePrivacySchema = z.object({
  showEmail: z.boolean().optional(),
  showPhone: z.boolean().optional(),
  showLocation: z.boolean().optional(),
  showConnections: z.boolean().optional(),
  showCareerHistory: z.boolean().optional(),
  showEducation: z.boolean().optional(),
  showProtecInvolvement: z.boolean().optional(),
})

export const profileRouter = createTRPCRouter({
  // Get complete profile with all sections
  getComplete: protectedProcedure
    .query(async ({ ctx }) => {
      const alumni = await ctx.prisma.alumni.findUnique({
        where: { email: ctx.session.user.email! },
        include: {
          careerHistory: {
            orderBy: { startDate: 'desc' },
          },
          education: {
            orderBy: { startDate: 'desc' },
          },
          protecInvolvement: {
            orderBy: { year: 'desc' },
          },
          connections: {
            select: {
              id: true,
              name: true,
              photoUrl: true,
              currentRole: true,
              company: true,
              graduationYear: true,
            },
          },
          _count: {
            select: {
              connections: true,
              posts: true,
              donations: true,
              organizedEvents: true,
            },
          },
        },
      })

      if (!alumni) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Profile not found',
        })
      }

      return alumni
    }),

  // Update basic profile information
  updateBasic: protectedProcedure
    .input(updateBasicProfileSchema)
    .mutation(async ({ ctx, input }) => {
      const alumni = await ctx.prisma.alumni.findUnique({
        where: { email: ctx.session.user.email! },
      })

      if (!alumni) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Profile not found',
        })
      }

      const updatedAlumni = await ctx.prisma.alumni.update({
        where: { id: alumni.id },
        data: input,
      })

      // Log activity
      await ctx.prisma.activity.create({
        data: {
          type: 'PROFILE_UPDATED',
          refId: alumni.id,
          alumniId: alumni.id,
        },
      })

      return updatedAlumni
    }),

  // Update privacy settings
  updatePrivacy: protectedProcedure
    .input(updatePrivacySchema)
    .mutation(async ({ ctx, input }) => {
      const alumni = await ctx.prisma.alumni.findUnique({
        where: { email: ctx.session.user.email! },
      })

      if (!alumni) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Profile not found',
        })
      }

      const currentPrivacy = (alumni.privacy as any) || {}
      const updatedPrivacy = { ...currentPrivacy, ...input }

      const updatedAlumni = await ctx.prisma.alumni.update({
        where: { id: alumni.id },
        data: { privacy: updatedPrivacy },
      })

      return updatedAlumni
    }),

  // Career History Management
  addCareerHistory: protectedProcedure
    .input(careerHistorySchema)
    .mutation(async ({ ctx, input }) => {
      const alumni = await ctx.prisma.alumni.findUnique({
        where: { email: ctx.session.user.email! },
      })

      if (!alumni) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Profile not found',
        })
      }

      // If this is marked as current, update other entries to not be current
      if (input.isCurrent) {
        await ctx.prisma.careerHistory.updateMany({
          where: { alumniId: alumni.id },
          data: { isCurrent: false },
        })
      }

      const careerEntry = await ctx.prisma.careerHistory.create({
        data: {
          ...input,
          alumniId: alumni.id,
        },
      })

      return careerEntry
    }),

  updateCareerHistory: protectedProcedure
    .input(careerHistorySchema.extend({ id: z.string().uuid() }))
    .mutation(async ({ ctx, input }) => {
      const { id, ...data } = input

      const alumni = await ctx.prisma.alumni.findUnique({
        where: { email: ctx.session.user.email! },
      })

      if (!alumni) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Profile not found',
        })
      }

      // Verify ownership
      const existingEntry = await ctx.prisma.careerHistory.findFirst({
        where: { id, alumniId: alumni.id },
      })

      if (!existingEntry) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Career history entry not found',
        })
      }

      // If this is marked as current, update other entries to not be current
      if (data.isCurrent) {
        await ctx.prisma.careerHistory.updateMany({
          where: { alumniId: alumni.id, id: { not: id } },
          data: { isCurrent: false },
        })
      }

      const updatedEntry = await ctx.prisma.careerHistory.update({
        where: { id },
        data,
      })

      return updatedEntry
    }),

  deleteCareerHistory: protectedProcedure
    .input(z.object({ id: z.string().uuid() }))
    .mutation(async ({ ctx, input }) => {
      const alumni = await ctx.prisma.alumni.findUnique({
        where: { email: ctx.session.user.email! },
      })

      if (!alumni) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Profile not found',
        })
      }

      // Verify ownership
      const existingEntry = await ctx.prisma.careerHistory.findFirst({
        where: { id: input.id, alumniId: alumni.id },
      })

      if (!existingEntry) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Career history entry not found',
        })
      }

      await ctx.prisma.careerHistory.delete({
        where: { id: input.id },
      })

      return { success: true }
    }),

  // Education Management
  addEducation: protectedProcedure
    .input(educationSchema)
    .mutation(async ({ ctx, input }) => {
      const alumni = await ctx.prisma.alumni.findUnique({
        where: { email: ctx.session.user.email! },
      })

      if (!alumni) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Profile not found',
        })
      }

      const educationEntry = await ctx.prisma.education.create({
        data: {
          ...input,
          alumniId: alumni.id,
        },
      })

      return educationEntry
    }),

  updateEducation: protectedProcedure
    .input(educationSchema.extend({ id: z.string().uuid() }))
    .mutation(async ({ ctx, input }) => {
      const { id, ...data } = input

      const alumni = await ctx.prisma.alumni.findUnique({
        where: { email: ctx.session.user.email! },
      })

      if (!alumni) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Profile not found',
        })
      }

      // Verify ownership
      const existingEntry = await ctx.prisma.education.findFirst({
        where: { id, alumniId: alumni.id },
      })

      if (!existingEntry) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Education entry not found',
        })
      }

      const updatedEntry = await ctx.prisma.education.update({
        where: { id },
        data,
      })

      return updatedEntry
    }),

  deleteEducation: protectedProcedure
    .input(z.object({ id: z.string().uuid() }))
    .mutation(async ({ ctx, input }) => {
      const alumni = await ctx.prisma.alumni.findUnique({
        where: { email: ctx.session.user.email! },
      })

      if (!alumni) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Profile not found',
        })
      }

      // Verify ownership
      const existingEntry = await ctx.prisma.education.findFirst({
        where: { id: input.id, alumniId: alumni.id },
      })

      if (!existingEntry) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Education entry not found',
        })
      }

      await ctx.prisma.education.delete({
        where: { id: input.id },
      })

      return { success: true }
    }),

  // PROTEC Involvement Management
  addProtecInvolvement: protectedProcedure
    .input(protecInvolvementSchema)
    .mutation(async ({ ctx, input }) => {
      const alumni = await ctx.prisma.alumni.findUnique({
        where: { email: ctx.session.user.email! },
      })

      if (!alumni) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Profile not found',
        })
      }

      const protecEntry = await ctx.prisma.protecInvolvement.create({
        data: {
          ...input,
          alumniId: alumni.id,
        },
      })

      return protecEntry
    }),

  updateProtecInvolvement: protectedProcedure
    .input(protecInvolvementSchema.extend({ id: z.string().uuid() }))
    .mutation(async ({ ctx, input }) => {
      const { id, ...data } = input

      const alumni = await ctx.prisma.alumni.findUnique({
        where: { email: ctx.session.user.email! },
      })

      if (!alumni) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Profile not found',
        })
      }

      // Verify ownership
      const existingEntry = await ctx.prisma.protecInvolvement.findFirst({
        where: { id, alumniId: alumni.id },
      })

      if (!existingEntry) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'PROTEC involvement entry not found',
        })
      }

      const updatedEntry = await ctx.prisma.protecInvolvement.update({
        where: { id },
        data,
      })

      return updatedEntry
    }),

  deleteProtecInvolvement: protectedProcedure
    .input(z.object({ id: z.string().uuid() }))
    .mutation(async ({ ctx, input }) => {
      const alumni = await ctx.prisma.alumni.findUnique({
        where: { email: ctx.session.user.email! },
      })

      if (!alumni) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Profile not found',
        })
      }

      // Verify ownership
      const existingEntry = await ctx.prisma.protecInvolvement.findFirst({
        where: { id: input.id, alumniId: alumni.id },
      })

      if (!existingEntry) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'PROTEC involvement entry not found',
        })
      }

      await ctx.prisma.protecInvolvement.delete({
        where: { id: input.id },
      })

      return { success: true }
    }),

  // Get profile completion percentage
  getCompletionStatus: protectedProcedure
    .query(async ({ ctx }) => {
      const alumni = await ctx.prisma.alumni.findUnique({
        where: { email: ctx.session.user.email! },
        include: {
          careerHistory: true,
          education: true,
          protecInvolvement: true,
        },
      })

      if (!alumni) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Profile not found',
        })
      }

      // Calculate completion percentage
      let completedFields = 0
      const totalFields = 12

      // Basic fields (6 points)
      if (alumni.name) completedFields++
      if (alumni.bio) completedFields++
      if (alumni.photoUrl) completedFields++
      if (alumni.currentRole) completedFields++
      if (alumni.skills && alumni.skills.length > 0) completedFields++
      if (alumni.interests && alumni.interests.length > 0) completedFields++

      // Career history (2 points)
      if (alumni.careerHistory.length > 0) completedFields += 2

      // Education (2 points)
      if (alumni.education.length > 0) completedFields += 2

      // PROTEC involvement (2 points)
      if (alumni.protecInvolvement.length > 0) completedFields += 2

      const completionPercentage = Math.round((completedFields / totalFields) * 100)

      return {
        completionPercentage,
        missingFields: {
          basicInfo: !alumni.name || !alumni.bio || !alumni.photoUrl || !alumni.currentRole,
          skills: !alumni.skills || alumni.skills.length === 0,
          interests: !alumni.interests || alumni.interests.length === 0,
          careerHistory: alumni.careerHistory.length === 0,
          education: alumni.education.length === 0,
          protecInvolvement: alumni.protecInvolvement.length === 0,
        },
      }
    }),
})
