import { z } from 'zod'
import { createTRPCRouter, protectedProcedure, publicProcedure } from '../server'
import { TRPCError } from '@trpc/server'

// Input validation schemas
const createEventSchema = z.object({
  title: z.string().min(1).max(200),
  description: z.string().min(1).max(2000),
  category: z.enum(['networking', 'workshop', 'conference', 'social', 'career', 'mentorship']),
  startTime: z.date(),
  endTime: z.date(),
  location: z.object({
    type: z.enum(['physical', 'virtual', 'hybrid']),
    address: z.string().optional(),
    city: z.string().optional(),
    province: z.string().optional(),
    virtualLink: z.string().url().optional(),
    coordinates: z.object({
      lat: z.number(),
      lng: z.number(),
    }).optional(),
  }),
})

const updateEventSchema = createEventSchema.partial().extend({
  id: z.string().uuid(),
})

const rsvpSchema = z.object({
  eventId: z.string().uuid(),
  status: z.enum(['attending', 'not_attending', 'maybe']),
})

const getEventsSchema = z.object({
  category: z.enum(['networking', 'workshop', 'conference', 'social', 'career', 'mentorship']).optional(),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
  location: z.string().optional(),
  limit: z.number().int().min(1).max(50).default(20),
  cursor: z.string().optional(),
})

export const eventsRouter = createTRPCRouter({
  // Get all events with filtering
  getAll: publicProcedure
    .input(getEventsSchema)
    .query(async ({ ctx, input }) => {
      const { category, startDate, endDate, location, limit, cursor } = input

      const where: any = {}

      if (category) {
        where.category = category
      }

      if (startDate || endDate) {
        where.startTime = {}
        if (startDate) {
          where.startTime.gte = startDate
        }
        if (endDate) {
          where.startTime.lte = endDate
        }
      }

      if (location) {
        where.OR = [
          { location: { path: ['address'], string_contains: location } },
          { location: { path: ['city'], string_contains: location } },
          { location: { path: ['province'], string_contains: location } },
        ]
      }

      const events = await ctx.prisma.event.findMany({
        where,
        take: limit + 1,
        cursor: cursor ? { id: cursor } : undefined,
        orderBy: { startTime: 'asc' },
        include: {
          organizer: {
            select: {
              id: true,
              name: true,
              photoUrl: true,
              currentRole: true,
            },
          },
          _count: {
            select: {
              rsvps: true,
            },
          },
        },
      })

      let nextCursor: typeof cursor | undefined = undefined
      if (events.length > limit) {
        const nextItem = events.pop()
        nextCursor = nextItem!.id
      }

      return {
        events,
        nextCursor,
      }
    }),

  // Get event by ID
  getById: publicProcedure
    .input(z.object({ id: z.string().uuid() }))
    .query(async ({ ctx, input }) => {
      const event = await ctx.prisma.event.findUnique({
        where: { id: input.id },
        include: {
          organizer: {
            select: {
              id: true,
              name: true,
              photoUrl: true,
              currentRole: true,
              graduationYear: true,
            },
          },
          rsvps: {
            include: {
              alumni: {
                select: {
                  id: true,
                  name: true,
                  photoUrl: true,
                  currentRole: true,
                },
              },
            },
          },
        },
      })

      if (!event) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Event not found',
        })
      }

      return event
    }),

  // Create new event
  create: protectedProcedure
    .input(createEventSchema)
    .mutation(async ({ ctx, input }) => {
      const currentAlumni = await ctx.prisma.alumni.findUnique({
        where: { email: ctx.session.user.email! },
      })

      if (!currentAlumni) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Alumni profile not found',
        })
      }

      // Validate dates
      if (input.endTime <= input.startTime) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'End time must be after start time',
        })
      }

      const event = await ctx.prisma.event.create({
        data: {
          ...input,
          organizerId: currentAlumni.id,
        },
        include: {
          organizer: {
            select: {
              id: true,
              name: true,
              photoUrl: true,
              currentRole: true,
            },
          },
        },
      })

      // Log activity
      await ctx.prisma.activity.create({
        data: {
          type: 'EVENT_CREATED',
          refId: event.id,
          alumniId: currentAlumni.id,
        },
      })

      return event
    }),

  // Update event
  update: protectedProcedure
    .input(updateEventSchema)
    .mutation(async ({ ctx, input }) => {
      const { id, ...updateData } = input

      const currentAlumni = await ctx.prisma.alumni.findUnique({
        where: { email: ctx.session.user.email! },
      })

      if (!currentAlumni) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Alumni profile not found',
        })
      }

      // Check if user is the organizer
      const event = await ctx.prisma.event.findUnique({
        where: { id },
      })

      if (!event) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Event not found',
        })
      }

      if (event.organizerId !== currentAlumni.id) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Only the organizer can update this event',
        })
      }

      // Validate dates if provided
      if (updateData.endTime && updateData.startTime && updateData.endTime <= updateData.startTime) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'End time must be after start time',
        })
      }

      const updatedEvent = await ctx.prisma.event.update({
        where: { id },
        data: updateData,
        include: {
          organizer: {
            select: {
              id: true,
              name: true,
              photoUrl: true,
              currentRole: true,
            },
          },
        },
      })

      return updatedEvent
    }),

  // Delete event
  delete: protectedProcedure
    .input(z.object({ id: z.string().uuid() }))
    .mutation(async ({ ctx, input }) => {
      const currentAlumni = await ctx.prisma.alumni.findUnique({
        where: { email: ctx.session.user.email! },
      })

      if (!currentAlumni) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Alumni profile not found',
        })
      }

      // Check if user is the organizer
      const event = await ctx.prisma.event.findUnique({
        where: { id: input.id },
      })

      if (!event) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Event not found',
        })
      }

      if (event.organizerId !== currentAlumni.id) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Only the organizer can delete this event',
        })
      }

      await ctx.prisma.event.delete({
        where: { id: input.id },
      })

      return { success: true }
    }),

  // RSVP to event
  rsvp: protectedProcedure
    .input(rsvpSchema)
    .mutation(async ({ ctx, input }) => {
      const currentAlumni = await ctx.prisma.alumni.findUnique({
        where: { email: ctx.session.user.email! },
      })

      if (!currentAlumni) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Alumni profile not found',
        })
      }

      // Check if event exists
      const event = await ctx.prisma.event.findUnique({
        where: { id: input.eventId },
      })

      if (!event) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Event not found',
        })
      }

      // Upsert RSVP
      const rsvp = await ctx.prisma.rSVP.upsert({
        where: {
          alumniId_eventId: {
            alumniId: currentAlumni.id,
            eventId: input.eventId,
          },
        },
        update: {
          status: input.status,
          responded: new Date(),
        },
        create: {
          status: input.status,
          alumniId: currentAlumni.id,
          eventId: input.eventId,
        },
      })

      // Log activity
      await ctx.prisma.activity.create({
        data: {
          type: 'EVENT_RSVP',
          refId: input.eventId,
          alumniId: currentAlumni.id,
        },
      })

      return rsvp
    }),

  // Get user's RSVPs
  getMyRSVPs: protectedProcedure.query(async ({ ctx }) => {
    const currentAlumni = await ctx.prisma.alumni.findUnique({
      where: { email: ctx.session.user.email! },
    })

    if (!currentAlumni) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Alumni profile not found',
      })
    }

    const rsvps = await ctx.prisma.rSVP.findMany({
      where: { alumniId: currentAlumni.id },
      include: {
        event: {
          include: {
            organizer: {
              select: {
                id: true,
                name: true,
                photoUrl: true,
              },
            },
          },
        },
      },
      orderBy: { event: { startTime: 'asc' } },
    })

    return rsvps
  }),

  // Get events organized by user
  getMyEvents: protectedProcedure.query(async ({ ctx }) => {
    const currentAlumni = await ctx.prisma.alumni.findUnique({
      where: { email: ctx.session.user.email! },
    })

    if (!currentAlumni) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Alumni profile not found',
      })
    }

    const events = await ctx.prisma.event.findMany({
      where: { organizerId: currentAlumni.id },
      include: {
        _count: {
          select: {
            rsvps: true,
          },
        },
      },
      orderBy: { startTime: 'asc' },
    })

    return events
  }),
})
