import { z } from 'zod'
import { createTRPCRouter, protectedProcedure } from '../server'
import { TRPCError } from '@trpc/server'
import { MessageStatus } from '@/lib/types/user'

// Input validation schemas
const createConversationSchema = z.object({
  participantIds: z.array(z.string().uuid()).min(1).max(10),
  title: z.string().optional(),
  isGroup: z.boolean().default(false),
})

const sendMessageSchema = z.object({
  conversationId: z.string().uuid(),
  content: z.string().min(1).max(2000),
  messageType: z.enum(['text', 'image', 'file']).default('text'),
  attachmentUrl: z.string().url().optional(),
})

const getConversationsSchema = z.object({
  limit: z.number().int().min(1).max(50).default(20),
  cursor: z.string().optional(),
})

const getMessagesSchema = z.object({
  conversationId: z.string().uuid(),
  limit: z.number().int().min(1).max(100).default(50),
  cursor: z.string().optional(),
})

const markAsReadSchema = z.object({
  conversationId: z.string().uuid(),
})

const searchConversationsSchema = z.object({
  query: z.string().min(1),
  limit: z.number().int().min(1).max(20).default(10),
})

export const messagesRouter = createTRPCRouter({
  // Get user's conversations
  getConversations: protectedProcedure
    .input(getConversationsSchema)
    .query(async ({ ctx, input }) => {
      const { limit, cursor } = input

      const conversations = await ctx.prisma.conversation.findMany({
        where: {
          participants: {
            some: {
              alumniId: ctx.alumni.id,
              isActive: true,
            },
          },
        },
        include: {
          participants: {
            include: {
              alumni: {
                select: {
                  id: true,
                  name: true,
                  photoUrl: true,
                  currentRole: true,
                },
              },
            },
            where: {
              isActive: true,
            },
          },
          messages: {
            take: 1,
            orderBy: { createdAt: 'desc' },
            include: {
              sender: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
        },
        orderBy: { lastMessageAt: 'desc' },
        take: limit + 1,
        cursor: cursor ? { id: cursor } : undefined,
      })

      let nextCursor: typeof cursor | undefined = undefined
      if (conversations.length > limit) {
        const nextItem = conversations.pop()
        nextCursor = nextItem!.id
      }

      // Calculate unread count for each conversation
      const conversationsWithUnread = await Promise.all(
        conversations.map(async (conversation) => {
          const participant = conversation.participants.find(
            p => p.alumniId === ctx.alumni.id
          )
          
          const unreadCount = await ctx.prisma.message.count({
            where: {
              conversationId: conversation.id,
              senderId: { not: ctx.alumni.id },
              createdAt: {
                gt: participant?.lastReadAt || new Date(0),
              },
            },
          })

          return {
            ...conversation,
            unreadCount,
          }
        })
      )

      return {
        conversations: conversationsWithUnread,
        nextCursor,
      }
    }),

  // Get messages in a conversation
  getMessages: protectedProcedure
    .input(getMessagesSchema)
    .query(async ({ ctx, input }) => {
      const { conversationId, limit, cursor } = input

      // Verify user is participant
      const participant = await ctx.prisma.conversationParticipant.findFirst({
        where: {
          conversationId,
          alumniId: ctx.alumni.id,
          isActive: true,
        },
      })

      if (!participant) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'You are not a participant in this conversation',
        })
      }

      const messages = await ctx.prisma.message.findMany({
        where: { conversationId },
        include: {
          sender: {
            select: {
              id: true,
              name: true,
              photoUrl: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        take: limit + 1,
        cursor: cursor ? { id: cursor } : undefined,
      })

      let nextCursor: typeof cursor | undefined = undefined
      if (messages.length > limit) {
        const nextItem = messages.pop()
        nextCursor = nextItem!.id
      }

      return {
        messages: messages.reverse(), // Reverse to show oldest first
        nextCursor,
      }
    }),

  // Create new conversation
  createConversation: protectedProcedure
    .input(createConversationSchema)
    .mutation(async ({ ctx, input }) => {
      const { participantIds, title, isGroup } = input

      // Add current user to participants if not included
      const allParticipantIds = participantIds.includes(ctx.alumni.id)
        ? participantIds
        : [...participantIds, ctx.alumni.id]

      // Verify all participants exist
      const participants = await ctx.prisma.alumni.findMany({
        where: {
          id: { in: allParticipantIds },
          isActive: true,
        },
        select: { id: true, name: true },
      })

      if (participants.length !== allParticipantIds.length) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'One or more participants not found or inactive',
        })
      }

      // For non-group conversations, check if conversation already exists
      if (!isGroup && allParticipantIds.length === 2) {
        const existingConversation = await ctx.prisma.conversation.findFirst({
          where: {
            isGroup: false,
            participants: {
              every: {
                alumniId: { in: allParticipantIds },
              },
            },
          },
          include: {
            participants: true,
          },
        })

        if (existingConversation && existingConversation.participants.length === 2) {
          return existingConversation
        }
      }

      // Create new conversation
      const conversation = await ctx.prisma.conversation.create({
        data: {
          title,
          isGroup,
          participants: {
            create: allParticipantIds.map(id => ({
              alumniId: id,
            })),
          },
        },
        include: {
          participants: {
            include: {
              alumni: {
                select: {
                  id: true,
                  name: true,
                  photoUrl: true,
                  currentRole: true,
                },
              },
            },
          },
        },
      })

      return conversation
    }),

  // Send message
  sendMessage: protectedProcedure
    .input(sendMessageSchema)
    .mutation(async ({ ctx, input }) => {
      const { conversationId, content, messageType, attachmentUrl } = input

      // Verify user is participant
      const participant = await ctx.prisma.conversationParticipant.findFirst({
        where: {
          conversationId,
          alumniId: ctx.alumni.id,
          isActive: true,
        },
      })

      if (!participant) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'You are not a participant in this conversation',
        })
      }

      // Create message
      const message = await ctx.prisma.message.create({
        data: {
          conversationId,
          senderId: ctx.alumni.id,
          content,
          messageType,
          attachmentUrl,
        },
        include: {
          sender: {
            select: {
              id: true,
              name: true,
              photoUrl: true,
            },
          },
        },
      })

      // Update conversation last message
      await ctx.prisma.conversation.update({
        where: { id: conversationId },
        data: {
          lastMessage: content,
          lastMessageAt: new Date(),
        },
      })

      // Log activity
      await ctx.prisma.activity.create({
        data: {
          type: 'MESSAGE_SENT',
          refId: message.id,
          alumniId: ctx.alumni.id,
        },
      })

      return message
    }),

  // Mark conversation as read
  markAsRead: protectedProcedure
    .input(markAsReadSchema)
    .mutation(async ({ ctx, input }) => {
      const { conversationId } = input

      // Update participant's last read timestamp
      await ctx.prisma.conversationParticipant.updateMany({
        where: {
          conversationId,
          alumniId: ctx.alumni.id,
        },
        data: {
          lastReadAt: new Date(),
        },
      })

      return { success: true }
    }),

  // Search conversations
  searchConversations: protectedProcedure
    .input(searchConversationsSchema)
    .query(async ({ ctx, input }) => {
      const { query, limit } = input

      const conversations = await ctx.prisma.conversation.findMany({
        where: {
          participants: {
            some: {
              alumniId: ctx.alumni.id,
              isActive: true,
            },
          },
          OR: [
            {
              title: {
                contains: query,
                mode: 'insensitive',
              },
            },
            {
              participants: {
                some: {
                  alumni: {
                    name: {
                      contains: query,
                      mode: 'insensitive',
                    },
                  },
                },
              },
            },
          ],
        },
        include: {
          participants: {
            include: {
              alumni: {
                select: {
                  id: true,
                  name: true,
                  photoUrl: true,
                  currentRole: true,
                },
              },
            },
            where: {
              isActive: true,
            },
          },
        },
        take: limit,
        orderBy: { lastMessageAt: 'desc' },
      })

      return conversations
    }),

  // Get unread message count
  getUnreadCount: protectedProcedure
    .query(async ({ ctx }) => {
      const userConversations = await ctx.prisma.conversationParticipant.findMany({
        where: {
          alumniId: ctx.alumni.id,
          isActive: true,
        },
        select: {
          conversationId: true,
          lastReadAt: true,
        },
      })

      let totalUnread = 0

      for (const participant of userConversations) {
        const unreadCount = await ctx.prisma.message.count({
          where: {
            conversationId: participant.conversationId,
            senderId: { not: ctx.alumni.id },
            createdAt: {
              gt: participant.lastReadAt || new Date(0),
            },
          },
        })
        totalUnread += unreadCount
      }

      return { unreadCount: totalUnread }
    }),
})
