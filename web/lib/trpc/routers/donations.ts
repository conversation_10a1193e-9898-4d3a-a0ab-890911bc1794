import { z } from 'zod'
import { createTRPCRouter, protectedProcedure, publicProcedure } from '../server'
import { TRPCError } from '@trpc/server'
import { paymentService } from '@/lib/services/payment-service'

// Input validation schemas
const createDonationSchema = z.object({
  amount: z.number().positive().min(10).max(100000),
  currency: z.enum(['ZAR', 'USD']).default('ZAR'),
  gateway: z.enum(['payfast', 'paypal']),
  purpose: z.enum(['general', 'scholarship', 'infrastructure', 'events', 'mentorship']).default('general'),
  frequency: z.enum(['ONE_TIME', 'MONTHLY', 'QUARTERLY', 'ANNUALLY']).default('ONE_TIME'),
  returnUrl: z.string().url().optional(),
  cancelUrl: z.string().url().optional(),
})

const getDonationsSchema = z.object({
  alumniId: z.string().uuid().optional(),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
  gateway: z.enum(['payfast', 'snapscan', 'ozow']).optional(),
  status: z.enum(['pending', 'completed', 'failed', 'cancelled']).optional(),
  limit: z.number().int().min(1).max(100).default(20),
  cursor: z.string().optional(),
})

const updateDonationStatusSchema = z.object({
  donationId: z.string().uuid(),
  status: z.enum(['pending', 'completed', 'failed', 'cancelled']),
  transactionId: z.string().optional(),
})

export const donationsRouter = createTRPCRouter({
  // Create new donation
  create: protectedProcedure
    .input(createDonationSchema)
    .mutation(async ({ ctx, input }) => {
      const currentAlumni = await ctx.prisma.alumni.findUnique({
        where: { email: ctx.session.user.email! },
      })

      if (!currentAlumni) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Alumni profile not found',
        })
      }

      // Generate a unique donation ID
      const donationId = `PROTEC_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

      // Create donation record
      const donation = await ctx.prisma.donation.create({
        data: {
          id: donationId,
          amountZAR: input.currency === 'ZAR' ? input.amount : 0,
          gateway: input.gateway,
          transactionId: donationId,
          status: 'pending',
          frequency: input.frequency,
          purpose: input.purpose,
          isRecurring: input.frequency !== 'ONE_TIME',
          alumniId: currentAlumni.id,
          metadata: {
            currency: input.currency,
            originalAmount: input.amount,
            gateway: input.gateway
          }
        },
        include: {
          alumni: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      })

      // Log activity
      await ctx.prisma.activity.create({
        data: {
          type: 'DONATION_CREATED',
          refId: donation.id,
          alumniId: currentAlumni.id,
        },
      })

      // Create payment with gateway
      const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000'
      const paymentRequest = {
        amount: input.amount,
        currency: input.currency,
        gateway: input.gateway,
        purpose: input.purpose,
        frequency: input.frequency,
        donorEmail: currentAlumni.email,
        donorName: currentAlumni.name,
        returnUrl: input.returnUrl || `${baseUrl}/donations/success?id=${donationId}`,
        cancelUrl: input.cancelUrl || `${baseUrl}/donations/cancel?id=${donationId}`,
        notifyUrl: `${baseUrl}/api/webhooks/${input.gateway}`,
        donationId
      }

      const paymentResponse = await paymentService.createPayment(paymentRequest)

      if (!paymentResponse.success) {
        // Update donation status to failed
        await ctx.prisma.donation.update({
          where: { id: donationId },
          data: { status: 'failed' }
        })

        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: paymentResponse.error || 'Payment creation failed',
        })
      }

      // Update donation with payment details
      await ctx.prisma.donation.update({
        where: { id: donationId },
        data: {
          metadata: {
            ...donation.metadata as object,
            paymentId: paymentResponse.paymentId,
            paymentMetadata: paymentResponse.metadata
          }
        }
      })

      return {
        ...donation,
        paymentUrl: paymentResponse.paymentUrl,
        paymentId: paymentResponse.paymentId,
      }
    }),

  // Get all donations with filtering
  getAll: publicProcedure
    .input(getDonationsSchema)
    .query(async ({ ctx, input }) => {
      const { alumniId, startDate, endDate, gateway, status, limit, cursor } = input

      const where: any = {}

      if (alumniId) {
        where.alumniId = alumniId
      }

      if (startDate || endDate) {
        where.createdAt = {}
        if (startDate) {
          where.createdAt.gte = startDate
        }
        if (endDate) {
          where.createdAt.lte = endDate
        }
      }

      if (gateway) {
        where.gateway = gateway
      }

      if (status) {
        where.status = status
      }

      const donations = await ctx.prisma.donation.findMany({
        where,
        take: limit + 1,
        cursor: cursor ? { id: cursor } : undefined,
        orderBy: { createdAt: 'desc' },
        include: {
          alumni: {
            select: {
              id: true,
              name: true,
              photoUrl: true,
              graduationYear: true,
            },
          },
        },
      })

      let nextCursor: typeof cursor | undefined = undefined
      if (donations.length > limit) {
        const nextItem = donations.pop()
        nextCursor = nextItem!.id
      }

      return {
        donations,
        nextCursor,
      }
    }),

  // Get user's donations
  getMyDonations: protectedProcedure.query(async ({ ctx }) => {
    const currentAlumni = await ctx.prisma.alumni.findUnique({
      where: { email: ctx.session.user.email! },
    })

    if (!currentAlumni) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Alumni profile not found',
      })
    }

    const donations = await ctx.prisma.donation.findMany({
      where: { alumniId: currentAlumni.id },
      orderBy: { createdAt: 'desc' },
    })

    return donations
  }),

  // Get donation by ID
  getById: protectedProcedure
    .input(z.object({ id: z.string().uuid() }))
    .query(async ({ ctx, input }) => {
      const currentAlumni = await ctx.prisma.alumni.findUnique({
        where: { email: ctx.session.user.email! },
      })

      if (!currentAlumni) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Alumni profile not found',
        })
      }

      const donation = await ctx.prisma.donation.findUnique({
        where: { id: input.id },
        include: {
          alumni: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      })

      if (!donation) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Donation not found',
        })
      }

      // Only allow access to own donations or admin access
      if (donation.alumniId !== currentAlumni.id) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Access denied',
        })
      }

      return donation
    }),

  // Update donation status (typically called by payment webhook)
  updateStatus: protectedProcedure
    .input(updateDonationStatusSchema)
    .mutation(async ({ ctx, input }) => {
      const donation = await ctx.prisma.donation.findUnique({
        where: { id: input.donationId },
      })

      if (!donation) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Donation not found',
        })
      }

      const updatedDonation = await ctx.prisma.donation.update({
        where: { id: input.donationId },
        data: {
          status: input.status,
          ...(input.transactionId && { transactionId: input.transactionId }),
          updatedAt: new Date(),
        },
      })

      // Log activity for completed donations
      if (input.status === 'completed') {
        await ctx.prisma.activity.create({
          data: {
            type: 'DONATION_COMPLETED',
            refId: donation.id,
            alumniId: donation.alumniId,
          },
        })
      }

      return updatedDonation
    }),

  // Get donation statistics
  getStats: publicProcedure.query(async ({ ctx }) => {
    const [totalDonations, totalAmount, recentDonations, topDonors] = await Promise.all([
      ctx.prisma.donation.count({
        where: { status: 'completed' },
      }),
      ctx.prisma.donation.aggregate({
        where: { status: 'completed' },
        _sum: { amountZAR: true },
      }),
      ctx.prisma.donation.count({
        where: {
          status: 'completed',
          createdAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
          },
        },
      }),
      ctx.prisma.donation.groupBy({
        by: ['alumniId'],
        where: { status: 'completed' },
        _sum: { amountZAR: true },
        _count: { id: true },
        orderBy: { _sum: { amountZAR: 'desc' } },
        take: 10,
      }),
    ])

    return {
      totalDonations,
      totalAmount: totalAmount._sum.amountZAR || 0,
      recentDonations,
      topDonors,
    }
  }),

  // Get monthly donation trends
  getMonthlyTrends: publicProcedure
    .input(z.object({
      months: z.number().int().min(1).max(24).default(12),
    }))
    .query(async ({ ctx, input }) => {
      const startDate = new Date()
      startDate.setMonth(startDate.getMonth() - input.months)

      const donations = await ctx.prisma.donation.findMany({
        where: {
          status: 'completed',
          createdAt: { gte: startDate },
        },
        select: {
          amountZAR: true,
          createdAt: true,
        },
      })

      // Group by month
      const monthlyData: Record<string, { amount: number; count: number }> = {}

      donations.forEach(donation => {
        const monthKey = donation.createdAt.toISOString().slice(0, 7) // YYYY-MM
        if (!monthlyData[monthKey]) {
          monthlyData[monthKey] = { amount: 0, count: 0 }
        }
        monthlyData[monthKey].amount += donation.amountZAR
        monthlyData[monthKey].count += 1
      })

      // Convert to array and sort by month
      const trends = Object.entries(monthlyData)
        .map(([month, data]) => ({
          month,
          amount: data.amount,
          count: data.count,
        }))
        .sort((a, b) => a.month.localeCompare(b.month))

      return trends
    }),

  // Get donation statistics
  getStats: publicProcedure.query(async ({ ctx }) => {
    const [totalDonations, totalAmount, recentDonations] = await Promise.all([
      ctx.prisma.donation.count({
        where: { status: 'completed' }
      }),
      ctx.prisma.donation.aggregate({
        where: { status: 'completed' },
        _sum: { amountZAR: true }
      }),
      ctx.prisma.donation.count({
        where: {
          status: 'completed',
          createdAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
          }
        }
      })
    ])

    return {
      totalDonations,
      totalAmount: totalAmount._sum.amountZAR || 0,
      recentDonations,
      topDonors: [] // TODO: Implement top donors query
    }
  }),

  // Update subscription
  updateSubscription: protectedProcedure
    .input(z.object({
      id: z.string().uuid(),
      status: z.enum(['active', 'paused', 'cancelled']).optional(),
      frequency: z.enum(['MONTHLY', 'QUARTERLY', 'ANNUALLY']).optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      const currentAlumni = await ctx.prisma.alumni.findUnique({
        where: { email: ctx.session.user.email! },
      })

      if (!currentAlumni) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Alumni profile not found',
        })
      }

      // Verify ownership
      const donation = await ctx.prisma.donation.findUnique({
        where: { id: input.id },
      })

      if (!donation || donation.alumniId !== currentAlumni.id) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Access denied',
        })
      }

      const updateData: any = {}
      if (input.status) updateData.status = input.status
      if (input.frequency) updateData.frequency = input.frequency

      // Calculate next payment date if frequency changed
      if (input.frequency) {
        const now = new Date()
        switch (input.frequency) {
          case 'MONTHLY':
            updateData.nextPaymentDate = new Date(now.setMonth(now.getMonth() + 1))
            break
          case 'QUARTERLY':
            updateData.nextPaymentDate = new Date(now.setMonth(now.getMonth() + 3))
            break
          case 'ANNUALLY':
            updateData.nextPaymentDate = new Date(now.setFullYear(now.getFullYear() + 1))
            break
        }
      }

      const updatedDonation = await ctx.prisma.donation.update({
        where: { id: input.id },
        data: updateData,
      })

      // Log activity
      await ctx.prisma.activity.create({
        data: {
          type: 'SUBSCRIPTION_UPDATED',
          refId: input.id,
          alumniId: currentAlumni.id,
          metadata: { changes: updateData }
        },
      })

      return updatedDonation
    }),

  // Cancel subscription
  cancelSubscription: protectedProcedure
    .input(z.object({ id: z.string().uuid() }))
    .mutation(async ({ ctx, input }) => {
      const currentAlumni = await ctx.prisma.alumni.findUnique({
        where: { email: ctx.session.user.email! },
      })

      if (!currentAlumni) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Alumni profile not found',
        })
      }

      // Verify ownership
      const donation = await ctx.prisma.donation.findUnique({
        where: { id: input.id },
      })

      if (!donation || donation.alumniId !== currentAlumni.id) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Access denied',
        })
      }

      const updatedDonation = await ctx.prisma.donation.update({
        where: { id: input.id },
        data: {
          status: 'cancelled',
          cancelledAt: new Date(),
          nextPaymentDate: null,
        },
      })

      // Log activity
      await ctx.prisma.activity.create({
        data: {
          type: 'SUBSCRIPTION_CANCELLED',
          refId: input.id,
          alumniId: currentAlumni.id,
        },
      })

      return updatedDonation
    }),

  // Get analytics data
  getAnalytics: publicProcedure
    .input(z.object({
      timeRange: z.string().optional(),
      gateway: z.string().optional(),
    }))
    .query(async ({ ctx, input }) => {
      const { timeRange = '30d', gateway } = input

      // Calculate date range
      const now = new Date()
      let startDate: Date

      switch (timeRange) {
        case '7d':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          break
        case '90d':
          startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
          break
        case '1y':
          startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000)
          break
        default: // 30d
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      }

      const where: any = {
        createdAt: { gte: startDate },
        status: 'completed'
      }

      if (gateway) {
        where.gateway = gateway
      }

      // Get revenue by day/month
      const donations = await ctx.prisma.donation.findMany({
        where,
        select: {
          amountZAR: true,
          createdAt: true,
          gateway: true,
          purpose: true,
        },
        orderBy: { createdAt: 'asc' }
      })

      // Group by gateway
      const gatewayStats = donations.reduce((acc, donation) => {
        const gateway = donation.gateway
        if (!acc[gateway]) {
          acc[gateway] = { count: 0, amount: 0 }
        }
        acc[gateway].count++
        acc[gateway].amount += donation.amountZAR
        return acc
      }, {} as Record<string, { count: number; amount: number }>)

      // Group by purpose
      const purposeStats = donations.reduce((acc, donation) => {
        const purpose = donation.purpose
        if (!acc[purpose]) {
          acc[purpose] = { count: 0, amount: 0 }
        }
        acc[purpose].count++
        acc[purpose].amount += donation.amountZAR
        return acc
      }, {} as Record<string, { count: number; amount: number }>)

      return {
        totalRevenue: donations.reduce((sum, d) => sum + d.amountZAR, 0),
        totalDonations: donations.length,
        gatewayStats,
        purposeStats,
        dailyRevenue: donations // This would need more complex grouping in production
      }
    }),
})
