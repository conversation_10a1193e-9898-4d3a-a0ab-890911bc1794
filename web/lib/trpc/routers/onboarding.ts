import { z } from 'zod'
import { createTRPCRouter, protectedProcedure } from '../server'
import { TRPCError } from '@trpc/server'

// Onboarding step schemas
const personalInfoSchema = z.object({
  name: z.string().min(1, "Name is required").max(100),
  bio: z.string().max(500).optional(),
  photoUrl: z.string().url().optional(),
  currentRole: z.string().max(100).optional(),
  company: z.string().max(100).optional(),
  industry: z.string().max(50).optional(),
  province: z.string().max(50).optional(),
  city: z.string().max(50).optional(),
  country: z.string().max(50).default("South Africa"),
})

const protecBackgroundSchema = z.object({
  graduationYear: z.number().int().min(1982).max(new Date().getFullYear()),
  programmes: z.array(z.string()).min(1, "Please select at least one programme"),
  protecInvolvement: z.array(z.object({
    programme: z.string().min(1),
    year: z.number().int().min(1982).max(new Date().getFullYear()),
    role: z.string().optional(),
    description: z.string().max(500).optional(),
    achievements: z.array(z.string()).default([]),
  })).optional(),
})

const professionalInfoSchema = z.object({
  skills: z.array(z.string()).default([]),
  interests: z.array(z.string()).default([]),
  careerHistory: z.array(z.object({
    company: z.string().min(1),
    position: z.string().min(1),
    industry: z.string().optional(),
    startDate: z.date(),
    endDate: z.date().optional(),
    isCurrent: z.boolean().default(false),
    description: z.string().max(500).optional(),
    location: z.string().optional(),
  })).optional(),
  education: z.array(z.object({
    institution: z.string().min(1),
    degree: z.string().min(1),
    fieldOfStudy: z.string().optional(),
    startDate: z.date(),
    endDate: z.date().optional(),
    isCurrent: z.boolean().default(false),
    grade: z.string().optional(),
    description: z.string().max(500).optional(),
  })).optional(),
})

const preferencesSchema = z.object({
  socialLinks: z.object({
    linkedin: z.string().url().optional(),
    twitter: z.string().url().optional(),
    github: z.string().url().optional(),
    website: z.string().url().optional(),
  }).optional(),
  privacy: z.object({
    showEmail: z.boolean().default(false),
    showPhone: z.boolean().default(false),
    showLocation: z.boolean().default(true),
    showConnections: z.boolean().default(true),
    showCareerHistory: z.boolean().default(true),
    showEducation: z.boolean().default(true),
    showProtecInvolvement: z.boolean().default(true),
  }),
  notifications: z.object({
    emailUpdates: z.boolean().default(true),
    eventReminders: z.boolean().default(true),
    connectionRequests: z.boolean().default(true),
    weeklyDigest: z.boolean().default(true),
  }).optional(),
})

export const onboardingRouter = createTRPCRouter({
  // Get current onboarding status
  getStatus: protectedProcedure
    .query(async ({ ctx }) => {
      const alumni = await ctx.prisma.alumni.findUnique({
        where: { email: ctx.session.user.email! },
        include: {
          careerHistory: true,
          education: true,
          protecInvolvement: true,
        },
      })

      if (!alumni) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Alumni profile not found',
        })
      }

      // Determine onboarding completion status
      const hasBasicInfo = !!(alumni.name && alumni.graduationYear && alumni.programmes.length > 0)
      const hasProtecBackground = !!(alumni.graduationYear && alumni.programmes.length > 0)
      const hasProfessionalInfo = !!(alumni.skills.length > 0 || alumni.currentRole)
      const hasPreferences = !!(alumni.privacy)

      const completedSteps = [
        hasBasicInfo,
        hasProtecBackground,
        hasProfessionalInfo,
        hasPreferences,
      ].filter(Boolean).length

      const isOnboardingComplete = completedSteps === 4

      return {
        alumni,
        onboardingStatus: {
          isComplete: isOnboardingComplete,
          completedSteps,
          totalSteps: 4,
          currentStep: isOnboardingComplete ? 4 : completedSteps + 1,
          stepStatus: {
            personalInfo: hasBasicInfo,
            protecBackground: hasProtecBackground,
            professionalInfo: hasProfessionalInfo,
            preferences: hasPreferences,
          },
        },
      }
    }),

  // Update personal information (Step 1)
  updatePersonalInfo: protectedProcedure
    .input(personalInfoSchema)
    .mutation(async ({ ctx, input }) => {
      const alumni = await ctx.prisma.alumni.findUnique({
        where: { email: ctx.session.user.email! },
      })

      if (!alumni) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Alumni profile not found',
        })
      }

      const updatedAlumni = await ctx.prisma.alumni.update({
        where: { id: alumni.id },
        data: input,
      })

      // Log activity
      await ctx.prisma.activity.create({
        data: {
          type: 'ONBOARDING_PERSONAL_INFO',
          refId: alumni.id,
          alumniId: alumni.id,
        },
      })

      return updatedAlumni
    }),

  // Update PROTEC background (Step 2)
  updateProtecBackground: protectedProcedure
    .input(protecBackgroundSchema)
    .mutation(async ({ ctx, input }) => {
      const alumni = await ctx.prisma.alumni.findUnique({
        where: { email: ctx.session.user.email! },
      })

      if (!alumni) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Alumni profile not found',
        })
      }

      const { protecInvolvement, ...basicData } = input

      // Update basic PROTEC info
      const updatedAlumni = await ctx.prisma.alumni.update({
        where: { id: alumni.id },
        data: basicData,
      })

      // Add PROTEC involvement entries if provided
      if (protecInvolvement && protecInvolvement.length > 0) {
        await ctx.prisma.protecInvolvement.createMany({
          data: protecInvolvement.map(involvement => ({
            ...involvement,
            alumniId: alumni.id,
          })),
        })
      }

      // Log activity
      await ctx.prisma.activity.create({
        data: {
          type: 'ONBOARDING_PROTEC_BACKGROUND',
          refId: alumni.id,
          alumniId: alumni.id,
        },
      })

      return updatedAlumni
    }),

  // Update professional information (Step 3)
  updateProfessionalInfo: protectedProcedure
    .input(professionalInfoSchema)
    .mutation(async ({ ctx, input }) => {
      const alumni = await ctx.prisma.alumni.findUnique({
        where: { email: ctx.session.user.email! },
      })

      if (!alumni) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Alumni profile not found',
        })
      }

      const { careerHistory, education, ...basicData } = input

      // Update basic professional info
      const updatedAlumni = await ctx.prisma.alumni.update({
        where: { id: alumni.id },
        data: basicData,
      })

      // Add career history if provided
      if (careerHistory && careerHistory.length > 0) {
        await ctx.prisma.careerHistory.createMany({
          data: careerHistory.map(career => ({
            ...career,
            alumniId: alumni.id,
          })),
        })
      }

      // Add education if provided
      if (education && education.length > 0) {
        await ctx.prisma.education.createMany({
          data: education.map(edu => ({
            ...edu,
            alumniId: alumni.id,
          })),
        })
      }

      // Log activity
      await ctx.prisma.activity.create({
        data: {
          type: 'ONBOARDING_PROFESSIONAL_INFO',
          refId: alumni.id,
          alumniId: alumni.id,
        },
      })

      return updatedAlumni
    }),

  // Update preferences (Step 4)
  updatePreferences: protectedProcedure
    .input(preferencesSchema)
    .mutation(async ({ ctx, input }) => {
      const alumni = await ctx.prisma.alumni.findUnique({
        where: { email: ctx.session.user.email! },
      })

      if (!alumni) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Alumni profile not found',
        })
      }

      const updatedAlumni = await ctx.prisma.alumni.update({
        where: { id: alumni.id },
        data: {
          socialLinks: input.socialLinks,
          privacy: input.privacy,
        },
      })

      // Log activity
      await ctx.prisma.activity.create({
        data: {
          type: 'ONBOARDING_PREFERENCES',
          refId: alumni.id,
          alumniId: alumni.id,
        },
      })

      return updatedAlumni
    }),

  // Complete onboarding
  completeOnboarding: protectedProcedure
    .mutation(async ({ ctx }) => {
      const alumni = await ctx.prisma.alumni.findUnique({
        where: { email: ctx.session.user.email! },
      })

      if (!alumni) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Alumni profile not found',
        })
      }

      // Update last login and mark as active
      const updatedAlumni = await ctx.prisma.alumni.update({
        where: { id: alumni.id },
        data: {
          lastLoginAt: new Date(),
          isActive: true,
        },
      })

      // Log completion activity
      await ctx.prisma.activity.create({
        data: {
          type: 'ONBOARDING_COMPLETED',
          refId: alumni.id,
          alumniId: alumni.id,
        },
      })

      return {
        success: true,
        alumni: updatedAlumni,
      }
    }),

  // Skip onboarding (minimal setup)
  skipOnboarding: protectedProcedure
    .mutation(async ({ ctx }) => {
      const alumni = await ctx.prisma.alumni.findUnique({
        where: { email: ctx.session.user.email! },
      })

      if (!alumni) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Alumni profile not found',
        })
      }

      // Set minimal required fields
      const updatedAlumni = await ctx.prisma.alumni.update({
        where: { id: alumni.id },
        data: {
          graduationYear: alumni.graduationYear || new Date().getFullYear(),
          programmes: alumni.programmes.length > 0 ? alumni.programmes : ['General'],
          privacy: {
            showEmail: false,
            showPhone: false,
            showLocation: true,
            showConnections: true,
            showCareerHistory: true,
            showEducation: true,
            showProtecInvolvement: true,
          },
          lastLoginAt: new Date(),
          isActive: true,
        },
      })

      // Log skip activity
      await ctx.prisma.activity.create({
        data: {
          type: 'ONBOARDING_SKIPPED',
          refId: alumni.id,
          alumniId: alumni.id,
        },
      })

      return {
        success: true,
        alumni: updatedAlumni,
      }
    }),
})
