/**
 * PROTEC Alumni App - Design System
 * 
 * This file contains the complete design system for the PROTEC Alumni mobile app,
 * including component specifications, animation configurations, and layout guidelines.
 */

import { Colors, Typography, Spacing, BorderRadius, Shadows } from './Colors';

// Animation configurations
export const Animations = {
  // Duration presets
  duration: {
    fast: 150,
    normal: 250,
    slow: 350,
    slower: 500,
  },
  
  // Easing curves
  easing: {
    easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
    easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
    easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
    bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
  },
  
  // Spring configurations for react-native-reanimated
  spring: {
    gentle: {
      damping: 20,
      stiffness: 300,
    },
    bouncy: {
      damping: 15,
      stiffness: 400,
    },
    snappy: {
      damping: 25,
      stiffness: 500,
    },
  },
} as const;

// Component specifications
export const Components = {
  // Button specifications
  button: {
    height: {
      sm: 36,
      md: 44,
      lg: 52,
    },
    padding: {
      sm: { horizontal: Spacing.md, vertical: Spacing.xs },
      md: { horizontal: Spacing.lg, vertical: Spacing.md },
      lg: { horizontal: Spacing.xl, vertical: Spacing.lg },
    },
    borderRadius: BorderRadius.md,
    fontSize: {
      sm: Typography.fontSize.sm,
      md: Typography.fontSize.base,
      lg: Typography.fontSize.lg,
    },
  },
  
  // Input specifications
  input: {
    height: 48,
    padding: { horizontal: Spacing.lg, vertical: Spacing.md },
    borderRadius: BorderRadius.md,
    borderWidth: 1,
    fontSize: Typography.fontSize.base,
  },
  
  // Card specifications
  card: {
    padding: Spacing['2xl'],
    borderRadius: BorderRadius.lg,
    shadow: Shadows.md,
  },
  
  // Modal specifications
  modal: {
    borderRadius: BorderRadius.xl,
    padding: Spacing['3xl'],
    maxWidth: 400,
  },
} as const;

// Layout specifications
export const Layout = {
  // Screen padding
  screenPadding: {
    horizontal: Spacing['2xl'],
    vertical: Spacing.lg,
  },
  
  // Container max widths
  container: {
    sm: 640,
    md: 768,
    lg: 1024,
    xl: 1280,
  },
  
  // Header heights
  header: {
    default: 56,
    large: 72,
  },
  
  // Tab bar height
  tabBar: {
    height: 80,
    paddingBottom: 20, // For safe area
  },
} as const;

// Accessibility specifications
export const Accessibility = {
  // Minimum touch target sizes (iOS HIG & Material Design)
  minTouchTarget: {
    width: 44,
    height: 44,
  },
  
  // Color contrast ratios (WCAG AA compliance)
  contrast: {
    normal: 4.5,  // For normal text
    large: 3,     // For large text (18pt+ or 14pt+ bold)
  },
  
  // Focus indicators
  focus: {
    borderWidth: 2,
    borderColor: Colors.light.primary,
    borderRadius: BorderRadius.md,
  },
} as const;

// Breakpoints for responsive design
export const Breakpoints = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
} as const;

// Z-index scale
export const ZIndex = {
  base: 0,
  dropdown: 1000,
  sticky: 1020,
  fixed: 1030,
  modal: 1040,
  popover: 1050,
  tooltip: 1060,
  toast: 1070,
} as const;

// Icon sizes
export const IconSizes = {
  xs: 12,
  sm: 16,
  md: 20,
  lg: 24,
  xl: 32,
  '2xl': 40,
} as const;

// Common component variants
export const Variants = {
  button: {
    primary: {
      backgroundColor: Colors.light.primary,
      color: Colors.light.textInverse,
    },
    secondary: {
      backgroundColor: Colors.light.backgroundSecondary,
      color: Colors.light.text,
      borderColor: Colors.light.border,
    },
    outline: {
      backgroundColor: 'transparent',
      color: Colors.light.primary,
      borderColor: Colors.light.primary,
    },
    ghost: {
      backgroundColor: 'transparent',
      color: Colors.light.text,
    },
    destructive: {
      backgroundColor: Colors.light.error,
      color: Colors.light.textInverse,
    },
  },
  
  input: {
    default: {
      backgroundColor: Colors.light.background,
      borderColor: Colors.light.border,
      color: Colors.light.text,
    },
    error: {
      backgroundColor: Colors.light.background,
      borderColor: Colors.light.error,
      color: Colors.light.text,
    },
    disabled: {
      backgroundColor: Colors.light.backgroundMuted,
      borderColor: Colors.light.borderMuted,
      color: Colors.light.textMuted,
    },
  },
} as const;

// Export the complete design system
export const DesignSystem = {
  Colors,
  Typography,
  Spacing,
  BorderRadius,
  Shadows,
  Animations,
  Components,
  Layout,
  Accessibility,
  Breakpoints,
  ZIndex,
  IconSizes,
  Variants,
} as const;

export default DesignSystem;
