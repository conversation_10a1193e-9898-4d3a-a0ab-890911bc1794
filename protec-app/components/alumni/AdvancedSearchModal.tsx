import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Button, Input, Modal } from '@/components/ui';
import { DesignSystem } from '@/constants/DesignSystem';
import { Ionicons } from '@expo/vector-icons';
import { useThemeColor } from '@/hooks/useThemeColor';

interface AdvancedSearchFilters {
  query?: string;
  graduationYearRange?: {
    from?: number;
    to?: number;
  };
  programmes?: string[];
  industries?: string[];
  skills?: string[];
  locations?: {
    provinces?: string[];
    cities?: string[];
    countries?: string[];
  };
  careerLevel?: string[];
  interests?: string[];
  sortBy?: 'name' | 'graduationYear' | 'connections' | 'lastLogin';
  sortOrder?: 'asc' | 'desc';
}

interface AdvancedSearchModalProps {
  visible: boolean;
  onClose: () => void;
  onApplyFilters: (filters: AdvancedSearchFilters) => void;
  initialFilters?: AdvancedSearchFilters;
}

const PROGRAMMES = [
  'Computer Science',
  'Engineering',
  'Business Administration',
  'Information Technology',
  'Project Management',
  'Digital Marketing',
  'Data Science',
  'Cybersecurity',
];

const INDUSTRIES = [
  'Technology',
  'Finance',
  'Healthcare',
  'Education',
  'Manufacturing',
  'Retail',
  'Consulting',
  'Government',
  'Non-profit',
];

const PROVINCES = [
  'Gauteng',
  'Western Cape',
  'KwaZulu-Natal',
  'Eastern Cape',
  'Free State',
  'Limpopo',
  'Mpumalanga',
  'North West',
  'Northern Cape',
];

const CAREER_LEVELS = [
  'Entry Level',
  'Mid Level',
  'Senior Level',
  'Management',
  'Executive',
  'Entrepreneur',
];

export const AdvancedSearchModal: React.FC<AdvancedSearchModalProps> = ({
  visible,
  onClose,
  onApplyFilters,
  initialFilters = {},
}) => {
  const [filters, setFilters] = useState<AdvancedSearchFilters>(initialFilters);
  const [activeSection, setActiveSection] = useState<string | null>(null);

  const primaryColor = useThemeColor({}, 'primary');
  const textColor = useThemeColor({}, 'text');

  const updateFilter = (key: keyof AdvancedSearchFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const toggleArrayFilter = (key: keyof AdvancedSearchFilters, value: string) => {
    setFilters(prev => {
      const currentArray = (prev[key] as string[]) || [];
      const newArray = currentArray.includes(value)
        ? currentArray.filter(item => item !== value)
        : [...currentArray, value];
      return { ...prev, [key]: newArray };
    });
  };

  const clearAllFilters = () => {
    setFilters({});
    Alert.alert('Filters Cleared', 'All search filters have been reset.');
  };

  const applyFilters = () => {
    onApplyFilters(filters);
    onClose();
  };

  const renderSection = (title: string, content: React.ReactNode) => {
    const isActive = activeSection === title;
    
    return (
      <View style={styles.section}>
        <TouchableOpacity
          style={styles.sectionHeader}
          onPress={() => setActiveSection(isActive ? null : title)}
        >
          <ThemedText style={styles.sectionTitle}>{title}</ThemedText>
          <Ionicons
            name={isActive ? 'chevron-up' : 'chevron-down'}
            size={20}
            color={textColor}
          />
        </TouchableOpacity>
        
        {isActive && (
          <View style={styles.sectionContent}>
            {content}
          </View>
        )}
      </View>
    );
  };

  const renderMultiSelect = (
    options: string[],
    selectedValues: string[] = [],
    onToggle: (value: string) => void
  ) => (
    <View style={styles.multiSelectContainer}>
      {options.map(option => {
        const isSelected = selectedValues.includes(option);
        return (
          <TouchableOpacity
            key={option}
            style={[
              styles.multiSelectItem,
              isSelected && { backgroundColor: primaryColor + '20', borderColor: primaryColor }
            ]}
            onPress={() => onToggle(option)}
          >
            <ThemedText
              style={[
                styles.multiSelectText,
                isSelected && { color: primaryColor, fontWeight: '600' }
              ]}
            >
              {option}
            </ThemedText>
            {isSelected && (
              <Ionicons name="checkmark" size={16} color={primaryColor} />
            )}
          </TouchableOpacity>
        );
      })}
    </View>
  );

  return (
    <Modal visible={visible} onClose={onClose} title="Advanced Search">
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* Basic Search */}
        <View style={styles.basicSearch}>
          <Input
            label="Search Query"
            value={filters.query || ''}
            onChangeText={(text) => updateFilter('query', text)}
            placeholder="Search by name, role, company, or skills..."
            style={styles.input}
          />
        </View>

        {/* Graduation Year Range */}
        {renderSection(
          'Graduation Year',
          <View style={styles.yearRangeContainer}>
            <View style={styles.yearInputContainer}>
              <Input
                label="From"
                value={filters.graduationYearRange?.from?.toString() || ''}
                onChangeText={(text) => updateFilter('graduationYearRange', {
                  ...filters.graduationYearRange,
                  from: text ? parseInt(text) : undefined
                })}
                placeholder="1982"
                keyboardType="numeric"
                style={styles.yearInput}
              />
            </View>
            <View style={styles.yearInputContainer}>
              <Input
                label="To"
                value={filters.graduationYearRange?.to?.toString() || ''}
                onChangeText={(text) => updateFilter('graduationYearRange', {
                  ...filters.graduationYearRange,
                  to: text ? parseInt(text) : undefined
                })}
                placeholder={new Date().getFullYear().toString()}
                keyboardType="numeric"
                style={styles.yearInput}
              />
            </View>
          </View>
        )}

        {/* Programmes */}
        {renderSection(
          'Programmes',
          renderMultiSelect(
            PROGRAMMES,
            filters.programmes,
            (value) => toggleArrayFilter('programmes', value)
          )
        )}

        {/* Industries */}
        {renderSection(
          'Industries',
          renderMultiSelect(
            INDUSTRIES,
            filters.industries,
            (value) => toggleArrayFilter('industries', value)
          )
        )}

        {/* Provinces */}
        {renderSection(
          'Provinces',
          renderMultiSelect(
            PROVINCES,
            filters.locations?.provinces,
            (value) => {
              const currentLocations = filters.locations || {};
              const currentProvinces = currentLocations.provinces || [];
              const newProvinces = currentProvinces.includes(value)
                ? currentProvinces.filter(p => p !== value)
                : [...currentProvinces, value];
              
              updateFilter('locations', {
                ...currentLocations,
                provinces: newProvinces
              });
            }
          )
        )}

        {/* Career Level */}
        {renderSection(
          'Career Level',
          renderMultiSelect(
            CAREER_LEVELS,
            filters.careerLevel,
            (value) => toggleArrayFilter('careerLevel', value)
          )
        )}

        {/* Sort Options */}
        {renderSection(
          'Sort Options',
          <View style={styles.sortContainer}>
            <View style={styles.sortRow}>
              <ThemedText style={styles.sortLabel}>Sort by:</ThemedText>
              <View style={styles.sortOptions}>
                {['name', 'graduationYear', 'connections'].map(option => (
                  <TouchableOpacity
                    key={option}
                    style={[
                      styles.sortOption,
                      filters.sortBy === option && { backgroundColor: primaryColor + '20' }
                    ]}
                    onPress={() => updateFilter('sortBy', option)}
                  >
                    <ThemedText
                      style={[
                        styles.sortOptionText,
                        filters.sortBy === option && { color: primaryColor }
                      ]}
                    >
                      {option === 'graduationYear' ? 'Year' : option.charAt(0).toUpperCase() + option.slice(1)}
                    </ThemedText>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
            
            <View style={styles.sortRow}>
              <ThemedText style={styles.sortLabel}>Order:</ThemedText>
              <View style={styles.sortOptions}>
                {['asc', 'desc'].map(option => (
                  <TouchableOpacity
                    key={option}
                    style={[
                      styles.sortOption,
                      filters.sortOrder === option && { backgroundColor: primaryColor + '20' }
                    ]}
                    onPress={() => updateFilter('sortOrder', option)}
                  >
                    <ThemedText
                      style={[
                        styles.sortOptionText,
                        filters.sortOrder === option && { color: primaryColor }
                      ]}
                    >
                      {option === 'asc' ? 'A-Z' : 'Z-A'}
                    </ThemedText>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          </View>
        )}
      </ScrollView>

      {/* Action Buttons */}
      <View style={styles.actionButtons}>
        <Button
          title="Clear All"
          onPress={clearAllFilters}
          variant="outline"
          style={styles.actionButton}
        />
        <Button
          title="Apply Filters"
          onPress={applyFilters}
          style={styles.actionButton}
        />
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  basicSearch: {
    marginBottom: DesignSystem.Spacing.lg,
  },
  input: {
    marginBottom: DesignSystem.Spacing.sm,
  },
  section: {
    marginBottom: DesignSystem.Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: DesignSystem.Colors.border,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: DesignSystem.Spacing.md,
  },
  sectionTitle: {
    fontSize: DesignSystem.Typography.h4.fontSize,
    fontWeight: '600',
  },
  sectionContent: {
    paddingBottom: DesignSystem.Spacing.md,
  },
  multiSelectContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: DesignSystem.Spacing.sm,
  },
  multiSelectItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: DesignSystem.Spacing.md,
    paddingVertical: DesignSystem.Spacing.sm,
    borderRadius: DesignSystem.BorderRadius.md,
    borderWidth: 1,
    borderColor: DesignSystem.Colors.border,
    backgroundColor: DesignSystem.Colors.surface,
    gap: DesignSystem.Spacing.xs,
  },
  multiSelectText: {
    fontSize: DesignSystem.Typography.caption.fontSize,
  },
  yearRangeContainer: {
    flexDirection: 'row',
    gap: DesignSystem.Spacing.md,
  },
  yearInputContainer: {
    flex: 1,
  },
  yearInput: {
    flex: 1,
  },
  sortContainer: {
    gap: DesignSystem.Spacing.md,
  },
  sortRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: DesignSystem.Spacing.md,
  },
  sortLabel: {
    fontSize: DesignSystem.Typography.body.fontSize,
    fontWeight: '600',
    minWidth: 80,
  },
  sortOptions: {
    flexDirection: 'row',
    gap: DesignSystem.Spacing.sm,
    flex: 1,
  },
  sortOption: {
    paddingHorizontal: DesignSystem.Spacing.md,
    paddingVertical: DesignSystem.Spacing.sm,
    borderRadius: DesignSystem.BorderRadius.md,
    borderWidth: 1,
    borderColor: DesignSystem.Colors.border,
    backgroundColor: DesignSystem.Colors.surface,
  },
  sortOptionText: {
    fontSize: DesignSystem.Typography.caption.fontSize,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: DesignSystem.Spacing.md,
    paddingTop: DesignSystem.Spacing.lg,
    borderTopWidth: 1,
    borderTopColor: DesignSystem.Colors.border,
  },
  actionButton: {
    flex: 1,
  },
});
