import React, { useState, useEffect } from 'react';
import { View } from 'react-native';
import { AnimatedSplashScreen } from './SplashScreen';

interface SplashScreenManagerProps {
  children: React.ReactNode;
  isAppReady?: boolean;
}

export function SplashScreenManager({ children, isAppReady = true }: SplashScreenManagerProps) {
  const [showSplash, setShowSplash] = useState(true);
  const [appInitialized, setAppInitialized] = useState(false);

  useEffect(() => {
    // Wait for app to be ready before starting splash animation
    if (isAppReady) {
      setAppInitialized(true);
    }
  }, [isAppReady]);

  const handleSplashComplete = () => {
    setShowSplash(false);
  };

  if (!appInitialized || showSplash) {
    return (
      <AnimatedSplashScreen 
        onAnimationComplete={handleSplashComplete}
      />
    );
  }

  return <>{children}</>;
}
