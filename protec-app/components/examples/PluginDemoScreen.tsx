import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
  SafeAreaView,
} from 'react-native';
import {
  ContactsService,
  LocationService,
  CalendarService,
  AuthService,
  SharingService,
  AlumniContact,
  LocationCoordinates,
  AlumniCalendarEvent,
  AuthUser,
  AlumniAchievement,
} from '../../services';

export default function PluginDemoScreen() {
  const [currentUser, setCurrentUser] = useState<AuthUser | null>(null);
  const [currentLocation, setCurrentLocation] = useState<LocationCoordinates | null>(null);

  // Demo: Contacts Service
  const demoContacts = async () => {
    try {
      const hasPermission = await ContactsService.hasPermission();
      if (!hasPermission) {
        const granted = await ContactsService.requestPermission();
        if (!granted) {
          Alert.alert('Permission Required', 'Contacts permission is needed for this demo.');
          return;
        }
      }

      const contacts = await ContactsService.getAllContacts();
      Alert.alert(
        'Contacts Demo',
        `Found ${contacts.length} contacts in your device.\n\nThis feature can help you:\n• Find fellow PROTEC alumni\n• Connect with classmates\n• Build your professional network`
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to access contacts');
    }
  };

  // Demo: Location Service
  const demoLocation = async () => {
    try {
      const hasPermission = await LocationService.hasPermission();
      if (!hasPermission) {
        const granted = await LocationService.requestPermission();
        if (!granted) {
          Alert.alert('Permission Required', 'Location permission is needed for this demo.');
          return;
        }
      }

      const location = await LocationService.getCurrentLocation();
      if (location) {
        setCurrentLocation(location);
        const address = await LocationService.getAddressFromCoordinates(location);
        Alert.alert(
          'Location Demo',
          `Your current location:\n${address}\n\nThis feature can help you:\n• Find nearby alumni events\n• Discover local meetups\n• Get directions to events`
        );
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to get location');
    }
  };

  // Demo: Calendar Service
  const demoCalendar = async () => {
    try {
      const hasPermission = await CalendarService.hasPermission();
      if (!hasPermission) {
        const granted = await CalendarService.requestPermission();
        if (!granted) {
          Alert.alert('Permission Required', 'Calendar permission is needed for this demo.');
          return;
        }
      }

      // Create a sample alumni event
      const sampleEvent: AlumniCalendarEvent = {
        title: 'PROTEC Alumni Networking Event',
        startDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 1 week from now
        endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000 + 2 * 60 * 60 * 1000), // 2 hours later
        location: 'PROTEC Campus, Seattle, WA',
        notes: 'Join fellow PROTEC graduates for networking and career discussions.',
      };

      const eventId = await CalendarService.addEvent(sampleEvent);
      if (eventId) {
        Alert.alert(
          'Calendar Demo',
          'Sample alumni event added to your calendar!\n\nThis feature can help you:\n• Save event dates\n• Set reminders\n• Never miss alumni gatherings'
        );
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to add calendar event');
    }
  };

  // Demo: Auth Service
  const demoAuth = async () => {
    Alert.alert(
      'Authentication Demo',
      'This would normally open OAuth flows for:\n\n• Google Sign-In\n• LinkedIn Login\n• Microsoft Account\n\nThese help you:\n• Connect with professional networks\n• Import career information\n• Find alumni connections',
      [
        { text: 'Cancel' },
        {
          text: 'Demo Google',
          onPress: async () => {
            // In a real app, this would call AuthService.signInWithGoogle()
            const mockUser: AuthUser = {
              id: 'demo-user-123',
              email: '<EMAIL>',
              name: 'John Doe',
              firstName: 'John',
              lastName: 'Doe',
              provider: 'google',
              accessToken: 'demo-token',
            };
            setCurrentUser(mockUser);
            Alert.alert('Demo Success', `Signed in as ${mockUser.name}`);
          },
        },
      ]
    );
  };

  // Demo: Sharing Service
  const demoSharing = async () => {
    try {
      const isAvailable = await SharingService.isAvailable();
      if (!isAvailable) {
        Alert.alert('Sharing Not Available', 'Sharing is not available on this device.');
        return;
      }

      // Create a sample achievement to share
      const sampleAchievement: AlumniAchievement = {
        id: 'achievement-1',
        title: 'Promoted to Senior Software Engineer',
        description: 'Excited to share that I\'ve been promoted to Senior Software Engineer at my company! Grateful for the foundation PROTEC provided.',
        date: new Date(),
        category: 'promotion',
        alumniName: currentUser?.name || 'PROTEC Alumni',
        graduationYear: '2020',
      };

      Alert.alert(
        'Sharing Demo',
        'Choose what to share:',
        [
          { text: 'Cancel' },
          {
            text: 'Share Achievement',
            onPress: async () => {
              const success = await SharingService.shareAchievement(sampleAchievement);
              if (success) {
                Alert.alert('Success', 'Achievement shared successfully!');
              }
            },
          },
          {
            text: 'Share App Invitation',
            onPress: async () => {
              const success = await SharingService.shareAppInvitation(
                currentUser?.name || 'A PROTEC Alumni'
              );
              if (success) {
                Alert.alert('Success', 'App invitation shared successfully!');
              }
            },
          },
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to share content');
    }
  };

  // Demo: Combined Features
  const demoCombinedFeatures = async () => {
    Alert.alert(
      'Combined Features Demo',
      'Here\'s how these plugins work together:\n\n1. 📍 Get your location\n2. 📅 Find nearby events\n3. 👥 Check which contacts are attending\n4. 📱 Share event with friends\n5. 🔐 Sign in to RSVP',
      [
        { text: 'Cancel' },
        {
          text: 'Try It',
          onPress: async () => {
            // This would be a complex workflow combining all services
            Alert.alert(
              'Workflow Complete',
              'In a real app, this would:\n• Find events near you\n• Show which alumni contacts are attending\n• Let you RSVP and share\n• Add events to your calendar'
            );
          },
        },
      ]
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <Text style={styles.title}>PROTEC Alumni Plugin Demos</Text>
        <Text style={styles.subtitle}>
          Explore the powerful features available in your alumni app
        </Text>

        {currentUser && (
          <View style={styles.userInfo}>
            <Text style={styles.userText}>👋 Signed in as {currentUser.name}</Text>
          </View>
        )}

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>📱 Core Features</Text>
          
          <TouchableOpacity style={styles.demoButton} onPress={demoContacts}>
            <Text style={styles.buttonTitle}>👥 Alumni Contacts</Text>
            <Text style={styles.buttonDescription}>
              Find and connect with fellow PROTEC graduates in your contacts
            </Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.demoButton} onPress={demoLocation}>
            <Text style={styles.buttonTitle}>📍 Location Services</Text>
            <Text style={styles.buttonDescription}>
              Discover nearby alumni events and meetups
            </Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.demoButton} onPress={demoCalendar}>
            <Text style={styles.buttonTitle}>📅 Calendar Integration</Text>
            <Text style={styles.buttonDescription}>
              Save alumni events and set reminders
            </Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.demoButton} onPress={demoAuth}>
            <Text style={styles.buttonTitle}>🔐 Social Authentication</Text>
            <Text style={styles.buttonDescription}>
              Sign in with Google, LinkedIn, or Microsoft
            </Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.demoButton} onPress={demoSharing}>
            <Text style={styles.buttonTitle}>📤 Content Sharing</Text>
            <Text style={styles.buttonDescription}>
              Share achievements, events, and connect with alumni
            </Text>
          </TouchableOpacity>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🚀 Advanced Features</Text>
          
          <TouchableOpacity style={styles.demoButton} onPress={demoCombinedFeatures}>
            <Text style={styles.buttonTitle}>🔗 Combined Workflow</Text>
            <Text style={styles.buttonDescription}>
              See how all features work together seamlessly
            </Text>
          </TouchableOpacity>
        </View>

        <View style={styles.footer}>
          <Text style={styles.footerText}>
            These demos show the power of Expo SDK 53 plugins working together to create 
            a comprehensive alumni networking experience.
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollContent: {
    padding: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#012A5B',
    textAlign: 'center',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 24,
  },
  userInfo: {
    backgroundColor: '#E8F4FD',
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
    borderLeftWidth: 4,
    borderLeftColor: '#012A5B',
  },
  userText: {
    fontSize: 16,
    color: '#012A5B',
    fontWeight: '600',
  },
  section: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#012A5B',
    marginBottom: 15,
  },
  demoButton: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 12,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    borderLeftWidth: 4,
    borderLeftColor: '#012A5B',
  },
  buttonTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  buttonDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  footer: {
    backgroundColor: '#E8F4FD',
    padding: 20,
    borderRadius: 8,
    marginTop: 20,
  },
  footerText: {
    fontSize: 14,
    color: '#012A5B',
    textAlign: 'center',
    lineHeight: 22,
  },
});
