import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  StyleSheet,
  Alert,
  TextInput,
  ActivityIndicator,
} from 'react-native';
import { ContactsService, AlumniContact } from '../../services';

interface ContactsExampleProps {
  onContactSelect?: (contact: AlumniContact) => void;
}

export default function ContactsExample({ onContactSelect }: ContactsExampleProps = {}) {
  const [contacts, setContacts] = useState<AlumniContact[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [hasPermission, setHasPermission] = useState(false);

  useEffect(() => {
    checkPermission();
  }, []);

  const checkPermission = async () => {
    const permission = await ContactsService.hasPermission();
    setHasPermission(permission);
  };

  const requestPermission = async () => {
    const granted = await ContactsService.requestPermission();
    setHasPermission(granted);
    if (granted) {
      loadContacts();
    }
  };

  const loadContacts = async () => {
    setLoading(true);
    try {
      const allContacts = await ContactsService.getAllContacts();
      setContacts(allContacts);
    } catch (error) {
      console.error('Error loading contacts:', error);
      Alert.alert('Error', 'Failed to load contacts');
    } finally {
      setLoading(false);
    }
  };

  const searchContacts = async () => {
    if (!searchQuery.trim()) {
      loadContacts();
      return;
    }

    setLoading(true);
    try {
      const searchResults = await ContactsService.searchContacts(searchQuery);
      setContacts(searchResults);
    } catch (error) {
      console.error('Error searching contacts:', error);
      Alert.alert('Error', 'Failed to search contacts');
    } finally {
      setLoading(false);
    }
  };

  const findPotentialAlumni = async () => {
    setLoading(true);
    try {
      // Example alumni domains and companies
      const alumniDomains = ['protec.edu', 'alumni.protec.edu'];
      const alumniCompanies = ['PROTEC', 'Boeing', 'Microsoft', 'Amazon'];
      
      const potentialAlumni = await ContactsService.findPotentialAlumni(
        alumniDomains,
        alumniCompanies
      );
      setContacts(potentialAlumni);
      
      Alert.alert(
        'Potential Alumni Found',
        `Found ${potentialAlumni.length} potential PROTEC alumni in your contacts!`
      );
    } catch (error) {
      console.error('Error finding potential alumni:', error);
      Alert.alert('Error', 'Failed to find potential alumni');
    } finally {
      setLoading(false);
    }
  };

  const addSampleContact = async () => {
    const sampleContact: Partial<AlumniContact> = {
      firstName: 'John',
      lastName: 'Doe',
      emails: ['<EMAIL>'],
      phoneNumbers: ['(*************'],
      company: 'PROTEC Alumni Network',
      jobTitle: 'Software Engineer',
      graduationYear: '2020',
    };

    const success = await ContactsService.addContact(sampleContact);
    if (success) {
      Alert.alert('Success', 'Sample contact added successfully!');
      loadContacts();
    }
  };

  const renderContact = ({ item }: { item: AlumniContact }) => (
    <View style={styles.contactItem}>
      <Text style={styles.contactName}>{item.name}</Text>
      {item.company && (
        <Text style={styles.contactCompany}>{item.company}</Text>
      )}
      {item.emails && item.emails.length > 0 && (
        <Text style={styles.contactEmail}>{item.emails[0]}</Text>
      )}
      {item.phoneNumbers && item.phoneNumbers.length > 0 && (
        <Text style={styles.contactPhone}>{item.phoneNumbers[0]}</Text>
      )}
      {item.isAlumni && (
        <View style={styles.alumniTag}>
          <Text style={styles.alumniTagText}>PROTEC Alumni</Text>
        </View>
      )}
    </View>
  );

  if (!hasPermission) {
    return (
      <View style={styles.container}>
        <Text style={styles.title}>Contacts Access Required</Text>
        <Text style={styles.description}>
          This feature requires access to your contacts to help you connect with fellow PROTEC alumni.
        </Text>
        <TouchableOpacity style={styles.button} onPress={requestPermission}>
          <Text style={styles.buttonText}>Grant Permission</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title}>PROTEC Alumni Contacts</Text>
      
      <View style={styles.searchContainer}>
        <TextInput
          style={styles.searchInput}
          placeholder="Search contacts..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          onSubmitEditing={searchContacts}
        />
        <TouchableOpacity style={styles.searchButton} onPress={searchContacts}>
          <Text style={styles.buttonText}>Search</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.buttonRow}>
        <TouchableOpacity style={styles.smallButton} onPress={loadContacts}>
          <Text style={styles.buttonText}>Load All</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.smallButton} onPress={findPotentialAlumni}>
          <Text style={styles.buttonText}>Find Alumni</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.smallButton} onPress={addSampleContact}>
          <Text style={styles.buttonText}>Add Sample</Text>
        </TouchableOpacity>
      </View>

      {loading ? (
        <ActivityIndicator size="large" color="#012A5B" style={styles.loader} />
      ) : (
        <FlatList
          data={contacts}
          renderItem={renderContact}
          keyExtractor={(item) => item.id}
          style={styles.contactsList}
          showsVerticalScrollIndicator={false}
        />
      )}

      <Text style={styles.footer}>
        {contacts.length} contact{contacts.length !== 1 ? 's' : ''} found
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#012A5B',
    marginBottom: 20,
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 24,
  },
  searchContainer: {
    flexDirection: 'row',
    marginBottom: 15,
  },
  searchInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    backgroundColor: 'white',
    marginRight: 10,
  },
  searchButton: {
    backgroundColor: '#012A5B',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    justifyContent: 'center',
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15,
  },
  button: {
    backgroundColor: '#012A5B',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
  },
  smallButton: {
    backgroundColor: '#012A5B',
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderRadius: 8,
    flex: 1,
    marginHorizontal: 5,
    alignItems: 'center',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  loader: {
    marginTop: 50,
  },
  contactsList: {
    flex: 1,
  },
  contactItem: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  contactName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  contactCompany: {
    fontSize: 14,
    color: '#666',
    marginBottom: 3,
  },
  contactEmail: {
    fontSize: 14,
    color: '#007AFF',
    marginBottom: 3,
  },
  contactPhone: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  alumniTag: {
    backgroundColor: '#012A5B',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    alignSelf: 'flex-start',
  },
  alumniTagText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  footer: {
    textAlign: 'center',
    color: '#666',
    marginTop: 10,
    fontSize: 14,
  },
});
