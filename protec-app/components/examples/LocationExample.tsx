import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  ScrollView,
} from 'react-native';
import { LocationService, LocationCoordinates, LocationAlumniEvent } from '../../services';

export default function LocationExample() {
  const [currentLocation, setCurrentLocation] = useState<LocationCoordinates | null>(null);
  const [address, setAddress] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [hasPermission, setHasPermission] = useState(false);
  const [nearbyEvents, setNearbyEvents] = useState<LocationAlumniEvent[]>([]);
  const [isWatching, setIsWatching] = useState(false);

  // Sample alumni events for demonstration
  const sampleEvents: LocationAlumniEvent[] = [
    {
      id: '1',
      title: 'PROTEC Alumni Networking Mixer',
      description: 'Join fellow PROTEC graduates for an evening of networking and reconnection.',
      location: { latitude: 47.6062, longitude: -122.3321 }, // Seattle
      address: 'Seattle Center, Seattle, WA',
      date: new Date('2024-08-15T18:00:00'),
      attendees: 45,
    },
    {
      id: '2',
      title: 'Tech Career Workshop',
      description: 'Learn about the latest trends in technology careers from industry experts.',
      location: { latitude: 47.6205, longitude: -122.3493 }, // Belltown, Seattle
      address: 'Belltown Community Center, Seattle, WA',
      date: new Date('2024-08-20T14:00:00'),
      attendees: 32,
    },
    {
      id: '3',
      title: 'PROTEC Alumni Golf Tournament',
      description: 'Annual golf tournament for PROTEC alumni and their families.',
      location: { latitude: 47.5480, longitude: -122.3986 }, // West Seattle
      address: 'West Seattle Golf Course, Seattle, WA',
      date: new Date('2024-08-25T09:00:00'),
      attendees: 28,
    },
  ];

  useEffect(() => {
    checkPermission();
  }, []);

  const checkPermission = async () => {
    const permission = await LocationService.hasPermission();
    setHasPermission(permission);
  };

  const requestPermission = async () => {
    const granted = await LocationService.requestPermission();
    setHasPermission(granted);
    if (granted) {
      getCurrentLocation();
    }
  };

  const getCurrentLocation = async () => {
    setLoading(true);
    try {
      const location = await LocationService.getCurrentLocation();
      if (location) {
        setCurrentLocation(location);
        const locationAddress = await LocationService.getAddressFromCoordinates(location);
        setAddress(locationAddress);
        
        // Find nearby events
        const nearby = await LocationService.findNearbyEvents(location, sampleEvents, 50);
        setNearbyEvents(nearby);
      }
    } catch (error) {
      console.error('Error getting location:', error);
      Alert.alert('Error', 'Failed to get current location');
    } finally {
      setLoading(false);
    }
  };

  const startWatchingLocation = async () => {
    try {
      const subscription = await LocationService.watchLocation(
        (location) => {
          setCurrentLocation(location);
          console.log('Location updated:', location);
        },
        {
          timeInterval: 5000, // Update every 5 seconds
          distanceInterval: 10, // Update every 10 meters
        }
      );

      if (subscription) {
        setIsWatching(true);
        Alert.alert('Success', 'Started watching location updates');
        
        // Stop watching after 30 seconds for demo purposes
        setTimeout(() => {
          subscription.remove();
          setIsWatching(false);
          Alert.alert('Info', 'Stopped watching location updates');
        }, 30000);
      }
    } catch (error) {
      console.error('Error watching location:', error);
      Alert.alert('Error', 'Failed to start watching location');
    }
  };

  const calculateDistanceToEvent = (event: LocationAlumniEvent) => {
    if (!currentLocation) return null;
    return LocationService.calculateDistance(currentLocation, event.location);
  };

  const getAddressFromCoordinates = async () => {
    if (!currentLocation) return;
    
    setLoading(true);
    try {
      const locationAddress = await LocationService.getAddressFromCoordinates(currentLocation);
      setAddress(locationAddress);
      Alert.alert('Address Found', locationAddress);
    } catch (error) {
      console.error('Error getting address:', error);
      Alert.alert('Error', 'Failed to get address');
    } finally {
      setLoading(false);
    }
  };

  const checkLocationServices = async () => {
    const enabled = await LocationService.isLocationEnabled();
    Alert.alert(
      'Location Services',
      enabled ? 'Location services are enabled' : 'Location services are disabled',
      enabled ? [{ text: 'OK' }] : [
        { text: 'Cancel' },
        { text: 'Open Settings', onPress: () => LocationService.openLocationSettings() }
      ]
    );
  };

  if (!hasPermission) {
    return (
      <View style={styles.container}>
        <Text style={styles.title}>Location Access Required</Text>
        <Text style={styles.description}>
          This feature requires access to your location to find nearby alumni events and meetups.
        </Text>
        <TouchableOpacity style={styles.button} onPress={requestPermission}>
          <Text style={styles.buttonText}>Grant Permission</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>PROTEC Alumni Location Services</Text>
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Current Location</Text>
        {currentLocation ? (
          <View style={styles.locationInfo}>
            <Text style={styles.coordinates}>
              📍 {currentLocation.latitude.toFixed(6)}, {currentLocation.longitude.toFixed(6)}
            </Text>
            {address && <Text style={styles.address}>📍 {address}</Text>}
            {currentLocation.accuracy && (
              <Text style={styles.accuracy}>
                Accuracy: ±{Math.round(currentLocation.accuracy)}m
              </Text>
            )}
          </View>
        ) : (
          <Text style={styles.noLocation}>No location data available</Text>
        )}
      </View>

      <View style={styles.buttonRow}>
        <TouchableOpacity 
          style={[styles.smallButton, loading && styles.disabledButton]} 
          onPress={getCurrentLocation}
          disabled={loading}
        >
          <Text style={styles.buttonText}>Get Location</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.smallButton, isWatching && styles.disabledButton]} 
          onPress={startWatchingLocation}
          disabled={isWatching}
        >
          <Text style={styles.buttonText}>
            {isWatching ? 'Watching...' : 'Watch Location'}
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.buttonRow}>
        <TouchableOpacity style={styles.smallButton} onPress={getAddressFromCoordinates}>
          <Text style={styles.buttonText}>Get Address</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.smallButton} onPress={checkLocationServices}>
          <Text style={styles.buttonText}>Check Services</Text>
        </TouchableOpacity>
      </View>

      {loading && <ActivityIndicator size="large" color="#012A5B" style={styles.loader} />}

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Nearby Alumni Events</Text>
        {nearbyEvents.length > 0 ? (
          nearbyEvents.map((event) => {
            const distance = calculateDistanceToEvent(event);
            return (
              <View key={event.id} style={styles.eventItem}>
                <Text style={styles.eventTitle}>{event.title}</Text>
                <Text style={styles.eventDate}>
                  📅 {event.date.toLocaleDateString()} at {event.date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </Text>
                <Text style={styles.eventLocation}>📍 {event.address}</Text>
                {distance && (
                  <Text style={styles.eventDistance}>
                    🚗 {distance.toFixed(1)} km away
                  </Text>
                )}
                <Text style={styles.eventAttendees}>
                  👥 {event.attendees} alumni attending
                </Text>
                <Text style={styles.eventDescription}>{event.description}</Text>
              </View>
            );
          })
        ) : (
          <Text style={styles.noEvents}>
            {currentLocation 
              ? 'No nearby events found. Get your location to see events in your area.'
              : 'Get your location to see nearby alumni events.'
            }
          </Text>
        )}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#012A5B',
    marginBottom: 20,
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 24,
  },
  section: {
    marginBottom: 25,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#012A5B',
    marginBottom: 10,
  },
  locationInfo: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  coordinates: {
    fontSize: 14,
    color: '#333',
    marginBottom: 5,
    fontFamily: 'monospace',
  },
  address: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  accuracy: {
    fontSize: 12,
    color: '#999',
  },
  noLocation: {
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
    textAlign: 'center',
    padding: 20,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15,
  },
  button: {
    backgroundColor: '#012A5B',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
  },
  smallButton: {
    backgroundColor: '#012A5B',
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderRadius: 8,
    flex: 1,
    marginHorizontal: 5,
    alignItems: 'center',
  },
  disabledButton: {
    backgroundColor: '#ccc',
  },
  buttonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  loader: {
    marginVertical: 20,
  },
  eventItem: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  eventTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  eventDate: {
    fontSize: 14,
    color: '#666',
    marginBottom: 3,
  },
  eventLocation: {
    fontSize: 14,
    color: '#666',
    marginBottom: 3,
  },
  eventDistance: {
    fontSize: 14,
    color: '#007AFF',
    marginBottom: 3,
    fontWeight: '600',
  },
  eventAttendees: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  eventDescription: {
    fontSize: 14,
    color: '#333',
    lineHeight: 20,
  },
  noEvents: {
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
    textAlign: 'center',
    padding: 20,
  },
});
