import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  TouchableOpacity,
  Alert,
  Share,
  TextInput,
} from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { useTogglePostLike, useAddComment, useDeletePost } from '@/lib/hooks/usePosts';
import { useAuth } from '@/lib/providers/AuthProvider';
import { Post } from '@/lib/types';
import { DesignSystem } from '@/constants/DesignSystem';
import { Ionicons } from '@expo/vector-icons';
import { useThemeColor } from '@/hooks/useThemeColor';
import { router } from 'expo-router';

interface PostCardProps {
  post: Post;
  onPress?: () => void;
  showActions?: boolean;
}

export const PostCard: React.FC<PostCardProps> = ({
  post,
  onPress,
  showActions = true,
}) => {
  const [showCommentInput, setShowCommentInput] = useState(false);
  const [commentText, setCommentText] = useState('');
  const [isSubmittingComment, setIsSubmittingComment] = useState(false);

  const { user } = useAuth();
  const primaryColor = useThemeColor({}, 'primary');
  const textColor = useThemeColor({}, 'text');
  const surfaceColor = useThemeColor({}, 'surface');

  const toggleLikeMutation = useTogglePostLike();
  const addCommentMutation = useAddComment();
  const deletePostMutation = useDeletePost();

  const isLiked = post.likes?.includes(user?.id || '');
  const isMyPost = post.authorId === user?.id;

  const handleLike = async () => {
    try {
      await toggleLikeMutation.mutateAsync(post.id);
    } catch (error) {
      console.error('Error toggling like:', error);
      Alert.alert('Error', 'Failed to update like. Please try again.');
    }
  };

  const handleComment = () => {
    setShowCommentInput(!showCommentInput);
  };

  const handleSubmitComment = async () => {
    if (!commentText.trim()) return;

    setIsSubmittingComment(true);
    try {
      await addCommentMutation.mutateAsync({
        postId: post.id,
        text: commentText.trim(),
      });
      setCommentText('');
      setShowCommentInput(false);
    } catch (error) {
      console.error('Error adding comment:', error);
      Alert.alert('Error', 'Failed to add comment. Please try again.');
    } finally {
      setIsSubmittingComment(false);
    }
  };

  const handleShare = async () => {
    try {
      const shareContent = `${post.content}\n\n- ${post.author.name} (PROTEC Alumni)`;
      await Share.share({
        message: shareContent,
        title: 'PROTEC Alumni Post',
      });
    } catch (error) {
      console.error('Error sharing post:', error);
    }
  };

  const handleDelete = () => {
    Alert.alert(
      'Delete Post',
      'Are you sure you want to delete this post? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await deletePostMutation.mutateAsync(post.id);
            } catch (error) {
              console.error('Error deleting post:', error);
              Alert.alert('Error', 'Failed to delete post. Please try again.');
            }
          },
        },
      ]
    );
  };

  const handleAuthorPress = () => {
    router.push(`/profile/${post.authorId}`);
  };

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;
    
    return date.toLocaleDateString();
  };

  return (
    <Card style={styles.container}>
      <TouchableOpacity onPress={onPress} activeOpacity={0.7}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={handleAuthorPress} style={styles.authorInfo}>
            <View style={styles.avatar}>
              <ThemedText style={styles.avatarText}>
                {post.author.name.charAt(0).toUpperCase()}
              </ThemedText>
            </View>
            <View style={styles.authorDetails}>
              <ThemedText style={styles.authorName}>{post.author.name}</ThemedText>
              <ThemedText style={styles.authorMeta}>
                {post.author.currentRole && `${post.author.currentRole} • `}
                Class of {post.author.graduationYear} • {formatTimeAgo(post.createdAt)}
              </ThemedText>
            </View>
          </TouchableOpacity>

          {isMyPost && (
            <TouchableOpacity onPress={handleDelete} style={styles.deleteButton}>
              <Ionicons name="trash-outline" size={20} color={DesignSystem.Colors.error} />
            </TouchableOpacity>
          )}
        </View>

        {/* Content */}
        <View style={styles.content}>
          <ThemedText style={styles.postText}>{post.content}</ThemedText>
          
          {/* Tags */}
          {post.tags && post.tags.length > 0 && (
            <View style={styles.tagsContainer}>
              {post.tags.map((tag, index) => (
                <View key={index} style={styles.tag}>
                  <ThemedText style={styles.tagText}>#{tag}</ThemedText>
                </View>
              ))}
            </View>
          )}
        </View>

        {/* Actions */}
        {showActions && (
          <View style={styles.actions}>
            <TouchableOpacity
              onPress={handleLike}
              style={[styles.actionButton, isLiked && styles.likedButton]}
              disabled={toggleLikeMutation.isLoading}
            >
              <Ionicons
                name={isLiked ? 'heart' : 'heart-outline'}
                size={20}
                color={isLiked ? DesignSystem.Colors.error : textColor}
              />
              <ThemedText
                style={[
                  styles.actionText,
                  isLiked && { color: DesignSystem.Colors.error }
                ]}
              >
                {post.likes?.length || 0}
              </ThemedText>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={handleComment}
              style={styles.actionButton}
            >
              <Ionicons name="chatbubble-outline" size={20} color={textColor} />
              <ThemedText style={styles.actionText}>
                {post._count?.comments || 0}
              </ThemedText>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={handleShare}
              style={styles.actionButton}
            >
              <Ionicons name="share-outline" size={20} color={textColor} />
              <ThemedText style={styles.actionText}>Share</ThemedText>
            </TouchableOpacity>
          </View>
        )}

        {/* Recent Comments */}
        {post.comments && post.comments.length > 0 && (
          <View style={styles.commentsPreview}>
            {post.comments.slice(0, 2).map((comment) => (
              <View key={comment.id} style={styles.comment}>
                <ThemedText style={styles.commentAuthor}>
                  {comment.author.name}
                </ThemedText>
                <ThemedText style={styles.commentText}>{comment.text}</ThemedText>
              </View>
            ))}
            {(post._count?.comments || 0) > 2 && (
              <TouchableOpacity onPress={onPress}>
                <ThemedText style={styles.viewMoreComments}>
                  View all {post._count?.comments} comments
                </ThemedText>
              </TouchableOpacity>
            )}
          </View>
        )}

        {/* Comment Input */}
        {showCommentInput && (
          <View style={styles.commentInput}>
            <TextInput
              style={styles.commentTextInput}
              value={commentText}
              onChangeText={setCommentText}
              placeholder="Write a comment..."
              multiline
              maxLength={500}
            />
            <View style={styles.commentActions}>
              <Button
                title="Cancel"
                onPress={() => {
                  setShowCommentInput(false);
                  setCommentText('');
                }}
                variant="ghost"
                size="sm"
              />
              <Button
                title="Post"
                onPress={handleSubmitComment}
                disabled={!commentText.trim() || isSubmittingComment}
                loading={isSubmittingComment}
                size="sm"
              />
            </View>
          </View>
        )}
      </TouchableOpacity>
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: DesignSystem.Spacing.md,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: DesignSystem.Spacing.md,
  },
  authorInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: DesignSystem.Colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: DesignSystem.Spacing.sm,
  },
  avatarText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  authorDetails: {
    flex: 1,
  },
  authorName: {
    fontSize: DesignSystem.Typography.body.fontSize,
    fontWeight: '600',
    marginBottom: 2,
  },
  authorMeta: {
    fontSize: DesignSystem.Typography.caption.fontSize,
    color: DesignSystem.Colors.textSecondary,
  },
  deleteButton: {
    padding: DesignSystem.Spacing.xs,
  },
  content: {
    marginBottom: DesignSystem.Spacing.md,
  },
  postText: {
    fontSize: DesignSystem.Typography.body.fontSize,
    lineHeight: 22,
    marginBottom: DesignSystem.Spacing.sm,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: DesignSystem.Spacing.xs,
  },
  tag: {
    backgroundColor: DesignSystem.Colors.primary + '20',
    paddingHorizontal: DesignSystem.Spacing.sm,
    paddingVertical: DesignSystem.Spacing.xs,
    borderRadius: DesignSystem.BorderRadius.sm,
  },
  tagText: {
    fontSize: DesignSystem.Typography.caption.fontSize,
    color: DesignSystem.Colors.primary,
    fontWeight: '500',
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: DesignSystem.Spacing.md,
    borderTopWidth: 1,
    borderTopColor: DesignSystem.Colors.border,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: DesignSystem.Spacing.xs,
    paddingVertical: DesignSystem.Spacing.sm,
    paddingHorizontal: DesignSystem.Spacing.md,
    borderRadius: DesignSystem.BorderRadius.md,
  },
  likedButton: {
    backgroundColor: DesignSystem.Colors.error + '10',
  },
  actionText: {
    fontSize: DesignSystem.Typography.caption.fontSize,
    fontWeight: '500',
  },
  commentsPreview: {
    marginTop: DesignSystem.Spacing.sm,
    paddingTop: DesignSystem.Spacing.sm,
    borderTopWidth: 1,
    borderTopColor: DesignSystem.Colors.border,
  },
  comment: {
    marginBottom: DesignSystem.Spacing.xs,
  },
  commentAuthor: {
    fontSize: DesignSystem.Typography.caption.fontSize,
    fontWeight: '600',
    marginBottom: 2,
  },
  commentText: {
    fontSize: DesignSystem.Typography.caption.fontSize,
    lineHeight: 18,
  },
  viewMoreComments: {
    fontSize: DesignSystem.Typography.caption.fontSize,
    color: DesignSystem.Colors.primary,
    fontWeight: '500',
    marginTop: DesignSystem.Spacing.xs,
  },
  commentInput: {
    marginTop: DesignSystem.Spacing.md,
    paddingTop: DesignSystem.Spacing.md,
    borderTopWidth: 1,
    borderTopColor: DesignSystem.Colors.border,
  },
  commentTextInput: {
    borderWidth: 1,
    borderColor: DesignSystem.Colors.border,
    borderRadius: DesignSystem.BorderRadius.md,
    padding: DesignSystem.Spacing.md,
    fontSize: DesignSystem.Typography.body.fontSize,
    minHeight: 80,
    textAlignVertical: 'top',
    marginBottom: DesignSystem.Spacing.sm,
  },
  commentActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: DesignSystem.Spacing.sm,
  },
});
