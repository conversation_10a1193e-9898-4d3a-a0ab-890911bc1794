/**
 * Animated Components
 * 
 * Pre-built animated components with smooth micro-interactions
 */

import React, { useEffect } from 'react';
import { View, ViewProps } from 'react-native';
import Animated from 'react-native-reanimated';
import {
  useFadeInAnimation,
  useSlideAnimation,
  useScaleAnimation,
  useEntranceAnimation,
  useShakeAnimation,
} from '@/lib/utils/animations';

interface AnimatedViewProps extends ViewProps {
  children: React.ReactNode;
  animation?: 'fadeIn' | 'slideUp' | 'slideDown' | 'slideLeft' | 'slideRight' | 'scaleIn';
  delay?: number;
  duration?: number;
  distance?: number;
  autoStart?: boolean;
}

/**
 * Animated View with entrance animations
 */
export const AnimatedView: React.FC<AnimatedViewProps> = ({
  children,
  animation = 'fadeIn',
  delay = 0,
  duration = 300,
  distance = 50,
  autoStart = true,
  style,
  ...props
}) => {
  const fadeAnimation = useFadeInAnimation(delay, duration);
  const slideAnimation = useSlideAnimation(
    animation.includes('slide') ? animation.replace('slide', '').toLowerCase() as any : 'up',
    distance,
    delay
  );
  const scaleAnimation = useScaleAnimation(0, 1, delay);

  useEffect(() => {
    if (autoStart) {
      switch (animation) {
        case 'fadeIn':
          fadeAnimation.fadeIn();
          break;
        case 'slideUp':
        case 'slideDown':
        case 'slideLeft':
        case 'slideRight':
          slideAnimation.slideIn();
          break;
        case 'scaleIn':
          scaleAnimation.scaleIn();
          break;
      }
    }
  }, [autoStart, animation]);

  const getAnimatedStyle = () => {
    switch (animation) {
      case 'fadeIn':
        return fadeAnimation.animatedStyle;
      case 'slideUp':
      case 'slideDown':
      case 'slideLeft':
      case 'slideRight':
        return slideAnimation.animatedStyle;
      case 'scaleIn':
        return scaleAnimation.animatedStyle;
      default:
        return fadeAnimation.animatedStyle;
    }
  };

  return (
    <Animated.View style={[getAnimatedStyle(), style]} {...props}>
      {children}
    </Animated.View>
  );
};

interface StaggeredListProps {
  children: React.ReactNode[];
  staggerDelay?: number;
  autoStart?: boolean;
}

/**
 * Staggered list animation
 */
export const StaggeredList: React.FC<StaggeredListProps> = ({
  children,
  staggerDelay = 100,
  autoStart = true,
}) => {
  const { animations, startAnimation } = useEntranceAnimation(children.length, staggerDelay);

  useEffect(() => {
    if (autoStart) {
      startAnimation();
    }
  }, [autoStart]);

  return (
    <>
      {children.map((child, index) => (
        <Animated.View key={index} style={animations[index].animatedStyle}>
          {child}
        </Animated.View>
      ))}
    </>
  );
};

interface ShakeViewProps extends ViewProps {
  children: React.ReactNode;
  trigger?: boolean;
  intensity?: number;
}

/**
 * Shake animation component
 */
export const ShakeView: React.FC<ShakeViewProps> = ({
  children,
  trigger = false,
  intensity = 10,
  style,
  ...props
}) => {
  const { animatedStyle, shake } = useShakeAnimation(intensity);

  useEffect(() => {
    if (trigger) {
      shake();
    }
  }, [trigger]);

  return (
    <Animated.View style={[animatedStyle, style]} {...props}>
      {children}
    </Animated.View>
  );
};

interface FadeTransitionProps {
  children: React.ReactNode;
  visible: boolean;
  duration?: number;
}

/**
 * Fade transition component
 */
export const FadeTransition: React.FC<FadeTransitionProps> = ({
  children,
  visible,
  duration = 300,
}) => {
  const { animatedStyle, fadeIn, fadeOut } = useFadeInAnimation(0, duration);

  useEffect(() => {
    if (visible) {
      fadeIn();
    } else {
      fadeOut();
    }
  }, [visible]);

  return (
    <Animated.View style={animatedStyle}>
      {children}
    </Animated.View>
  );
};

interface SlideTransitionProps {
  children: React.ReactNode;
  visible: boolean;
  direction?: 'up' | 'down' | 'left' | 'right';
  distance?: number;
}

/**
 * Slide transition component
 */
export const SlideTransition: React.FC<SlideTransitionProps> = ({
  children,
  visible,
  direction = 'up',
  distance = 50,
}) => {
  const { animatedStyle, slideIn, slideOut } = useSlideAnimation(direction, distance);

  useEffect(() => {
    if (visible) {
      slideIn();
    } else {
      slideOut();
    }
  }, [visible]);

  return (
    <Animated.View style={animatedStyle}>
      {children}
    </Animated.View>
  );
};

interface ScaleTransitionProps {
  children: React.ReactNode;
  visible: boolean;
  fromScale?: number;
  toScale?: number;
}

/**
 * Scale transition component
 */
export const ScaleTransition: React.FC<ScaleTransitionProps> = ({
  children,
  visible,
  fromScale = 0,
  toScale = 1,
}) => {
  const { animatedStyle, scaleIn, scaleOut } = useScaleAnimation(fromScale, toScale);

  useEffect(() => {
    if (visible) {
      scaleIn();
    } else {
      scaleOut();
    }
  }, [visible]);

  return (
    <Animated.View style={animatedStyle}>
      {children}
    </Animated.View>
  );
};

interface AnimatedContainerProps extends ViewProps {
  children: React.ReactNode;
  entrance?: 'stagger' | 'sequence' | 'simultaneous';
  staggerDelay?: number;
  autoStart?: boolean;
}

/**
 * Animated container with multiple entrance options
 */
export const AnimatedContainer: React.FC<AnimatedContainerProps> = ({
  children,
  entrance = 'stagger',
  staggerDelay = 100,
  autoStart = true,
  style,
  ...props
}) => {
  const childrenArray = React.Children.toArray(children);

  if (entrance === 'stagger') {
    return (
      <View style={style} {...props}>
        <StaggeredList staggerDelay={staggerDelay} autoStart={autoStart}>
          {childrenArray}
        </StaggeredList>
      </View>
    );
  }

  return (
    <AnimatedView
      animation="fadeIn"
      autoStart={autoStart}
      style={style}
      {...props}
    >
      {children}
    </AnimatedView>
  );
};

// Export all components
export {
  AnimatedView,
  StaggeredList,
  ShakeView,
  FadeTransition,
  SlideTransition,
  ScaleTransition,
  AnimatedContainer,
};
