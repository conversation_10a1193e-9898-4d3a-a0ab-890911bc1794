import React, { useEffect, useState } from 'react';
import { View, StyleSheet, Dimensions, Image } from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
  withSequence,
  withDelay,
  withSpring,
  interpolate,
  runOnJS,
  Easing,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import * as SplashScreen from 'expo-splash-screen';
import { ThemedText } from '@/components/ThemedText';
import { DesignSystem } from '@/constants/DesignSystem';

const { width, height } = Dimensions.get('window');

interface SplashScreenProps {
  onAnimationComplete: () => void;
}

export function AnimatedSplashScreen({ onAnimationComplete }: SplashScreenProps) {
  const [isReady, setIsReady] = useState(false);
  
  // Animation values
  const logoScale = useSharedValue(0);
  const logoOpacity = useSharedValue(0);
  const logoRotation = useSharedValue(0);
  const titleOpacity = useSharedValue(0);
  const titleTranslateY = useSharedValue(50);
  const subtitleOpacity = useSharedValue(0);
  const subtitleTranslateY = useSharedValue(30);
  const backgroundOpacity = useSharedValue(0);
  const progressWidth = useSharedValue(0);
  const containerScale = useSharedValue(1);

  useEffect(() => {
    const prepare = async () => {
      try {
        // Keep the splash screen visible while we prepare the app
        await SplashScreen.preventAutoHideAsync();
        
        // Simulate app initialization
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        setIsReady(true);
      } catch (e) {
        console.warn(e);
        setIsReady(true);
      }
    };

    prepare();
  }, []);

  useEffect(() => {
    if (isReady) {
      startAnimation();
    }
  }, [isReady]);

  const startAnimation = () => {
    // Hide the native splash screen
    SplashScreen.hideAsync();

    // Start the custom animation sequence
    backgroundOpacity.value = withTiming(1, { duration: 500 });

    // Logo animation - scale and fade in with bounce
    logoScale.value = withDelay(
      300,
      withSpring(1, {
        damping: 15,
        stiffness: 200,
      })
    );
    
    logoOpacity.value = withDelay(
      300,
      withTiming(1, { duration: 800, easing: Easing.out(Easing.ease) })
    );

    // Subtle logo rotation
    logoRotation.value = withDelay(
      300,
      withSequence(
        withTiming(5, { duration: 400 }),
        withTiming(-2, { duration: 300 }),
        withTiming(0, { duration: 200 })
      )
    );

    // Title animation
    titleOpacity.value = withDelay(
      800,
      withTiming(1, { duration: 600, easing: Easing.out(Easing.ease) })
    );
    
    titleTranslateY.value = withDelay(
      800,
      withSpring(0, DesignSystem.Animations.spring.gentle)
    );

    // Subtitle animation
    subtitleOpacity.value = withDelay(
      1200,
      withTiming(1, { duration: 600, easing: Easing.out(Easing.ease) })
    );
    
    subtitleTranslateY.value = withDelay(
      1200,
      withSpring(0, DesignSystem.Animations.spring.gentle)
    );

    // Progress bar animation
    progressWidth.value = withDelay(
      1600,
      withTiming(100, { 
        duration: 1500, 
        easing: Easing.out(Easing.ease) 
      })
    );

    // Final exit animation
    containerScale.value = withDelay(
      3500,
      withTiming(1.1, { 
        duration: 300,
        easing: Easing.in(Easing.ease) 
      }, () => {
        runOnJS(onAnimationComplete)();
      })
    );

    backgroundOpacity.value = withDelay(
      3500,
      withTiming(0, { duration: 300 })
    );
  };

  // Animated styles
  const backgroundStyle = useAnimatedStyle(() => ({
    opacity: backgroundOpacity.value,
  }));

  const logoStyle = useAnimatedStyle(() => ({
    transform: [
      { scale: logoScale.value },
      { rotate: `${logoRotation.value}deg` }
    ],
    opacity: logoOpacity.value,
  }));

  const titleStyle = useAnimatedStyle(() => ({
    opacity: titleOpacity.value,
    transform: [{ translateY: titleTranslateY.value }],
  }));

  const subtitleStyle = useAnimatedStyle(() => ({
    opacity: subtitleOpacity.value,
    transform: [{ translateY: subtitleTranslateY.value }],
  }));

  const progressStyle = useAnimatedStyle(() => ({
    width: `${progressWidth.value}%`,
  }));

  const containerStyle = useAnimatedStyle(() => ({
    transform: [{ scale: containerScale.value }],
  }));

  if (!isReady) {
    return null;
  }

  return (
    <Animated.View style={[styles.container, containerStyle]}>
      <Animated.View style={[StyleSheet.absoluteFill, backgroundStyle]}>
        <LinearGradient
          colors={[
            DesignSystem.Colors.light.primary,
            DesignSystem.Colors.light.primaryDark,
            '#001122'
          ]}
          style={StyleSheet.absoluteFill}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        />
      </Animated.View>

      <View style={styles.content}>
        {/* Logo */}
        <Animated.View style={[styles.logoContainer, logoStyle]}>
          <View style={styles.logoBackground}>
            <Image
              source={require('@/assets/images/icon.png')}
              style={styles.logo}
              resizeMode="contain"
            />
          </View>
        </Animated.View>

        {/* Title */}
        <Animated.View style={titleStyle}>
          <ThemedText 
            type="title" 
            style={styles.title}
            lightColor="#FFFFFF"
            darkColor="#FFFFFF"
          >
            PROTEC Alumni
          </ThemedText>
        </Animated.View>

        {/* Subtitle */}
        <Animated.View style={subtitleStyle}>
          <ThemedText 
            style={styles.subtitle}
            lightColor="rgba(255, 255, 255, 0.8)"
            darkColor="rgba(255, 255, 255, 0.8)"
          >
            Connecting Excellence
          </ThemedText>
        </Animated.View>

        {/* Progress Bar */}
        <View style={styles.progressContainer}>
          <View style={styles.progressTrack}>
            <Animated.View style={[styles.progressBar, progressStyle]} />
          </View>
        </View>
      </View>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: DesignSystem.Colors.light.primary,
  },
  
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: DesignSystem.Spacing['3xl'],
  },
  
  logoContainer: {
    marginBottom: DesignSystem.Spacing['4xl'],
  },
  
  logoBackground: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
    ...DesignSystem.Shadows.lg,
  },
  
  logo: {
    width: 80,
    height: 80,
  },
  
  title: {
    fontSize: 32,
    fontWeight: '800',
    textAlign: 'center',
    marginBottom: DesignSystem.Spacing.md,
    letterSpacing: 1,
  },
  
  subtitle: {
    fontSize: 18,
    textAlign: 'center',
    marginBottom: DesignSystem.Spacing['5xl'],
    fontWeight: '300',
    letterSpacing: 0.5,
  },
  
  progressContainer: {
    position: 'absolute',
    bottom: DesignSystem.Spacing['6xl'],
    left: DesignSystem.Spacing['3xl'],
    right: DesignSystem.Spacing['3xl'],
  },
  
  progressTrack: {
    height: 3,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 2,
    overflow: 'hidden',
  },
  
  progressBar: {
    height: '100%',
    backgroundColor: '#FFFFFF',
    borderRadius: 2,
  },
});
