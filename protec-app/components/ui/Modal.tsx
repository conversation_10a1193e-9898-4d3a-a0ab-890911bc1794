import React from 'react';
import {
  Modal as RNModal,
  View,
  StyleSheet,
  TouchableOpacity,
  TouchableWithoutFeedback,
  Dimensions,
  ViewStyle,
} from 'react-native';
import { ThemedText } from '@/components/ThemedText';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface ModalProps {
  visible: boolean;
  onClose: () => void;
  children: React.ReactNode;
  title?: string;
  size?: 'sm' | 'md' | 'lg' | 'full';
  animationType?: 'none' | 'slide' | 'fade';
  transparent?: boolean;
  closeOnBackdrop?: boolean;
  style?: ViewStyle;
}

export function Modal({
  visible,
  onClose,
  children,
  title,
  size = 'md',
  animationType = 'fade',
  transparent = true,
  closeOnBackdrop = true,
  style,
}: ModalProps) {
  const modalContentStyles = [
    styles.modalContent,
    styles[size],
    style,
  ];

  const handleBackdropPress = () => {
    if (closeOnBackdrop) {
      onClose();
    }
  };

  return (
    <RNModal
      visible={visible}
      animationType={animationType}
      transparent={transparent}
      onRequestClose={onClose}
    >
      <TouchableWithoutFeedback onPress={handleBackdropPress}>
        <View style={styles.overlay}>
          <TouchableWithoutFeedback>
            <View style={modalContentStyles}>
              {title && (
                <View style={styles.header}>
                  <ThemedText type="subtitle">{title}</ThemedText>
                  <TouchableOpacity
                    style={styles.closeButton}
                    onPress={onClose}
                  >
                    <ThemedText style={styles.closeButtonText}>×</ThemedText>
                  </TouchableOpacity>
                </View>
              )}
              
              <View style={styles.body}>
                {children}
              </View>
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </RNModal>
  );
}

interface ModalHeaderProps {
  children: React.ReactNode;
  style?: ViewStyle;
}

export function ModalHeader({ children, style }: ModalHeaderProps) {
  return <View style={[styles.header, style]}>{children}</View>;
}

interface ModalBodyProps {
  children: React.ReactNode;
  style?: ViewStyle;
}

export function ModalBody({ children, style }: ModalBodyProps) {
  return <View style={[styles.body, style]}>{children}</View>;
}

interface ModalFooterProps {
  children: React.ReactNode;
  style?: ViewStyle;
}

export function ModalFooter({ children, style }: ModalFooterProps) {
  return <View style={[styles.footer, style]}>{children}</View>;
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 12,
    maxHeight: screenHeight * 0.9,
  },
  
  // Sizes
  sm: {
    width: Math.min(screenWidth * 0.8, 400),
  },
  
  md: {
    width: Math.min(screenWidth * 0.9, 500),
  },
  
  lg: {
    width: Math.min(screenWidth * 0.95, 600),
  },
  
  full: {
    width: screenWidth * 0.95,
    height: screenHeight * 0.9,
  },
  
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  
  closeButton: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  
  closeButtonText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#666',
  },
  
  body: {
    padding: 20,
    flex: 1,
  },
  
  footer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
    gap: 12,
  },
});
