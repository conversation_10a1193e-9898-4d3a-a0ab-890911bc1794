import React, { useState } from 'react';
import {
  TextInput,
  View,
  Text,
  StyleSheet,
  TextInputProps,
  ViewStyle,
  TextStyle,
  TouchableOpacity,
} from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
  interpolateColor,
} from 'react-native-reanimated';
import { useThemeColor } from '@/hooks/useThemeColor';
import { DesignSystem } from '@/constants/DesignSystem';
import { getAccessibilityProps } from '@/lib/utils/accessibility';
import { getResponsiveLayout, getPlatformStyles } from '@/lib/utils/responsive';

const AnimatedView = Animated.createAnimatedComponent(View);

interface InputProps extends TextInputProps {
  label?: string;
  error?: string;
  helperText?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  containerStyle?: ViewStyle;
  labelStyle?: TextStyle;
  errorStyle?: TextStyle;
  variant?: 'default' | 'outlined' | 'filled';
  size?: 'sm' | 'md' | 'lg';
}

export function Input({
  label,
  error,
  helperText,
  leftIcon,
  rightIcon,
  containerStyle,
  labelStyle,
  errorStyle,
  variant = 'outlined',
  size = 'md',
  style,
  onFocus,
  onBlur,
  ...props
}: InputProps) {
  const [isFocused, setIsFocused] = useState(false);

  // Responsive and platform utilities
  const responsiveLayout = getResponsiveLayout();
  const platformStyles = getPlatformStyles();

  // Animation values
  const focusAnimation = useSharedValue(0);

  // Theme colors
  const backgroundColor = useThemeColor(
    { light: DesignSystem.Colors.light.background, dark: DesignSystem.Colors.dark.background },
    'background'
  );
  const borderColor = useThemeColor(
    { light: DesignSystem.Colors.light.border, dark: DesignSystem.Colors.dark.border },
    'border'
  );
  const focusedBorderColor = useThemeColor(
    { light: DesignSystem.Colors.light.primary, dark: DesignSystem.Colors.dark.primary },
    'primary'
  );
  const errorBorderColor = useThemeColor(
    { light: DesignSystem.Colors.light.error, dark: DesignSystem.Colors.dark.error },
    'error'
  );
  const textColor = useThemeColor(
    { light: DesignSystem.Colors.light.text, dark: DesignSystem.Colors.dark.text },
    'text'
  );
  const placeholderColor = useThemeColor(
    { light: DesignSystem.Colors.light.textMuted, dark: DesignSystem.Colors.dark.textMuted },
    'textMuted'
  );

  // Animated border color
  const animatedBorderStyle = useAnimatedStyle(() => {
    const currentBorderColor = error
      ? errorBorderColor
      : interpolateColor(
          focusAnimation.value,
          [0, 1],
          [borderColor, focusedBorderColor]
        );

    return {
      borderColor: currentBorderColor,
      borderWidth: error ? 2 : focusAnimation.value === 1 ? 2 : 1,
    };
  });

  const handleFocus = (event: any) => {
    setIsFocused(true);
    focusAnimation.value = withTiming(1, { duration: DesignSystem.Animations.duration.normal });
    onFocus?.(event);
  };

  const handleBlur = (event: any) => {
    setIsFocused(false);
    focusAnimation.value = withTiming(0, { duration: DesignSystem.Animations.duration.normal });
    onBlur?.(event);
  };

  // Accessibility props
  const accessibilityProps = getAccessibilityProps.textInput(
    label || 'Input field',
    props.value,
    error,
    props.required
  );

  const getInputContainerStyle = () => {
    const baseStyle = [styles.inputContainer, styles[variant], styles[size]];

    if (variant === 'filled') {
      baseStyle.push({ backgroundColor });
    }

    return baseStyle;
  };

  return (
    <View style={[styles.container, containerStyle]}>
      {label && (
        <Text style={[
          styles.label,
          { color: error ? errorBorderColor : textColor },
          labelStyle
        ]}>
          {label}
        </Text>
      )}

      <AnimatedView style={[
        getInputContainerStyle(),
        animatedBorderStyle,
        error && styles.inputError
      ]}>
        {leftIcon && <View style={styles.leftIcon}>{leftIcon}</View>}

        <TextInput
          style={[
            styles.input,
            styles[`${size}Input`],
            {
              color: textColor,
              fontSize: platformStyles.input.fontSize,
              minHeight: responsiveLayout.inputHeight,
            },
            style
          ]}
          placeholderTextColor={placeholderColor}
          onFocus={handleFocus}
          onBlur={handleBlur}
          {...accessibilityProps}
          {...props}
        />

        {rightIcon && (
          <TouchableOpacity style={styles.rightIcon}>
            {rightIcon}
          </TouchableOpacity>
        )}
      </AnimatedView>

      {(error || helperText) && (
        <Text style={[
          styles.helperText,
          error && [styles.errorText, { color: errorBorderColor }],
          errorStyle
        ]}>
          {error || helperText}
        </Text>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: DesignSystem.Spacing.lg,
  },

  label: {
    fontSize: DesignSystem.Typography.fontSize.sm,
    fontWeight: DesignSystem.Typography.fontWeight.medium,
    marginBottom: DesignSystem.Spacing.xs,
  },

  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: DesignSystem.Components.input.borderRadius,
  },

  // Variants
  default: {
    borderBottomWidth: 1,
    backgroundColor: 'transparent',
  },

  outlined: {
    borderWidth: 1,
  },

  filled: {
    borderWidth: 0,
  },

  // Sizes
  sm: {
    minHeight: 36,
    paddingHorizontal: DesignSystem.Spacing.md,
  },

  md: {
    minHeight: DesignSystem.Components.input.height,
    paddingHorizontal: DesignSystem.Components.input.padding.horizontal,
  },

  lg: {
    minHeight: 52,
    paddingHorizontal: DesignSystem.Spacing.xl,
  },

  // States
  inputError: {
    // Error styling handled by animation
  },

  input: {
    flex: 1,
    fontSize: DesignSystem.Components.input.fontSize,
    paddingVertical: DesignSystem.Components.input.padding.vertical,
  },

  smInput: {
    fontSize: DesignSystem.Typography.fontSize.sm,
  },

  mdInput: {
    fontSize: DesignSystem.Typography.fontSize.base,
  },

  lgInput: {
    fontSize: DesignSystem.Typography.fontSize.lg,
  },

  leftIcon: {
    marginRight: DesignSystem.Spacing.md,
  },

  rightIcon: {
    marginLeft: DesignSystem.Spacing.md,
  },

  helperText: {
    fontSize: DesignSystem.Typography.fontSize.xs,
    marginTop: DesignSystem.Spacing.xs,
  },

  errorText: {
    // Color handled by theme
  },
});
