import React from 'react';
import { View, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { router } from 'expo-router';
import { ThemedText } from '@/components/ThemedText';
import { Card } from './Card';
import { Alumni } from '@/lib/types';

interface AlumniCardProps {
  alumni: Alumni;
  onPress?: () => void;
  showConnectButton?: boolean;
}

export function AlumniCard({ 
  alumni, 
  onPress,
  showConnectButton = true 
}: AlumniCardProps) {
  const handlePress = () => {
    if (onPress) {
      onPress();
    } else {
      router.push(`/alumni/${alumni.id}`);
    }
  };

  const handleConnect = (e: any) => {
    e.stopPropagation();
    // TODO: Implement connect functionality
    console.log('Connect with', alumni.name);
  };

  return (
    <TouchableOpacity onPress={handlePress}>
      <Card variant="elevated" style={styles.card}>
        <View style={styles.header}>
          <View style={styles.avatarContainer}>
            {alumni.photoUrl ? (
              <Image source={{ uri: alumni.photoUrl }} style={styles.avatar} />
            ) : (
              <View style={styles.avatarPlaceholder}>
                <ThemedText style={styles.avatarText}>
                  {alumni.name.charAt(0).toUpperCase()}
                </ThemedText>
              </View>
            )}
          </View>
          
          <View style={styles.info}>
            <ThemedText type="defaultSemiBold" style={styles.name}>
              {alumni.name}
            </ThemedText>
            
            {alumni.currentRole && (
              <ThemedText style={styles.role}>
                {alumni.currentRole}
                {alumni.company && ` at ${alumni.company}`}
              </ThemedText>
            )}
            
            <View style={styles.details}>
              <ThemedText style={styles.detail}>
                Class of {alumni.graduationYear}
              </ThemedText>
              
              {alumni.city && alumni.province && (
                <ThemedText style={styles.detail}>
                  {alumni.city}, {alumni.province}
                </ThemedText>
              )}
            </View>
          </View>
        </View>

        {alumni.skills && alumni.skills.length > 0 && (
          <View style={styles.skills}>
            {alumni.skills.slice(0, 3).map((skill, index) => (
              <View key={index} style={styles.skillTag}>
                <ThemedText style={styles.skillText}>{skill}</ThemedText>
              </View>
            ))}
            {alumni.skills.length > 3 && (
              <View style={styles.skillTag}>
                <ThemedText style={styles.skillText}>
                  +{alumni.skills.length - 3}
                </ThemedText>
              </View>
            )}
          </View>
        )}

        {showConnectButton && (
          <View style={styles.footer}>
            <TouchableOpacity
              style={styles.connectButton}
              onPress={handleConnect}
            >
              <ThemedText style={styles.connectButtonText}>
                Connect
              </ThemedText>
            </TouchableOpacity>
          </View>
        )}
      </Card>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  card: {
    marginBottom: 12,
  },
  
  header: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  
  avatarContainer: {
    marginRight: 12,
  },
  
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
  },
  
  avatarPlaceholder: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#012A5B',
    justifyContent: 'center',
    alignItems: 'center',
  },
  
  avatarText: {
    color: '#fff',
    fontSize: 24,
    fontWeight: 'bold',
  },
  
  info: {
    flex: 1,
  },
  
  name: {
    fontSize: 18,
    marginBottom: 4,
  },
  
  role: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  
  details: {
    gap: 2,
  },
  
  detail: {
    fontSize: 12,
    color: '#888',
  },
  
  skills: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6,
    marginBottom: 12,
  },
  
  skillTag: {
    backgroundColor: 'rgba(1, 42, 91, 0.1)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  
  skillText: {
    fontSize: 12,
    color: '#012A5B',
    fontWeight: '500',
  },
  
  footer: {
    alignItems: 'flex-end',
  },
  
  connectButton: {
    backgroundColor: '#012A5B',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  
  connectButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
});
