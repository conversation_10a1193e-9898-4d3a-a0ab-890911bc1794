import React from 'react';
import { View, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { router } from 'expo-router';
import { ThemedText } from '@/components/ThemedText';
import { Card } from './Card';
import { Event } from '@/lib/types';
import { format } from 'date-fns';

interface EventCardProps {
  event: Event;
  onPress?: () => void;
  showRSVPButton?: boolean;
}

export function EventCard({ 
  event, 
  onPress,
  showRSVPButton = true 
}: EventCardProps) {
  const handlePress = () => {
    if (onPress) {
      onPress();
    } else {
      router.push(`/events/${event.id}`);
    }
  };

  const handleRSVP = (e: any) => {
    e.stopPropagation();
    // TODO: Implement RSVP functionality
    console.log('RSVP to', event.title);
  };

  const formatEventDate = (date: Date) => {
    return format(date, 'MMM dd, yyyy');
  };

  const formatEventTime = (date: Date) => {
    return format(date, 'h:mm a');
  };

  return (
    <TouchableOpacity onPress={handlePress}>
      <Card variant="elevated" style={styles.card}>
        {event.imageUrl && (
          <Image source={{ uri: event.imageUrl }} style={styles.image} />
        )}
        
        <View style={styles.content}>
          <View style={styles.header}>
            <View style={styles.dateContainer}>
              <ThemedText style={styles.dateText}>
                {formatEventDate(event.startTime)}
              </ThemedText>
              <ThemedText style={styles.timeText}>
                {formatEventTime(event.startTime)}
              </ThemedText>
            </View>
            
            <View style={styles.categoryContainer}>
              <ThemedText style={styles.categoryText}>
                {event.category}
              </ThemedText>
            </View>
          </View>

          <ThemedText type="defaultSemiBold" style={styles.title}>
            {event.title}
          </ThemedText>

          <ThemedText style={styles.description} numberOfLines={2}>
            {event.description}
          </ThemedText>

          <View style={styles.details}>
            <View style={styles.locationContainer}>
              <ThemedText style={styles.locationText}>
                📍 {event.location?.city || 'Location TBD'}
              </ThemedText>
            </View>
            
            <View style={styles.organizerContainer}>
              <ThemedText style={styles.organizerText}>
                By {event.organizer.name}
              </ThemedText>
            </View>
          </View>

          {event.tags && event.tags.length > 0 && (
            <View style={styles.tags}>
              {event.tags.slice(0, 3).map((tag, index) => (
                <View key={index} style={styles.tag}>
                  <ThemedText style={styles.tagText}>#{tag}</ThemedText>
                </View>
              ))}
              {event.tags.length > 3 && (
                <View style={styles.tag}>
                  <ThemedText style={styles.tagText}>
                    +{event.tags.length - 3}
                  </ThemedText>
                </View>
              )}
            </View>
          )}

          {showRSVPButton && (
            <View style={styles.footer}>
              <TouchableOpacity
                style={styles.rsvpButton}
                onPress={handleRSVP}
              >
                <ThemedText style={styles.rsvpButtonText}>
                  RSVP
                </ThemedText>
              </TouchableOpacity>
              
              {event.maxAttendees && (
                <ThemedText style={styles.attendeeCount}>
                  {/* TODO: Get actual RSVP count */}
                  0 / {event.maxAttendees} attending
                </ThemedText>
              )}
            </View>
          )}
        </View>
      </Card>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  card: {
    marginBottom: 12,
    overflow: 'hidden',
  },
  
  image: {
    width: '100%',
    height: 150,
    resizeMode: 'cover',
  },
  
  content: {
    padding: 16,
  },
  
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  
  dateContainer: {
    flex: 1,
  },
  
  dateText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#012A5B',
  },
  
  timeText: {
    fontSize: 12,
    color: '#666',
  },
  
  categoryContainer: {
    backgroundColor: 'rgba(1, 42, 91, 0.1)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  
  categoryText: {
    fontSize: 12,
    color: '#012A5B',
    fontWeight: '500',
  },
  
  title: {
    fontSize: 18,
    marginBottom: 8,
  },
  
  description: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    marginBottom: 12,
  },
  
  details: {
    marginBottom: 12,
  },
  
  locationContainer: {
    marginBottom: 4,
  },
  
  locationText: {
    fontSize: 12,
    color: '#888',
  },
  
  organizerContainer: {},
  
  organizerText: {
    fontSize: 12,
    color: '#888',
  },
  
  tags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6,
    marginBottom: 12,
  },
  
  tag: {
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  
  tagText: {
    fontSize: 12,
    color: '#666',
  },
  
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  
  rsvpButton: {
    backgroundColor: '#012A5B',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  
  rsvpButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  
  attendeeCount: {
    fontSize: 12,
    color: '#888',
  },
});
