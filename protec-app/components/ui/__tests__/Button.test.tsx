import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { Button } from '../Button';

describe('Button Component', () => {
  it('renders correctly with title', () => {
    const mockOnPress = jest.fn();
    const { getByText } = render(<Button title="Test Button" onPress={mockOnPress} />);

    expect(getByText('Test Button')).toBeTruthy();
  });

  it('calls onPress when pressed', () => {
    const mockOnPress = jest.fn();
    const { getByText } = render(
      <Button title="Test Button" onPress={mockOnPress} />
    );
    
    fireEvent.press(getByText('Test Button'));
    expect(mockOnPress).toHaveBeenCalledTimes(1);
  });

  it('is disabled when disabled prop is true', () => {
    const mockOnPress = jest.fn();
    const { getByText } = render(
      <Button title="Test Button" onPress={mockOnPress} disabled />
    );
    
    const button = getByText('Test Button').parent;
    expect(button?.props.accessibilityState?.disabled).toBe(true);
    
    fireEvent.press(getByText('Test Button'));
    expect(mockOnPress).not.toHaveBeenCalled();
  });

  it('renders with different variants', () => {
    const mockOnPress = jest.fn();
    const { getByText: getPrimary } = render(
      <Button title="Primary" variant="primary" onPress={mockOnPress} />
    );
    const { getByText: getOutline } = render(
      <Button title="Outline" variant="outline" onPress={mockOnPress} />
    );
    const { getByText: getGhost } = render(
      <Button title="Ghost" variant="ghost" onPress={mockOnPress} />
    );

    expect(getPrimary('Primary')).toBeTruthy();
    expect(getOutline('Outline')).toBeTruthy();
    expect(getGhost('Ghost')).toBeTruthy();
  });

  it('renders with different sizes', () => {
    const mockOnPress = jest.fn();
    const { getByText: getSmall } = render(
      <Button title="Small" size="sm" onPress={mockOnPress} />
    );
    const { getByText: getMedium } = render(
      <Button title="Medium" size="md" onPress={mockOnPress} />
    );
    const { getByText: getLarge } = render(
      <Button title="Large" size="lg" onPress={mockOnPress} />
    );

    expect(getSmall('Small')).toBeTruthy();
    expect(getMedium('Medium')).toBeTruthy();
    expect(getLarge('Large')).toBeTruthy();
  });

  it('shows loading state', () => {
    const mockOnPress = jest.fn();
    const { getByText, queryByText } = render(
      <Button title="Test Button" loading onPress={mockOnPress} />
    );

    // Should show loading text instead of title
    expect(queryByText('Test Button')).toBeNull();
    expect(getByText('Loading...')).toBeTruthy();
  });

  it('applies custom styles', () => {
    const mockOnPress = jest.fn();
    const customStyle = { backgroundColor: 'red' };
    const { getByText } = render(
      <Button title="Test Button" style={customStyle} onPress={mockOnPress} />
    );

    const button = getByText('Test Button').parent;
    expect(button?.props.style).toContainEqual(
      expect.objectContaining(customStyle)
    );
  });

  it('renders with icon', () => {
    const { getByText } = render(
      <Button title="With Icon" icon="🚀" />
    );
    
    expect(getByText('🚀')).toBeTruthy();
    expect(getByText('With Icon')).toBeTruthy();
  });

  it('handles accessibility props', () => {
    const { getByText } = render(
      <Button 
        title="Accessible Button" 
        accessibilityLabel="Custom accessibility label"
        accessibilityHint="Custom accessibility hint"
      />
    );
    
    const button = getByText('Accessible Button').parent;
    expect(button?.props.accessibilityLabel).toBe('Custom accessibility label');
    expect(button?.props.accessibilityHint).toBe('Custom accessibility hint');
  });

  it('renders full width when specified', () => {
    const { getByText } = render(
      <Button title="Full Width" fullWidth />
    );
    
    const button = getByText('Full Width').parent;
    expect(button?.props.style).toContainEqual(
      expect.objectContaining({ width: '100%' })
    );
  });

  describe('Button Variants', () => {
    it('applies correct styles for primary variant', () => {
      const { getByText } = render(
        <Button title="Primary" variant="primary" />
      );
      
      const button = getByText('Primary').parent;
      expect(button?.props.style).toContainEqual(
        expect.objectContaining({ backgroundColor: '#012A5B' })
      );
    });

    it('applies correct styles for outline variant', () => {
      const { getByText } = render(
        <Button title="Outline" variant="outline" />
      );
      
      const button = getByText('Outline').parent;
      expect(button?.props.style).toContainEqual(
        expect.objectContaining({ 
          borderWidth: 1,
          borderColor: '#012A5B',
          backgroundColor: 'transparent'
        })
      );
    });

    it('applies correct styles for ghost variant', () => {
      const { getByText } = render(
        <Button title="Ghost" variant="ghost" />
      );
      
      const button = getByText('Ghost').parent;
      expect(button?.props.style).toContainEqual(
        expect.objectContaining({ backgroundColor: 'transparent' })
      );
    });
  });

  describe('Button Sizes', () => {
    it('applies correct styles for small size', () => {
      const { getByText } = render(
        <Button title="Small" size="sm" />
      );
      
      const text = getByText('Small');
      expect(text.props.style).toContainEqual(
        expect.objectContaining({ fontSize: 14 })
      );
    });

    it('applies correct styles for medium size', () => {
      const { getByText } = render(
        <Button title="Medium" size="md" />
      );
      
      const text = getByText('Medium');
      expect(text.props.style).toContainEqual(
        expect.objectContaining({ fontSize: 16 })
      );
    });

    it('applies correct styles for large size', () => {
      const { getByText } = render(
        <Button title="Large" size="lg" />
      );
      
      const text = getByText('Large');
      expect(text.props.style).toContainEqual(
        expect.objectContaining({ fontSize: 18 })
      );
    });
  });

  describe('Disabled State', () => {
    it('applies disabled styles', () => {
      const { getByText } = render(
        <Button title="Disabled" disabled />
      );
      
      const button = getByText('Disabled').parent;
      expect(button?.props.style).toContainEqual(
        expect.objectContaining({ opacity: 0.5 })
      );
    });

    it('prevents interaction when disabled', () => {
      const mockOnPress = jest.fn();
      const { getByText } = render(
        <Button title="Disabled" onPress={mockOnPress} disabled />
      );
      
      fireEvent.press(getByText('Disabled'));
      expect(mockOnPress).not.toHaveBeenCalled();
    });
  });

  describe('Loading State', () => {
    it('shows loading indicator', () => {
      const { getByTestId } = render(
        <Button title="Loading" loading />
      );
      
      expect(getByTestId('button-loading-indicator')).toBeTruthy();
    });

    it('prevents interaction when loading', () => {
      const mockOnPress = jest.fn();
      const { getByText } = render(
        <Button title="Loading" onPress={mockOnPress} loading />
      );
      
      fireEvent.press(getByText('Loading...'));
      expect(mockOnPress).not.toHaveBeenCalled();
    });
  });
});
