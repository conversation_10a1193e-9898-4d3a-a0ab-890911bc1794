import React from 'react';
import { View, ActivityIndicator, StyleSheet, ViewStyle } from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
  withSequence,
  Easing,
} from 'react-native-reanimated';
import { ThemedText } from '@/components/ThemedText';
import { useThemeColor } from '@/hooks/useThemeColor';
import { DesignSystem } from '@/constants/DesignSystem';

interface LoadingSpinnerProps {
  size?: 'small' | 'medium' | 'large';
  color?: string;
  text?: string;
  style?: ViewStyle;
  variant?: 'spinner' | 'dots' | 'pulse';
  overlay?: boolean;
}

export function LoadingSpinner({
  size = 'large',
  color,
  text,
  style,
  variant = 'spinner',
  overlay = false,
}: LoadingSpinnerProps) {
  const themeColor = useThemeColor(
    { light: DesignSystem.Colors.light.primary, dark: DesignSystem.Colors.dark.primary },
    'primary'
  );

  const spinnerColor = color || themeColor;

  if (variant === 'spinner') {
    return (
      <View style={[
        styles.container,
        overlay && styles.overlay,
        style
      ]}>
        <ActivityIndicator
          size={size === 'medium' ? 'large' : size}
          color={spinnerColor}
        />
        {text && (
          <ThemedText style={styles.text}>
            {text}
          </ThemedText>
        )}
      </View>
    );
  }

  if (variant === 'dots') {
    return <DotsLoader size={size} color={spinnerColor} text={text} style={style} overlay={overlay} />;
  }

  if (variant === 'pulse') {
    return <PulseLoader size={size} color={spinnerColor} text={text} style={style} overlay={overlay} />;
  }

  return null;
}

function DotsLoader({ size, color, text, style, overlay }: Omit<LoadingSpinnerProps, 'variant'>) {
  const dot1 = useSharedValue(0.3);
  const dot2 = useSharedValue(0.3);
  const dot3 = useSharedValue(0.3);

  React.useEffect(() => {
    const duration = 600;
    const delay = 200;

    dot1.value = withRepeat(
      withSequence(
        withTiming(1, { duration, easing: Easing.inOut(Easing.ease) }),
        withTiming(0.3, { duration, easing: Easing.inOut(Easing.ease) })
      ),
      -1
    );

    setTimeout(() => {
      dot2.value = withRepeat(
        withSequence(
          withTiming(1, { duration, easing: Easing.inOut(Easing.ease) }),
          withTiming(0.3, { duration, easing: Easing.inOut(Easing.ease) })
        ),
        -1
      );
    }, delay);

    setTimeout(() => {
      dot3.value = withRepeat(
        withSequence(
          withTiming(1, { duration, easing: Easing.inOut(Easing.ease) }),
          withTiming(0.3, { duration, easing: Easing.inOut(Easing.ease) })
        ),
        -1
      );
    }, delay * 2);
  }, []);

  const dot1Style = useAnimatedStyle(() => ({
    opacity: dot1.value,
  }));

  const dot2Style = useAnimatedStyle(() => ({
    opacity: dot2.value,
  }));

  const dot3Style = useAnimatedStyle(() => ({
    opacity: dot3.value,
  }));

  const dotSize = size === 'small' ? 8 : size === 'medium' ? 12 : 16;

  return (
    <View style={[
      styles.container,
      overlay && styles.overlay,
      style
    ]}>
      <View style={styles.dotsContainer}>
        <Animated.View style={[
          styles.dot,
          { width: dotSize, height: dotSize, backgroundColor: color },
          dot1Style
        ]} />
        <Animated.View style={[
          styles.dot,
          { width: dotSize, height: dotSize, backgroundColor: color },
          dot2Style
        ]} />
        <Animated.View style={[
          styles.dot,
          { width: dotSize, height: dotSize, backgroundColor: color },
          dot3Style
        ]} />
      </View>
      {text && (
        <ThemedText style={styles.text}>
          {text}
        </ThemedText>
      )}
    </View>
  );
}

function PulseLoader({ size, color, text, style, overlay }: Omit<LoadingSpinnerProps, 'variant'>) {
  const scale = useSharedValue(1);
  const opacity = useSharedValue(1);

  React.useEffect(() => {
    scale.value = withRepeat(
      withSequence(
        withTiming(1.2, { duration: 800, easing: Easing.inOut(Easing.ease) }),
        withTiming(1, { duration: 800, easing: Easing.inOut(Easing.ease) })
      ),
      -1
    );

    opacity.value = withRepeat(
      withSequence(
        withTiming(0.5, { duration: 800, easing: Easing.inOut(Easing.ease) }),
        withTiming(1, { duration: 800, easing: Easing.inOut(Easing.ease) })
      ),
      -1
    );
  }, []);

  const pulseStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value,
  }));

  const pulseSize = size === 'small' ? 24 : size === 'medium' ? 32 : 48;

  return (
    <View style={[
      styles.container,
      overlay && styles.overlay,
      style
    ]}>
      <Animated.View style={[
        styles.pulse,
        {
          width: pulseSize,
          height: pulseSize,
          backgroundColor: color,
        },
        pulseStyle
      ]} />
      {text && (
        <ThemedText style={styles.text}>
          {text}
        </ThemedText>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: DesignSystem.Spacing.xl,
  },

  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    zIndex: DesignSystem.ZIndex.modal,
  },

  text: {
    marginTop: DesignSystem.Spacing.md,
    textAlign: 'center',
  },

  dotsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: DesignSystem.Spacing.sm,
  },

  dot: {
    borderRadius: 50,
  },

  pulse: {
    borderRadius: 50,
  },
});
