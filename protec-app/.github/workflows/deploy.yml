name: Deploy to App Stores

on:
  release:
    types: [published]
  workflow_dispatch:
    inputs:
      platform:
        description: 'Platform to deploy'
        required: true
        default: 'all'
        type: choice
        options:
          - all
          - ios
          - android
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'production'
        type: choice
        options:
          - production
          - staging

env:
  NODE_VERSION: '18'
  EXPO_CLI_VERSION: 'latest'

jobs:
  prepare:
    name: Prepare Deployment
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.version.outputs.version }}
      build-number: ${{ steps.version.outputs.build-number }}
      
    steps:
      - name: 🏗 Setup repo
        uses: actions/checkout@v4

      - name: 📋 Extract version info
        id: version
        run: |
          VERSION=$(node -p "require('./package.json').version")
          BUILD_NUMBER=$(date +%Y%m%d%H%M)
          echo "version=$VERSION" >> $GITHUB_OUTPUT
          echo "build-number=$BUILD_NUMBER" >> $GITHUB_OUTPUT
          echo "Version: $VERSION, Build: $BUILD_NUMBER"

  build-ios:
    name: Build iOS
    runs-on: ubuntu-latest
    needs: prepare
    if: github.event.inputs.platform == 'ios' || github.event.inputs.platform == 'all' || github.event_name == 'release'
    
    steps:
      - name: 🏗 Setup repo
        uses: actions/checkout@v4

      - name: 🏗 Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: npm

      - name: 🏗 Setup Expo and EAS
        uses: expo/expo-github-action@v8
        with:
          expo-version: ${{ env.EXPO_CLI_VERSION }}
          eas-version: latest
          token: ${{ secrets.EXPO_TOKEN }}

      - name: 📦 Install dependencies
        run: npm ci

      - name: 🔧 Configure app version
        run: |
          # Update version in app.json
          node -e "
            const fs = require('fs');
            const app = JSON.parse(fs.readFileSync('app.json', 'utf8'));
            app.expo.version = '${{ needs.prepare.outputs.version }}';
            app.expo.ios.buildNumber = '${{ needs.prepare.outputs.build-number }}';
            fs.writeFileSync('app.json', JSON.stringify(app, null, 2));
          "

      - name: 🚀 Build iOS app
        run: eas build --platform ios --profile production --non-interactive

      - name: 📱 Submit to App Store
        if: github.event_name == 'release'
        run: eas submit --platform ios --profile production --non-interactive

  build-android:
    name: Build Android
    runs-on: ubuntu-latest
    needs: prepare
    if: github.event.inputs.platform == 'android' || github.event.inputs.platform == 'all' || github.event_name == 'release'
    
    steps:
      - name: 🏗 Setup repo
        uses: actions/checkout@v4

      - name: 🏗 Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: npm

      - name: 🏗 Setup Expo and EAS
        uses: expo/expo-github-action@v8
        with:
          expo-version: ${{ env.EXPO_CLI_VERSION }}
          eas-version: latest
          token: ${{ secrets.EXPO_TOKEN }}

      - name: 📦 Install dependencies
        run: npm ci

      - name: 🔧 Configure app version
        run: |
          # Update version in app.json
          node -e "
            const fs = require('fs');
            const app = JSON.parse(fs.readFileSync('app.json', 'utf8'));
            app.expo.version = '${{ needs.prepare.outputs.version }}';
            app.expo.android.versionCode = parseInt('${{ needs.prepare.outputs.build-number }}');
            fs.writeFileSync('app.json', JSON.stringify(app, null, 2));
          "

      - name: 🚀 Build Android app
        run: eas build --platform android --profile production --non-interactive

      - name: 📱 Submit to Google Play
        if: github.event_name == 'release'
        run: eas submit --platform android --profile production --non-interactive

  create-github-release:
    name: Create GitHub Release
    runs-on: ubuntu-latest
    needs: [build-ios, build-android]
    if: github.event_name == 'release'
    
    steps:
      - name: 🏗 Setup repo
        uses: actions/checkout@v4

      - name: 📝 Generate changelog
        id: changelog
        run: |
          # Generate changelog from commits since last release
          LAST_TAG=$(git describe --tags --abbrev=0 HEAD~1 2>/dev/null || echo "")
          if [ -n "$LAST_TAG" ]; then
            CHANGELOG=$(git log $LAST_TAG..HEAD --pretty=format:"- %s" --no-merges)
          else
            CHANGELOG=$(git log --pretty=format:"- %s" --no-merges -10)
          fi
          
          echo "changelog<<EOF" >> $GITHUB_OUTPUT
          echo "$CHANGELOG" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

      - name: 📦 Create Release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: v${{ needs.prepare.outputs.version }}
          release_name: PROTEC Alumni App v${{ needs.prepare.outputs.version }}
          body: |
            ## What's New in v${{ needs.prepare.outputs.version }}
            
            ${{ steps.changelog.outputs.changelog }}
            
            ## Download
            
            - **iOS**: Available on the App Store
            - **Android**: Available on Google Play Store
            
            ## Technical Details
            
            - Build Number: ${{ needs.prepare.outputs.build-number }}
            - Platform: iOS & Android
            - Minimum iOS Version: 13.0
            - Minimum Android Version: API 21 (Android 5.0)
          draft: false
          prerelease: false

  notify-deployment:
    name: Notify Deployment
    runs-on: ubuntu-latest
    needs: [build-ios, build-android]
    if: always()
    
    steps:
      - name: 📢 Notify success
        if: needs.build-ios.result == 'success' && needs.build-android.result == 'success'
        uses: 8398a7/action-slack@v3
        with:
          status: success
          text: |
            🚀 PROTEC Alumni App v${{ needs.prepare.outputs.version }} deployed successfully!
            
            ✅ iOS build completed
            ✅ Android build completed
            📱 Apps submitted to stores
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

      - name: 📢 Notify failure
        if: needs.build-ios.result == 'failure' || needs.build-android.result == 'failure'
        uses: 8398a7/action-slack@v3
        with:
          status: failure
          text: |
            ❌ PROTEC Alumni App deployment failed!
            
            iOS: ${{ needs.build-ios.result }}
            Android: ${{ needs.build-android.result }}
            
            Please check the workflow logs for details.
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

      - name: 📧 Email notification
        if: github.event_name == 'release'
        uses: dawidd6/action-send-mail@v3
        with:
          server_address: smtp.gmail.com
          server_port: 587
          username: ${{ secrets.EMAIL_USERNAME }}
          password: ${{ secrets.EMAIL_PASSWORD }}
          subject: PROTEC Alumni App v${{ needs.prepare.outputs.version }} Released
          to: <EMAIL>
          from: <EMAIL>
          body: |
            The PROTEC Alumni App v${{ needs.prepare.outputs.version }} has been successfully released!
            
            The new version includes the latest features and improvements.
            Both iOS and Android versions have been submitted to their respective app stores.
            
            Build Details:
            - Version: ${{ needs.prepare.outputs.version }}
            - Build Number: ${{ needs.prepare.outputs.build-number }}
            - Release Date: $(date)
            
            The apps should be available for download within 24-48 hours after store review.

  rollback:
    name: Emergency Rollback
    runs-on: ubuntu-latest
    if: failure() && github.event_name == 'release'
    needs: [build-ios, build-android]
    
    steps:
      - name: 🏗 Setup repo
        uses: actions/checkout@v4

      - name: 🚨 Trigger rollback procedure
        run: |
          echo "Deployment failed. Initiating rollback procedure..."
          # Add rollback logic here
          # This could include:
          # - Reverting to previous app store version
          # - Rolling back backend changes
          # - Notifying the team
          
      - name: 📢 Notify rollback
        uses: 8398a7/action-slack@v3
        with:
          status: failure
          text: |
            🚨 EMERGENCY: PROTEC Alumni App deployment failed!
            
            Rollback procedure has been initiated.
            Please check the deployment immediately.
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
