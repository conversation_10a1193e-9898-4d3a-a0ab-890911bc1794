name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

env:
  NODE_VERSION: '18'
  EXPO_CLI_VERSION: 'latest'

jobs:
  test:
    name: Test Suite
    runs-on: ubuntu-latest
    
    steps:
      - name: 🏗 Setup repo
        uses: actions/checkout@v4

      - name: 🏗 Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: npm

      - name: 📦 Install dependencies
        run: npm ci

      - name: 🔍 Type check
        run: npm run type-check

      - name: 🧹 Lint code
        run: npm run lint

      - name: 🧪 Run tests
        run: npm run test:coverage

      - name: 📊 Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella

  build-preview:
    name: Build Preview
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'pull_request'
    
    steps:
      - name: 🏗 Setup repo
        uses: actions/checkout@v4

      - name: 🏗 Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: npm

      - name: 🏗 Setup Expo
        uses: expo/expo-github-action@v8
        with:
          expo-version: ${{ env.EXPO_CLI_VERSION }}
          token: ${{ secrets.EXPO_TOKEN }}

      - name: 📦 Install dependencies
        run: npm ci

      - name: 🚀 Build preview
        run: expo export --platform all

  build-production:
    name: Build Production
    runs-on: ubuntu-latest
    needs: test
    if: github.ref == 'refs/heads/main'
    
    strategy:
      matrix:
        platform: [ios, android]
    
    steps:
      - name: 🏗 Setup repo
        uses: actions/checkout@v4

      - name: 🏗 Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: npm

      - name: 🏗 Setup Expo and EAS
        uses: expo/expo-github-action@v8
        with:
          expo-version: ${{ env.EXPO_CLI_VERSION }}
          eas-version: latest
          token: ${{ secrets.EXPO_TOKEN }}

      - name: 📦 Install dependencies
        run: npm ci

      - name: 🚀 Build app
        run: eas build --platform ${{ matrix.platform }} --non-interactive --profile production

  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [test, build-preview]
    if: github.ref == 'refs/heads/develop'
    
    steps:
      - name: 🏗 Setup repo
        uses: actions/checkout@v4

      - name: 🏗 Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: npm

      - name: 🏗 Setup Expo
        uses: expo/expo-github-action@v8
        with:
          expo-version: ${{ env.EXPO_CLI_VERSION }}
          token: ${{ secrets.EXPO_TOKEN }}

      - name: 📦 Install dependencies
        run: npm ci

      - name: 🚀 Publish to Expo
        run: expo publish --release-channel staging

  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    
    steps:
      - name: 🏗 Setup repo
        uses: actions/checkout@v4

      - name: 🏗 Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: npm

      - name: 📦 Install dependencies
        run: npm ci

      - name: 🔒 Run security audit
        run: npm audit --audit-level moderate

      - name: 🔍 Run CodeQL Analysis
        uses: github/codeql-action/init@v2
        with:
          languages: javascript

      - name: 🔍 Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v2

  performance-test:
    name: Performance Test
    runs-on: ubuntu-latest
    needs: test
    
    steps:
      - name: 🏗 Setup repo
        uses: actions/checkout@v4

      - name: 🏗 Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: npm

      - name: 📦 Install dependencies
        run: npm ci

      - name: 🚀 Build for analysis
        run: expo export --platform all

      - name: 📊 Analyze bundle size
        run: |
          BUNDLE_SIZE=$(du -sh dist/ | cut -f1)
          echo "Bundle size: $BUNDLE_SIZE"
          
          # Check if bundle size is reasonable (adjust threshold as needed)
          BUNDLE_SIZE_MB=$(du -sm dist/ | cut -f1)
          if [ $BUNDLE_SIZE_MB -gt 50 ]; then
            echo "Warning: Bundle size is larger than 50MB"
            exit 1
          fi

  accessibility-test:
    name: Accessibility Test
    runs-on: ubuntu-latest
    needs: test
    
    steps:
      - name: 🏗 Setup repo
        uses: actions/checkout@v4

      - name: 🏗 Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: npm

      - name: 📦 Install dependencies
        run: npm ci

      - name: ♿ Run accessibility tests
        run: npm run test -- --testNamePattern="accessibility"

  notify:
    name: Notify Team
    runs-on: ubuntu-latest
    needs: [test, build-production, security-scan, performance-test]
    if: always() && github.ref == 'refs/heads/main'
    
    steps:
      - name: 📢 Notify on success
        if: needs.test.result == 'success' && needs.build-production.result == 'success'
        uses: 8398a7/action-slack@v3
        with:
          status: success
          text: '✅ PROTEC Alumni App: Production build completed successfully!'
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

      - name: 📢 Notify on failure
        if: needs.test.result == 'failure' || needs.build-production.result == 'failure'
        uses: 8398a7/action-slack@v3
        with:
          status: failure
          text: '❌ PROTEC Alumni App: Build failed! Please check the logs.'
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
