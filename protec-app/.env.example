# PROTEC Alumni App - Environment Configuration
# Copy this file to .env.local and update with your actual values

# API Configuration
EXPO_PUBLIC_API_URL=http://localhost:3000
EXPO_PUBLIC_WEB_URL=http://localhost:3000
EXPO_PUBLIC_API_TIMEOUT=30000

# Authentication
EXPO_PUBLIC_AUTH_PROVIDER=nextauth
EXPO_PUBLIC_OAUTH_REDIRECT_URI=protec-alumni://auth

# Push Notifications
EXPO_PUBLIC_PUSH_NOTIFICATIONS_ENABLED=true

# Development
EXPO_PUBLIC_DEV_MODE=true
EXPO_PUBLIC_LOG_LEVEL=debug

# Features
EXPO_PUBLIC_ENABLE_OFFLINE_MODE=true
EXPO_PUBLIC_ENABLE_ANALYTICS=false

# Email Service
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>

# File Storage
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=af-south-1
AWS_S3_BUCKET=protec-alumni-assets

# Analytics
GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX
MIXPANEL_TOKEN=your-mixpanel-token

# Error Tracking
SENTRY_DSN=your-sentry-dsn
SENTRY_ORG=protec
SENTRY_PROJECT=alumni-app

# Social Media Integration
LINKEDIN_CLIENT_ID=your-linkedin-client-id
LINKEDIN_CLIENT_SECRET=your-linkedin-client-secret

# Feature Flags
ENABLE_DONATIONS=true
ENABLE_MESSAGING=true
ENABLE_EVENTS=true
ENABLE_NOTIFICATIONS=true
ENABLE_ANALYTICS=true

# Rate Limiting
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX_REQUESTS=100

# Security
CORS_ORIGIN=https://protec.org,https://www.protec.org
HELMET_CSP_ENABLED=true
BCRYPT_ROUNDS=12

# Logging
LOG_LEVEL=info
LOG_FORMAT=json

# Development
NODE_ENV=production
PORT=3000
DEBUG=false
