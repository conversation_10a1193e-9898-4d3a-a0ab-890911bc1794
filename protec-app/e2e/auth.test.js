describe('Authentication Flow', () => {
  beforeEach(async () => {
    await device.reloadReactNative();
  });

  describe('Login', () => {
    it('should display login screen on app launch', async () => {
      await expect(element(by.id('login-screen'))).toBeVisible();
      await expect(element(by.text('Welcome Back'))).toBeVisible();
      await expect(element(by.id('email-input'))).toBeVisible();
      await expect(element(by.id('password-input'))).toBeVisible();
      await expect(element(by.id('login-button'))).toBeVisible();
    });

    it('should show validation errors for invalid inputs', async () => {
      // Try to login with empty fields
      await element(by.id('login-button')).tap();
      
      await expect(element(by.text('Email is required'))).toBeVisible();
      await expect(element(by.text('Password is required'))).toBeVisible();
    });

    it('should show error for invalid email format', async () => {
      await element(by.id('email-input')).typeText('invalid-email');
      await element(by.id('login-button')).tap();
      
      await expect(element(by.text('Please enter a valid email address'))).toBeVisible();
    });

    it('should login successfully with valid credentials', async () => {
      await element(by.id('email-input')).typeText('<EMAIL>');
      await element(by.id('password-input')).typeText('password123');
      await element(by.id('login-button')).tap();
      
      // Wait for navigation to dashboard
      await waitFor(element(by.id('dashboard-screen')))
        .toBeVisible()
        .withTimeout(10000);
      
      // Verify we're on the dashboard
      await expect(element(by.text('Welcome to PROTEC Alumni'))).toBeVisible();
    });

    it('should show error for invalid credentials', async () => {
      await element(by.id('email-input')).typeText('<EMAIL>');
      await element(by.id('password-input')).typeText('wrongpassword');
      await element(by.id('login-button')).tap();
      
      await expect(element(by.text('Invalid credentials'))).toBeVisible();
    });

    it('should toggle password visibility', async () => {
      await element(by.id('password-input')).typeText('password123');
      
      // Password should be hidden initially
      await expect(element(by.id('password-input'))).toHaveValue('•••••••••••');
      
      // Toggle to show password
      await element(by.id('password-toggle')).tap();
      await expect(element(by.id('password-input'))).toHaveValue('password123');
      
      // Toggle to hide password again
      await element(by.id('password-toggle')).tap();
      await expect(element(by.id('password-input'))).toHaveValue('•••••••••••');
    });
  });

  describe('Registration', () => {
    beforeEach(async () => {
      // Navigate to registration screen
      await element(by.id('create-account-button')).tap();
      await expect(element(by.id('register-screen'))).toBeVisible();
    });

    it('should display registration form', async () => {
      await expect(element(by.text('Create Account'))).toBeVisible();
      await expect(element(by.id('name-input'))).toBeVisible();
      await expect(element(by.id('email-input'))).toBeVisible();
      await expect(element(by.id('password-input'))).toBeVisible();
      await expect(element(by.id('confirm-password-input'))).toBeVisible();
      await expect(element(by.id('register-button'))).toBeVisible();
    });

    it('should validate required fields', async () => {
      await element(by.id('register-button')).tap();
      
      await expect(element(by.text('Name is required'))).toBeVisible();
      await expect(element(by.text('Email is required'))).toBeVisible();
      await expect(element(by.text('Password is required'))).toBeVisible();
    });

    it('should validate password confirmation', async () => {
      await element(by.id('name-input')).typeText('Test User');
      await element(by.id('email-input')).typeText('<EMAIL>');
      await element(by.id('password-input')).typeText('password123');
      await element(by.id('confirm-password-input')).typeText('differentpassword');
      await element(by.id('register-button')).tap();
      
      await expect(element(by.text('Passwords do not match'))).toBeVisible();
    });

    it('should register successfully with valid data', async () => {
      await element(by.id('name-input')).typeText('New User');
      await element(by.id('email-input')).typeText('<EMAIL>');
      await element(by.id('password-input')).typeText('password123');
      await element(by.id('confirm-password-input')).typeText('password123');
      await element(by.id('graduation-year-input')).typeText('2020');
      await element(by.id('register-button')).tap();
      
      // Wait for navigation to dashboard
      await waitFor(element(by.id('dashboard-screen')))
        .toBeVisible()
        .withTimeout(10000);
    });
  });

  describe('Logout', () => {
    beforeEach(async () => {
      // Login first
      await testHelpers.loginUser();
    });

    it('should logout successfully', async () => {
      // Navigate to profile tab
      await element(by.id('profile-tab')).tap();
      await expect(element(by.id('profile-screen'))).toBeVisible();
      
      // Tap logout button
      await element(by.id('logout-button')).tap();
      
      // Confirm logout
      await element(by.text('Logout')).tap();
      
      // Should navigate back to login screen
      await waitFor(element(by.id('login-screen')))
        .toBeVisible()
        .withTimeout(5000);
    });
  });

  describe('Forgot Password', () => {
    beforeEach(async () => {
      // Navigate to forgot password screen
      await element(by.id('forgot-password-link')).tap();
      await expect(element(by.id('forgot-password-screen'))).toBeVisible();
    });

    it('should display forgot password form', async () => {
      await expect(element(by.text('Reset Password'))).toBeVisible();
      await expect(element(by.id('email-input'))).toBeVisible();
      await expect(element(by.id('reset-button'))).toBeVisible();
    });

    it('should validate email field', async () => {
      await element(by.id('reset-button')).tap();
      await expect(element(by.text('Email is required'))).toBeVisible();
      
      await element(by.id('email-input')).typeText('invalid-email');
      await element(by.id('reset-button')).tap();
      await expect(element(by.text('Please enter a valid email address'))).toBeVisible();
    });

    it('should send reset email successfully', async () => {
      await element(by.id('email-input')).typeText('<EMAIL>');
      await element(by.id('reset-button')).tap();
      
      await expect(element(by.text('Reset email sent successfully'))).toBeVisible();
    });
  });

  describe('Session Management', () => {
    it('should persist login session across app restarts', async () => {
      // Login
      await testHelpers.loginUser();
      
      // Restart app
      await device.terminateApp();
      await device.launchApp();
      
      // Should still be logged in
      await waitFor(element(by.id('dashboard-screen')))
        .toBeVisible()
        .withTimeout(10000);
    });

    it('should handle expired session', async () => {
      // Login
      await testHelpers.loginUser();
      
      // Simulate expired token (this would need backend support)
      // For now, we'll test the logout flow
      await testHelpers.logoutUser();
      
      // Should be redirected to login
      await expect(element(by.id('login-screen'))).toBeVisible();
    });
  });

  describe('Network Connectivity', () => {
    it('should handle offline login attempt', async () => {
      // Disable network
      await testHelpers.disableNetwork();
      
      await element(by.id('email-input')).typeText('<EMAIL>');
      await element(by.id('password-input')).typeText('password123');
      await element(by.id('login-button')).tap();
      
      // Should show network error
      await expect(element(by.text('Network error. Please check your connection.'))).toBeVisible();
      
      // Re-enable network
      await testHelpers.enableNetwork();
    });
  });

  describe('Accessibility', () => {
    it('should have proper accessibility labels', async () => {
      await expect(element(by.id('email-input'))).toHaveAccessibilityLabel('Email address');
      await expect(element(by.id('password-input'))).toHaveAccessibilityLabel('Password');
      await expect(element(by.id('login-button'))).toHaveAccessibilityLabel('Sign in button');
    });

    it('should support screen reader navigation', async () => {
      // This would require more specific accessibility testing
      // For now, we'll verify elements are accessible
      await expect(element(by.id('email-input'))).toBeVisible();
      await expect(element(by.id('password-input'))).toBeVisible();
      await expect(element(by.id('login-button'))).toBeVisible();
    });
  });
});
