const { device, expect, element, by, waitFor } = require('detox');

// Global test utilities
global.device = device;
global.expect = expect;
global.element = element;
global.by = by;
global.waitFor = waitFor;

// Test helpers
global.testHelpers = {
  // Login helper
  async loginUser(email = '<EMAIL>', password = 'password123') {
    await element(by.id('email-input')).typeText(email);
    await element(by.id('password-input')).typeText(password);
    await element(by.id('login-button')).tap();
    
    // Wait for navigation to dashboard
    await waitFor(element(by.id('dashboard-screen')))
      .toBeVisible()
      .withTimeout(10000);
  },

  // Logout helper
  async logoutUser() {
    await element(by.id('profile-tab')).tap();
    await element(by.id('logout-button')).tap();
    
    // Wait for navigation to login screen
    await waitFor(element(by.id('login-screen')))
      .toBeVisible()
      .withTimeout(5000);
  },

  // Navigation helpers
  async navigateToTab(tabId) {
    await element(by.id(`${tabId}-tab`)).tap();
    await waitFor(element(by.id(`${tabId}-screen`)))
      .toBeVisible()
      .withTimeout(5000);
  },

  // Form helpers
  async fillForm(formData) {
    for (const [fieldId, value] of Object.entries(formData)) {
      await element(by.id(fieldId)).typeText(value);
    }
  },

  // Wait for loading to complete
  async waitForLoadingToComplete() {
    await waitFor(element(by.id('loading-spinner')))
      .not.toBeVisible()
      .withTimeout(10000);
  },

  // Screenshot helper
  async takeScreenshot(name) {
    await device.takeScreenshot(name);
  },

  // Scroll helpers
  async scrollToElement(elementId, direction = 'down') {
    await waitFor(element(by.id(elementId)))
      .toBeVisible()
      .whileElement(by.id('scroll-view'))
      .scroll(200, direction);
  },

  // Alert helpers
  async dismissAlert() {
    try {
      await element(by.text('OK')).tap();
    } catch (error) {
      // Alert might not be present
    }
  },

  // Permission helpers
  async allowPermissions() {
    try {
      await device.launchApp({ permissions: { notifications: 'YES' } });
    } catch (error) {
      // Permissions might already be granted
    }
  },

  // Network helpers
  async enableNetwork() {
    await device.enableSynchronization();
  },

  async disableNetwork() {
    await device.disableSynchronization();
  },

  // Device helpers
  async reloadApp() {
    await device.reloadReactNative();
  },

  async backgroundApp() {
    await device.sendToHome();
  },

  async foregroundApp() {
    await device.launchApp({ newInstance: false });
  },
};

// Global setup
beforeAll(async () => {
  await device.launchApp();
});

beforeEach(async () => {
  await device.reloadReactNative();
});

afterAll(async () => {
  await device.terminateApp();
});
