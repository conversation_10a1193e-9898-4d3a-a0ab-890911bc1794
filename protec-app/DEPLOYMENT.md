# PROTEC Alumni App - Deployment Guide

This guide covers the complete deployment process for the PROTEC Alumni mobile application.

## 📋 Prerequisites

### Development Environment
- Node.js 18+ 
- npm or yarn
- Expo CLI (`npm install -g @expo/cli`)
- EAS CLI (`npm install -g eas-cli`)

### Accounts Required
- Expo Account (for EAS Build)
- Apple Developer Account (for iOS deployment)
- Google Play Console Account (for Android deployment)

### Certificates & Keys
- iOS: Apple Developer certificates and provisioning profiles
- Android: Google Play signing key and service account JSON

## 🧪 Testing & Quality Assurance

### 1. Run Complete Test Suite
```bash
# Run all tests
./scripts/test-all.sh

# Run with E2E tests
./scripts/test-all.sh --e2e

# Skip build check (for faster testing)
./scripts/test-all.sh --skip-build
```

### 2. Manual Testing Checklist
- [ ] Authentication flow (login, register, logout)
- [ ] Alumni directory browsing and search
- [ ] Event creation and RSVP functionality
- [ ] Messaging system (send, receive, conversations)
- [ ] Feed posts (create, like, comment)
- [ ] Donations flow (amount selection, payment)
- [ ] Push notifications (permissions, delivery)
- [ ] Profile management (view, edit, update)
- [ ] Offline functionality (basic caching)
- [ ] Deep linking (navigation from notifications)

### 3. Performance Testing
```bash
# Bundle size analysis
npm run build
du -sh dist/

# Memory usage testing
# Use React Native Flipper or similar tools

# Network performance
# Test with slow 3G simulation
```

### 4. Accessibility Testing
- [ ] Screen reader compatibility
- [ ] Color contrast ratios
- [ ] Touch target sizes (minimum 44px)
- [ ] Keyboard navigation
- [ ] Voice control support

## 🏗️ Build Configuration

### 1. Environment Setup
Create environment-specific configurations:

```bash
# Development
cp .env.example .env.development

# Staging  
cp .env.example .env.staging

# Production
cp .env.example .env.production
```

### 2. Update App Configuration
Update `app.json` with production values:
- App name and description
- Bundle identifiers
- Version numbers
- Permissions
- API endpoints

### 3. Asset Preparation
Ensure all required assets are present:
- App icon (1024x1024 PNG)
- Splash screen images
- Notification icons
- Adaptive icons (Android)

## 📱 iOS Deployment

### 1. Apple Developer Setup
```bash
# Login to Apple Developer account
eas device:create

# Configure iOS credentials
eas credentials:configure -p ios
```

### 2. Build for iOS
```bash
# Development build
eas build --platform ios --profile development

# Production build
eas build --platform ios --profile production
```

### 3. TestFlight Distribution
```bash
# Submit to TestFlight
eas submit --platform ios --profile production
```

### 4. App Store Submission
1. Open App Store Connect
2. Create new app version
3. Upload build from TestFlight
4. Fill in app metadata:
   - Description
   - Keywords
   - Screenshots
   - Privacy policy
5. Submit for review

## 🤖 Android Deployment

### 1. Google Play Setup
```bash
# Configure Android credentials
eas credentials:configure -p android
```

### 2. Build for Android
```bash
# Development build (APK)
eas build --platform android --profile development

# Production build (AAB)
eas build --platform android --profile production
```

### 3. Google Play Console
1. Create new app in Google Play Console
2. Upload AAB file
3. Configure app details:
   - Store listing
   - Content rating
   - Pricing & distribution
   - App content
4. Create release:
   - Internal testing
   - Closed testing
   - Open testing
   - Production

## 🚀 Deployment Pipeline

### 1. Automated CI/CD (GitHub Actions)
```yaml
# .github/workflows/deploy.yml
name: Deploy
on:
  push:
    branches: [main]
    
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm install
      - run: npm test
      
  build:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm install
      - run: eas build --platform all --non-interactive
```

### 2. Release Process
1. **Code Review**: All changes reviewed via pull requests
2. **Testing**: Automated tests pass + manual QA
3. **Staging**: Deploy to staging environment
4. **Production**: Deploy to app stores
5. **Monitoring**: Monitor crash reports and analytics

### 3. Version Management
```bash
# Update version numbers
npm version patch  # 1.0.0 -> 1.0.1
npm version minor  # 1.0.0 -> 1.1.0  
npm version major  # 1.0.0 -> 2.0.0

# Update native build numbers
# iOS: increment buildNumber in app.json
# Android: increment versionCode in app.json
```

## 📊 Monitoring & Analytics

### 1. Crash Reporting
- Sentry integration for error tracking
- Expo crash reports
- Platform-specific crash tools

### 2. Analytics
- User engagement metrics
- Feature usage analytics
- Performance monitoring
- Push notification metrics

### 3. App Store Metrics
- Download numbers
- User ratings and reviews
- Conversion rates
- Revenue tracking (if applicable)

## 🔧 Maintenance

### 1. Regular Updates
- Security patches
- Dependency updates
- OS compatibility updates
- Feature enhancements

### 2. Monitoring Checklist
- [ ] App store ratings and reviews
- [ ] Crash reports and error rates
- [ ] API performance and uptime
- [ ] Push notification delivery rates
- [ ] User engagement metrics

### 3. Support Process
- User feedback collection
- Bug report triage
- Feature request evaluation
- Community management

## 🚨 Rollback Procedures

### 1. Emergency Rollback
```bash
# Revert to previous version
eas build --platform all --profile production --clear-cache

# Submit emergency update
eas submit --platform all --profile production
```

### 2. Hotfix Process
1. Create hotfix branch from production
2. Apply minimal fix
3. Test thoroughly
4. Fast-track through review process
5. Deploy immediately

## 📋 Pre-Launch Checklist

### Technical
- [ ] All tests passing
- [ ] Performance benchmarks met
- [ ] Security audit completed
- [ ] Accessibility compliance verified
- [ ] App store guidelines compliance

### Business
- [ ] Legal review completed
- [ ] Privacy policy updated
- [ ] Terms of service finalized
- [ ] Marketing materials prepared
- [ ] Support documentation ready

### Operations
- [ ] Monitoring systems configured
- [ ] Backup procedures tested
- [ ] Incident response plan ready
- [ ] Team training completed
- [ ] Launch communication plan executed

## 📞 Support Contacts

- **Development Team**: <EMAIL>
- **QA Team**: <EMAIL>  
- **DevOps**: <EMAIL>
- **Product Manager**: <EMAIL>

## 📚 Additional Resources

- [Expo Documentation](https://docs.expo.dev/)
- [EAS Build Documentation](https://docs.expo.dev/build/introduction/)
- [React Native Performance](https://reactnative.dev/docs/performance)
- [App Store Review Guidelines](https://developer.apple.com/app-store/review/guidelines/)
- [Google Play Policy](https://play.google.com/about/developer-content-policy/)

---

**Last Updated**: December 2024
**Version**: 1.0.0
