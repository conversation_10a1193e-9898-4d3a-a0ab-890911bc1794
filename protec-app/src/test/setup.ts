import 'react-native-gesture-handler/jestSetup';

// Mock Expo modules
jest.mock('expo-constants', () => ({
  expoConfig: {
    extra: {
      eas: {
        projectId: 'test-project-id'
      }
    }
  }
}));

jest.mock('expo-notifications', () => ({
  setNotificationHandler: jest.fn(),
  getPermissionsAsync: jest.fn(() => Promise.resolve({ status: 'granted' })),
  requestPermissionsAsync: jest.fn(() => Promise.resolve({ status: 'granted' })),
  getExpoPushTokenAsync: jest.fn(() => Promise.resolve({ data: 'test-token' })),
  setNotificationChannelAsync: jest.fn(),
  addNotificationReceivedListener: jest.fn(),
  addNotificationResponseReceivedListener: jest.fn(),
  removeNotificationSubscription: jest.fn(),
  scheduleNotificationAsync: jest.fn(),
  setBadgeCountAsync: jest.fn(),
  dismissAllNotificationsAsync: jest.fn(),
}));

jest.mock('expo-device', () => ({
  isDevice: true,
}));

jest.mock('expo-router', () => ({
  router: {
    push: jest.fn(),
    back: jest.fn(),
    replace: jest.fn(),
  },
  useLocalSearchParams: jest.fn(() => ({})),
  useRouter: jest.fn(() => ({
    push: jest.fn(),
    back: jest.fn(),
    replace: jest.fn(),
  })),
}));

jest.mock('expo-image-picker', () => ({
  requestMediaLibraryPermissionsAsync: jest.fn(() => 
    Promise.resolve({ status: 'granted' })
  ),
  launchImageLibraryAsync: jest.fn(() => 
    Promise.resolve({ 
      canceled: false, 
      assets: [{ uri: 'test-image-uri' }] 
    })
  ),
  MediaTypeOptions: {
    Images: 'Images',
  },
}));

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(() => Promise.resolve(null)),
  setItem: jest.fn(() => Promise.resolve()),
  removeItem: jest.fn(() => Promise.resolve()),
  clear: jest.fn(() => Promise.resolve()),
}));

// Mock React Query
jest.mock('@tanstack/react-query', () => ({
  useQuery: jest.fn(() => ({
    data: null,
    isLoading: false,
    error: null,
  })),
  useMutation: jest.fn(() => ({
    mutate: jest.fn(),
    mutateAsync: jest.fn(),
    isLoading: false,
    error: null,
  })),
  QueryClient: jest.fn(() => ({
    invalidateQueries: jest.fn(),
  })),
  QueryClientProvider: ({ children }: { children: React.ReactNode }) => children,
}));

// Mock date-fns
jest.mock('date-fns', () => ({
  format: jest.fn((date, formatStr) => {
    if (formatStr === 'PPP') return 'January 1st, 2024';
    if (formatStr === 'p') return '10:00 AM';
    if (formatStr === 'MMM dd, yyyy') return 'Jan 01, 2024';
    if (formatStr === 'HH:mm') return '10:00';
    return '2024-01-01';
  }),
}));

// Mock Zustand stores
jest.mock('@/lib/stores/authStore', () => ({
  useAuthStore: jest.fn(() => ({
    user: {
      id: 'test-user-id',
      name: 'Test User',
      email: '<EMAIL>',
      role: 'ALUMNI',
    },
    isAuthenticated: true,
    login: jest.fn(),
    logout: jest.fn(),
    loadAuthState: jest.fn(),
  })),
}));

jest.mock('@/lib/stores/notificationsStore', () => ({
  useNotificationsStore: jest.fn(() => ({
    preferences: {
      messages: true,
      events: true,
      connections: true,
      donations: false,
      posts: false,
      general: true,
      sound: true,
      vibration: true,
    },
    isInitialized: true,
    pushToken: 'test-push-token',
    permissionStatus: 'granted',
    notifications: [],
    unreadCount: 0,
    initializeNotifications: jest.fn(),
    updatePreferences: jest.fn(),
    requestPermissions: jest.fn(),
    sendTestNotification: jest.fn(),
    markAsRead: jest.fn(),
    markAllAsRead: jest.fn(),
    clearNotifications: jest.fn(),
    addNotification: jest.fn(),
    updateBadgeCount: jest.fn(),
    clearBadge: jest.fn(),
  })),
}));

jest.mock('@/lib/stores/messagesStore', () => ({
  useMessagesStore: jest.fn(() => ({
    conversations: [],
    currentConversation: null,
    isLoadingConversations: false,
    messages: [],
    isLoadingMessages: false,
    fetchConversations: jest.fn(),
    fetchConversation: jest.fn(),
    fetchMessages: jest.fn(),
    sendMessage: jest.fn(),
    createConversation: jest.fn(),
    markAsRead: jest.fn(),
    addMessage: jest.fn(),
    updateConversation: jest.fn(),
  })),
}));

// Global test utilities
global.console = {
  ...console,
  // Suppress console.warn and console.error in tests unless explicitly needed
  warn: jest.fn(),
  error: jest.fn(),
};

// Mock timers
jest.useFakeTimers();

// Setup test environment
beforeEach(() => {
  jest.clearAllMocks();
});
