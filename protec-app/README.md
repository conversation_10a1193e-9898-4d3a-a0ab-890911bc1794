# PROTEC Alumni Mobile App

A comprehensive mobile application for the PROTEC Alumni community, built with React Native and Expo.

## 🚀 Features

### Core Functionality
- **Authentication**: Secure login/register with JWT tokens
- **Alumni Directory**: Browse and search alumni profiles
- **Events Management**: View, create, and RSVP to events
- **Messaging System**: Real-time chat between alumni
- **News Feed**: Share posts, photos, and updates
- **Donations**: Support PROTEC with integrated payment gateways
- **Push Notifications**: Stay updated with important announcements
- **Profile Management**: Comprehensive profile and career tracking

### Technical Features
- **Offline Support**: Core features work without internet
- **Cross-Platform**: iOS and Android compatibility
- **Real-time Updates**: Live messaging and notifications
- **Secure Storage**: Encrypted local data storage
- **Performance Optimized**: Fast loading and smooth animations

## 🛠️ Technology Stack

- **Framework**: React Native with Expo
- **Language**: TypeScript
- **State Management**: Zustand + React Query
- **Navigation**: Expo Router
- **UI Components**: Custom component library
- **Authentication**: JWT with secure storage
- **Real-time**: WebSocket connections
- **Payments**: PayFast, SnapScan, Ozow integration
- **Push Notifications**: Expo Notifications
- **Testing**: Jest + React Native Testing Library + Detox

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn
- Expo CLI
- iOS Simulator (for iOS development)
- Android Studio (for Android development)

### Installation

1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your configuration
   ```

3. **Start the development server**
   ```bash
   npm start
   ```

4. **Run on device/simulator**
   ```bash
   # iOS
   npm run ios

   # Android
   npm run android
   ```

## 🧪 Testing

### Run All Tests
```bash
# Complete test suite
./scripts/test-all.sh

# With E2E tests
./scripts/test-all.sh --e2e
```

### Individual Test Types
```bash
# Unit tests
npm test

# Integration tests
npm run test:integration

# E2E tests
npm run test:e2e

# Type checking
npm run type-check

# Linting
npm run lint
```

## 🏗️ Building & Deployment

### Development Build
```bash
./scripts/build.sh -p all -e development
```

### Production Build
```bash
./scripts/build.sh -p all -e production
```

See [DEPLOYMENT.md](DEPLOYMENT.md) for detailed deployment instructions.

## 📚 Learn More

- [Expo documentation](https://docs.expo.dev/)
- [React Native documentation](https://reactnative.dev/)
- [TypeScript documentation](https://www.typescriptlang.org/)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

---

**Made with ❤️ for the PROTEC Alumni Community**
