import * as Crypto from 'expo-crypto';
import { config } from '../lib/config/env';

export interface MagicLinkToken {
  token: string;
  email: string;
  expiresAt: Date;
  used: boolean;
}

export interface MagicLinkRequest {
  email: string;
  redirectUrl?: string;
}

export interface MagicLinkVerification {
  success: boolean;
  user?: any;
  error?: string;
  token?: string;
}

export class MagicLinkService {
  private static readonly TOKEN_EXPIRY_MINUTES = 15; // 15 minutes
  private static readonly BASE_URL = config.WEB_URL || 'https://protec-alumni.vercel.app';

  /**
   * Generate a secure magic link token
   */
  static async generateToken(): Promise<string> {
    try {
      // Generate a cryptographically secure random token
      const randomBytes = await Crypto.getRandomBytesAsync(32);
      const token = Array.from(randomBytes)
        .map(byte => byte.toString(16).padStart(2, '0'))
        .join('');
      
      return token;
    } catch (error) {
      console.error('Error generating token:', error);
      // Fallback to timestamp-based token if crypto fails
      return `${Date.now()}-${Math.random().toString(36).substring(2)}`;
    }
  }

  /**
   * Create a magic link URL
   */
  static createMagicLinkUrl(token: string, email: string, redirectUrl?: string): string {
    const params = new URLSearchParams({
      token,
      email,
    });

    if (redirectUrl) {
      params.append('redirect', redirectUrl);
    }

    // Use deep link scheme for mobile app
    return `protec-alumni://auth/verify?${params.toString()}`;
  }

  /**
   * Send magic link via email
   */
  static async sendMagicLink(request: MagicLinkRequest): Promise<{ success: boolean; error?: string }> {
    try {
      const { email, redirectUrl } = request;

      // Generate secure token
      const token = await this.generateToken();
      const expiresAt = new Date(Date.now() + this.TOKEN_EXPIRY_MINUTES * 60 * 1000);

      // Create magic link URL
      const magicLinkUrl = this.createMagicLinkUrl(token, email, redirectUrl);

      // Send email via web API
      const response = await fetch(`${this.BASE_URL}/api/auth/send-magic-link`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'PROTEC Alumni Mobile App',
        },
        body: JSON.stringify({
          email,
          token,
          magicLinkUrl,
          expiresAt: expiresAt.toISOString(),
          appName: 'PROTEC Alumni Mobile',
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();

        // Handle rate limiting
        if (response.status === 429) {
          return {
            success: false,
            error: errorData.error || 'Too many requests. Please wait before trying again.',
          };
        }

        return {
          success: false,
          error: errorData.error || 'Failed to send magic link',
        };
      }

      const result = await response.json();

      // Store token locally for verification
      await this.storeTokenLocally(token, email, expiresAt);

      return {
        success: true,
        messageId: result.messageId
      };
    } catch (error) {
      console.error('Error sending magic link:', error);
      return {
        success: false,
        error: 'Network error. Please check your connection and try again.',
      };
    }
  }

  /**
   * Verify magic link token
   */
  static async verifyToken(token: string, email: string): Promise<MagicLinkVerification> {
    try {
      // First check locally stored token
      const localToken = await this.getStoredToken(token);

      if (!localToken) {
        return {
          success: false,
          error: 'Invalid or expired magic link',
        };
      }

      if (localToken.email !== email) {
        return {
          success: false,
          error: 'Magic link email mismatch',
        };
      }

      if (localToken.used) {
        return {
          success: false,
          error: 'Magic link has already been used',
        };
      }

      if (new Date() > localToken.expiresAt) {
        await this.removeStoredToken(token);
        return {
          success: false,
          error: 'Magic link has expired',
        };
      }

      // Verify with server
      const response = await fetch(`${this.BASE_URL}/api/auth/verify-magic-link`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'PROTEC Alumni Mobile App',
        },
        body: JSON.stringify({
          token,
          email,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();

        // Clean up local token on server rejection
        await this.removeStoredToken(token);

        return {
          success: false,
          error: errorData.error || 'Verification failed',
        };
      }

      const result = await response.json();

      // Mark token as used locally
      await this.markTokenAsUsed(token);

      // Store authentication data
      const { setAuthToken, setUserData } = await import('../lib/auth/storage');
      await setAuthToken(result.authToken);
      await setUserData(result.user);

      return {
        success: true,
        user: result.user,
        token: result.authToken,
      };
    } catch (error) {
      console.error('Error verifying token:', error);
      return {
        success: false,
        error: 'Network error during verification',
      };
    }
  }

  /**
   * Store token locally for verification
   */
  private static async storeTokenLocally(token: string, email: string, expiresAt: Date): Promise<void> {
    try {
      const AsyncStorage = (await import('@react-native-async-storage/async-storage')).default;
      const tokenData: MagicLinkToken = {
        token,
        email,
        expiresAt,
        used: false,
      };

      await AsyncStorage.setItem(`magic_link_${token}`, JSON.stringify(tokenData));
    } catch (error) {
      console.error('Error storing token locally:', error);
    }
  }

  /**
   * Get stored token
   */
  private static async getStoredToken(token: string): Promise<MagicLinkToken | null> {
    try {
      const AsyncStorage = (await import('@react-native-async-storage/async-storage')).default;
      const tokenData = await AsyncStorage.getItem(`magic_link_${token}`);

      if (!tokenData) {
        return null;
      }

      const parsed = JSON.parse(tokenData);
      return {
        ...parsed,
        expiresAt: new Date(parsed.expiresAt),
      };
    } catch (error) {
      console.error('Error getting stored token:', error);
      return null;
    }
  }

  /**
   * Mark token as used
   */
  private static async markTokenAsUsed(token: string): Promise<void> {
    try {
      const tokenData = await this.getStoredToken(token);
      if (tokenData) {
        tokenData.used = true;
        const AsyncStorage = (await import('@react-native-async-storage/async-storage')).default;
        await AsyncStorage.setItem(`magic_link_${token}`, JSON.stringify(tokenData));
      }
    } catch (error) {
      console.error('Error marking token as used:', error);
    }
  }

  /**
   * Remove stored token
   */
  private static async removeStoredToken(token: string): Promise<void> {
    try {
      const AsyncStorage = (await import('@react-native-async-storage/async-storage')).default;
      await AsyncStorage.removeItem(`magic_link_${token}`);
    } catch (error) {
      console.error('Error removing stored token:', error);
    }
  }

  /**
   * Clean up expired tokens
   */
  static async cleanupExpiredTokens(): Promise<void> {
    try {
      const AsyncStorage = (await import('@react-native-async-storage/async-storage')).default;
      const keys = await AsyncStorage.getAllKeys();
      const magicLinkKeys = keys.filter((key: string) => key.startsWith('magic_link_'));

      for (const key of magicLinkKeys) {
        const tokenData = await AsyncStorage.getItem(key);
        if (tokenData) {
          const parsed = JSON.parse(tokenData);
          if (new Date() > new Date(parsed.expiresAt)) {
            await AsyncStorage.removeItem(key);
          }
        }
      }
    } catch (error) {
      console.error('Error cleaning up expired tokens:', error);
    }
  }
}

export default MagicLinkService;
