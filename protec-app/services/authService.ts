import * as AuthSession from 'expo-auth-session';
import * as WebBrowser from 'expo-web-browser';
import { Alert, Platform } from 'react-native';
import * as SecureStore from 'expo-secure-store';

// Complete the auth session for web
WebBrowser.maybeCompleteAuthSession();

export interface AuthUser {
  id: string;
  email: string;
  name: string;
  firstName?: string;
  lastName?: string;
  profilePicture?: string;
  provider: 'google' | 'linkedin' | 'microsoft';
  accessToken: string;
  refreshToken?: string;
  expiresAt?: Date;
}

export interface AuthConfig {
  clientId: string;
  clientSecret?: string;
  redirectUri: string;
  scopes: string[];
  additionalParameters?: Record<string, string>;
}

export class AuthService {
  private static readonly STORAGE_KEYS = {
    USER: 'auth_user',
    ACCESS_TOKEN: 'auth_access_token',
    REFRESH_TOKEN: 'auth_refresh_token',
  };

  /**
   * Google OAuth Configuration
   */
  private static getGoogleConfig(): AuthConfig {
    return {
      clientId: process.env.EXPO_PUBLIC_GOOGLE_CLIENT_ID || 'your-google-client-id',
      redirectUri: AuthSession.makeRedirectUri({
        scheme: 'protec-alumni',
        path: 'auth/google',
      }),
      scopes: ['openid', 'profile', 'email'],
      additionalParameters: {},
    };
  }

  /**
   * LinkedIn OAuth Configuration
   */
  private static getLinkedInConfig(): AuthConfig {
    return {
      clientId: process.env.EXPO_PUBLIC_LINKEDIN_CLIENT_ID || 'your-linkedin-client-id',
      redirectUri: AuthSession.makeRedirectUri({
        scheme: 'protec-alumni',
        path: 'auth/linkedin',
      }),
      scopes: ['r_liteprofile', 'r_emailaddress'],
      additionalParameters: {},
    };
  }

  /**
   * Microsoft OAuth Configuration
   */
  private static getMicrosoftConfig(): AuthConfig {
    return {
      clientId: process.env.EXPO_PUBLIC_MICROSOFT_CLIENT_ID || 'your-microsoft-client-id',
      redirectUri: AuthSession.makeRedirectUri({
        scheme: 'protec-alumni',
        path: 'auth/microsoft',
      }),
      scopes: ['openid', 'profile', 'email', 'User.Read'],
      additionalParameters: {},
    };
  }

  /**
   * Sign in with Google
   */
  static async signInWithGoogle(): Promise<AuthUser | null> {
    try {
      const config = this.getGoogleConfig();
      
      const request = new AuthSession.AuthRequest({
        clientId: config.clientId,
        scopes: config.scopes,
        redirectUri: config.redirectUri,
        responseType: AuthSession.ResponseType.Code,
        extraParams: {
          access_type: 'offline',
          prompt: 'consent',
          ...config.additionalParameters,
        },
      });

      const result = await request.promptAsync({
        authorizationEndpoint: 'https://accounts.google.com/o/oauth2/v2/auth',
      });

      if (result.type === 'success') {
        const tokenResult = await AuthSession.exchangeCodeAsync(
          {
            clientId: config.clientId,
            code: result.params.code,
            redirectUri: config.redirectUri,
            extraParams: {
              code_verifier: request.codeVerifier || '',
            },
          },
          {
            tokenEndpoint: 'https://oauth2.googleapis.com/token',
          }
        );

        const userInfo = await this.fetchGoogleUserInfo(tokenResult.accessToken);
        
        if (userInfo) {
          const authUser: AuthUser = {
            id: userInfo.sub,
            email: userInfo.email,
            name: userInfo.name,
            firstName: userInfo.given_name,
            lastName: userInfo.family_name,
            profilePicture: userInfo.picture,
            provider: 'google',
            accessToken: tokenResult.accessToken,
            refreshToken: tokenResult.refreshToken,
            expiresAt: tokenResult.expiresIn 
              ? new Date(Date.now() + tokenResult.expiresIn * 1000)
              : undefined,
          };

          await this.storeAuthUser(authUser);
          return authUser;
        }
      }

      return null;
    } catch (error) {
      console.error('Google sign-in error:', error);
      Alert.alert('Sign In Error', 'Failed to sign in with Google. Please try again.');
      return null;
    }
  }

  /**
   * Sign in with LinkedIn
   */
  static async signInWithLinkedIn(): Promise<AuthUser | null> {
    try {
      const config = this.getLinkedInConfig();
      
      const request = new AuthSession.AuthRequest({
        clientId: config.clientId,
        scopes: config.scopes,
        redirectUri: config.redirectUri,
        responseType: AuthSession.ResponseType.Code,
        extraParams: config.additionalParameters,
      });

      const result = await request.promptAsync({
        authorizationEndpoint: 'https://www.linkedin.com/oauth/v2/authorization',
      });

      if (result.type === 'success') {
        const tokenResult = await AuthSession.exchangeCodeAsync(
          {
            clientId: config.clientId,
            code: result.params.code,
            redirectUri: config.redirectUri,
          },
          {
            tokenEndpoint: 'https://www.linkedin.com/oauth/v2/accessToken',
          }
        );

        const userInfo = await this.fetchLinkedInUserInfo(tokenResult.accessToken);
        
        if (userInfo) {
          const authUser: AuthUser = {
            id: userInfo.id,
            email: userInfo.emailAddress,
            name: `${userInfo.firstName} ${userInfo.lastName}`,
            firstName: userInfo.firstName,
            lastName: userInfo.lastName,
            profilePicture: userInfo.profilePicture,
            provider: 'linkedin',
            accessToken: tokenResult.accessToken,
            refreshToken: tokenResult.refreshToken,
            expiresAt: tokenResult.expiresIn 
              ? new Date(Date.now() + tokenResult.expiresIn * 1000)
              : undefined,
          };

          await this.storeAuthUser(authUser);
          return authUser;
        }
      }

      return null;
    } catch (error) {
      console.error('LinkedIn sign-in error:', error);
      Alert.alert('Sign In Error', 'Failed to sign in with LinkedIn. Please try again.');
      return null;
    }
  }

  /**
   * Sign in with Microsoft
   */
  static async signInWithMicrosoft(): Promise<AuthUser | null> {
    try {
      const config = this.getMicrosoftConfig();
      
      const request = new AuthSession.AuthRequest({
        clientId: config.clientId,
        scopes: config.scopes,
        redirectUri: config.redirectUri,
        responseType: AuthSession.ResponseType.Code,
        extraParams: config.additionalParameters,
      });

      const result = await request.promptAsync({
        authorizationEndpoint: 'https://login.microsoftonline.com/common/oauth2/v2.0/authorize',
      });

      if (result.type === 'success') {
        const tokenResult = await AuthSession.exchangeCodeAsync(
          {
            clientId: config.clientId,
            code: result.params.code,
            redirectUri: config.redirectUri,
          },
          {
            tokenEndpoint: 'https://login.microsoftonline.com/common/oauth2/v2.0/token',
          }
        );

        const userInfo = await this.fetchMicrosoftUserInfo(tokenResult.accessToken);
        
        if (userInfo) {
          const authUser: AuthUser = {
            id: userInfo.id,
            email: userInfo.mail || userInfo.userPrincipalName,
            name: userInfo.displayName,
            firstName: userInfo.givenName,
            lastName: userInfo.surname,
            profilePicture: undefined, // Microsoft Graph photo requires separate API call
            provider: 'microsoft',
            accessToken: tokenResult.accessToken,
            refreshToken: tokenResult.refreshToken,
            expiresAt: tokenResult.expiresIn 
              ? new Date(Date.now() + tokenResult.expiresIn * 1000)
              : undefined,
          };

          await this.storeAuthUser(authUser);
          return authUser;
        }
      }

      return null;
    } catch (error) {
      console.error('Microsoft sign-in error:', error);
      Alert.alert('Sign In Error', 'Failed to sign in with Microsoft. Please try again.');
      return null;
    }
  }

  /**
   * Fetch Google user information
   */
  private static async fetchGoogleUserInfo(accessToken: string): Promise<any> {
    try {
      const response = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });
      return await response.json();
    } catch (error) {
      console.error('Error fetching Google user info:', error);
      return null;
    }
  }

  /**
   * Fetch LinkedIn user information
   */
  private static async fetchLinkedInUserInfo(accessToken: string): Promise<any> {
    try {
      const [profileResponse, emailResponse] = await Promise.all([
        fetch('https://api.linkedin.com/v2/people/~', {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        }),
        fetch('https://api.linkedin.com/v2/emailAddress?q=members&projection=(elements*(handle~))', {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        }),
      ]);

      const profile = await profileResponse.json();
      const email = await emailResponse.json();

      return {
        id: profile.id,
        firstName: profile.firstName?.localized?.en_US || '',
        lastName: profile.lastName?.localized?.en_US || '',
        emailAddress: email.elements?.[0]?.['handle~']?.emailAddress || '',
        profilePicture: profile.profilePicture?.['displayImage~']?.elements?.[0]?.identifiers?.[0]?.identifier,
      };
    } catch (error) {
      console.error('Error fetching LinkedIn user info:', error);
      return null;
    }
  }

  /**
   * Fetch Microsoft user information
   */
  private static async fetchMicrosoftUserInfo(accessToken: string): Promise<any> {
    try {
      const response = await fetch('https://graph.microsoft.com/v1.0/me', {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });
      return await response.json();
    } catch (error) {
      console.error('Error fetching Microsoft user info:', error);
      return null;
    }
  }

  /**
   * Store authenticated user data securely
   */
  private static async storeAuthUser(user: AuthUser): Promise<void> {
    try {
      await SecureStore.setItemAsync(this.STORAGE_KEYS.USER, JSON.stringify(user));
      await SecureStore.setItemAsync(this.STORAGE_KEYS.ACCESS_TOKEN, user.accessToken);
      if (user.refreshToken) {
        await SecureStore.setItemAsync(this.STORAGE_KEYS.REFRESH_TOKEN, user.refreshToken);
      }
    } catch (error) {
      console.error('Error storing auth user:', error);
    }
  }

  /**
   * Get stored authenticated user
   */
  static async getStoredUser(): Promise<AuthUser | null> {
    try {
      const userJson = await SecureStore.getItemAsync(this.STORAGE_KEYS.USER);
      if (userJson) {
        const user = JSON.parse(userJson);
        // Convert expiresAt back to Date object
        if (user.expiresAt) {
          user.expiresAt = new Date(user.expiresAt);
        }
        return user;
      }
      return null;
    } catch (error) {
      console.error('Error getting stored user:', error);
      return null;
    }
  }

  /**
   * Check if user is authenticated and token is valid
   */
  static async isAuthenticated(): Promise<boolean> {
    try {
      const user = await this.getStoredUser();
      if (!user) return false;

      // Check if token is expired
      if (user.expiresAt && user.expiresAt < new Date()) {
        // Try to refresh token
        return await this.refreshToken(user);
      }

      return true;
    } catch (error) {
      console.error('Error checking authentication:', error);
      return false;
    }
  }

  /**
   * Refresh access token
   */
  static async refreshToken(user: AuthUser): Promise<boolean> {
    try {
      if (!user.refreshToken) return false;

      // Implementation depends on the OAuth provider
      // This is a simplified example
      console.log('Refreshing token for user:', user.email);
      
      // You would implement the actual token refresh logic here
      // based on the OAuth provider's refresh token endpoint
      
      return false; // Return true if refresh was successful
    } catch (error) {
      console.error('Error refreshing token:', error);
      return false;
    }
  }

  /**
   * Sign out user
   */
  static async signOut(): Promise<void> {
    try {
      await SecureStore.deleteItemAsync(this.STORAGE_KEYS.USER);
      await SecureStore.deleteItemAsync(this.STORAGE_KEYS.ACCESS_TOKEN);
      await SecureStore.deleteItemAsync(this.STORAGE_KEYS.REFRESH_TOKEN);
    } catch (error) {
      console.error('Error signing out:', error);
    }
  }

  /**
   * Get current access token
   */
  static async getAccessToken(): Promise<string | null> {
    try {
      return await SecureStore.getItemAsync(this.STORAGE_KEYS.ACCESS_TOKEN);
    } catch (error) {
      console.error('Error getting access token:', error);
      return null;
    }
  }
}

export default AuthService;
