import * as Sharing from 'expo-sharing';
import * as FileSystem from 'expo-file-system';
import { Alert, Platform } from 'react-native';

export interface ShareableContent {
  type: 'text' | 'image' | 'file' | 'url' | 'achievement' | 'event';
  title?: string;
  message: string;
  url?: string;
  imageUri?: string;
  fileUri?: string;
  mimeType?: string;
}

export interface AlumniAchievement {
  id: string;
  title: string;
  description: string;
  date: Date;
  category: 'graduation' | 'job' | 'promotion' | 'award' | 'certification' | 'other';
  imageUri?: string;
  alumniName: string;
  graduationYear?: string;
}

export interface AlumniEvent {
  id: string;
  title: string;
  description: string;
  date: Date;
  location: string;
  organizer: string;
  attendeeCount?: number;
  imageUri?: string;
  registrationUrl?: string;
}

export class SharingService {
  /**
   * Check if sharing is available on the device
   */
  static async isAvailable(): Promise<boolean> {
    try {
      return await Sharing.isAvailableAsync();
    } catch (error) {
      console.error('Error checking sharing availability:', error);
      return false;
    }
  }

  /**
   * Share text content
   */
  static async shareText(message: string, title?: string): Promise<boolean> {
    try {
      const isAvailable = await this.isAvailable();
      if (!isAvailable) {
        Alert.alert('Sharing Not Available', 'Sharing is not available on this device.');
        return false;
      }

      // Create a temporary text file to share
      const fileName = `protec_alumni_${Date.now()}.txt`;
      const fileUri = `${FileSystem.documentDirectory}${fileName}`;
      
      await FileSystem.writeAsStringAsync(fileUri, message);
      
      await Sharing.shareAsync(fileUri, {
        mimeType: 'text/plain',
        dialogTitle: title || 'Share PROTEC Alumni Content',
        UTI: 'public.plain-text',
      });

      // Clean up temporary file
      await FileSystem.deleteAsync(fileUri, { idempotent: true });
      
      return true;
    } catch (error) {
      console.error('Error sharing text:', error);
      Alert.alert('Sharing Error', 'Failed to share content. Please try again.');
      return false;
    }
  }

  /**
   * Share an image
   */
  static async shareImage(imageUri: string, message?: string, title?: string): Promise<boolean> {
    try {
      const isAvailable = await this.isAvailable();
      if (!isAvailable) {
        Alert.alert('Sharing Not Available', 'Sharing is not available on this device.');
        return false;
      }

      // Check if the image file exists
      const fileInfo = await FileSystem.getInfoAsync(imageUri);
      if (!fileInfo.exists) {
        Alert.alert('Error', 'Image file not found.');
        return false;
      }

      const shareOptions: Sharing.SharingOptions = {
        mimeType: 'image/jpeg',
        dialogTitle: title || message || 'Share PROTEC Alumni Image',
        UTI: 'public.jpeg',
      };

      await Sharing.shareAsync(imageUri, shareOptions);
      return true;
    } catch (error) {
      console.error('Error sharing image:', error);
      Alert.alert('Sharing Error', 'Failed to share image. Please try again.');
      return false;
    }
  }

  /**
   * Share a file
   */
  static async shareFile(
    fileUri: string, 
    mimeType: string, 
    title?: string
  ): Promise<boolean> {
    try {
      const isAvailable = await this.isAvailable();
      if (!isAvailable) {
        Alert.alert('Sharing Not Available', 'Sharing is not available on this device.');
        return false;
      }

      const fileInfo = await FileSystem.getInfoAsync(fileUri);
      if (!fileInfo.exists) {
        Alert.alert('Error', 'File not found.');
        return false;
      }

      await Sharing.shareAsync(fileUri, {
        mimeType,
        dialogTitle: title || 'Share PROTEC Alumni File',
      });

      return true;
    } catch (error) {
      console.error('Error sharing file:', error);
      Alert.alert('Sharing Error', 'Failed to share file. Please try again.');
      return false;
    }
  }

  /**
   * Share alumni achievement
   */
  static async shareAchievement(achievement: AlumniAchievement): Promise<boolean> {
    try {
      const shareText = this.formatAchievementText(achievement);
      
      if (achievement.imageUri) {
        // If there's an image, create a combined share with text and image
        return await this.shareImageWithText(achievement.imageUri, shareText, 'PROTEC Alumni Achievement');
      } else {
        // Share just the text
        return await this.shareText(shareText, 'PROTEC Alumni Achievement');
      }
    } catch (error) {
      console.error('Error sharing achievement:', error);
      Alert.alert('Sharing Error', 'Failed to share achievement. Please try again.');
      return false;
    }
  }

  /**
   * Share alumni event
   */
  static async shareEvent(event: AlumniEvent): Promise<boolean> {
    try {
      const shareText = this.formatEventText(event);
      
      if (event.imageUri) {
        return await this.shareImageWithText(event.imageUri, shareText, 'PROTEC Alumni Event');
      } else {
        return await this.shareText(shareText, 'PROTEC Alumni Event');
      }
    } catch (error) {
      console.error('Error sharing event:', error);
      Alert.alert('Sharing Error', 'Failed to share event. Please try again.');
      return false;
    }
  }

  /**
   * Share image with accompanying text
   */
  private static async shareImageWithText(
    imageUri: string, 
    text: string, 
    title: string
  ): Promise<boolean> {
    try {
      // Create a temporary text file with the message
      const textFileName = `protec_alumni_${Date.now()}.txt`;
      const textFileUri = `${FileSystem.documentDirectory}${textFileName}`;
      
      await FileSystem.writeAsStringAsync(textFileUri, text);

      // On iOS, we can share multiple items
      if (Platform.OS === 'ios') {
        // For iOS, we'll share the image first, then the text
        await this.shareImage(imageUri, text, title);
      } else {
        // For Android, share the image with the text as a caption
        await this.shareImage(imageUri, text, title);
      }

      // Clean up temporary file
      await FileSystem.deleteAsync(textFileUri, { idempotent: true });
      
      return true;
    } catch (error) {
      console.error('Error sharing image with text:', error);
      return false;
    }
  }

  /**
   * Format achievement for sharing
   */
  private static formatAchievementText(achievement: AlumniAchievement): string {
    const categoryEmoji = this.getCategoryEmoji(achievement.category);
    const dateStr = achievement.date.toLocaleDateString();
    
    let text = `${categoryEmoji} PROTEC Alumni Achievement ${categoryEmoji}\n\n`;
    text += `🎉 ${achievement.title}\n\n`;
    text += `👤 ${achievement.alumniName}`;
    
    if (achievement.graduationYear) {
      text += ` (Class of ${achievement.graduationYear})`;
    }
    
    text += `\n📅 ${dateStr}\n\n`;
    text += `${achievement.description}\n\n`;
    text += `#PROTECAlumni #Achievement #Success\n`;
    text += `Shared via PROTEC Alumni App`;
    
    return text;
  }

  /**
   * Format event for sharing
   */
  private static formatEventText(event: AlumniEvent): string {
    const dateStr = event.date.toLocaleDateString();
    const timeStr = event.date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    
    let text = `🎓 PROTEC Alumni Event 🎓\n\n`;
    text += `📅 ${event.title}\n\n`;
    text += `📅 ${dateStr} at ${timeStr}\n`;
    text += `📍 ${event.location}\n`;
    text += `👥 Organized by ${event.organizer}\n\n`;
    
    if (event.attendeeCount) {
      text += `👥 ${event.attendeeCount} alumni attending\n\n`;
    }
    
    text += `${event.description}\n\n`;
    
    if (event.registrationUrl) {
      text += `🔗 Register: ${event.registrationUrl}\n\n`;
    }
    
    text += `#PROTECAlumni #Event #Networking\n`;
    text += `Shared via PROTEC Alumni App`;
    
    return text;
  }

  /**
   * Get emoji for achievement category
   */
  private static getCategoryEmoji(category: AlumniAchievement['category']): string {
    const emojiMap = {
      graduation: '🎓',
      job: '💼',
      promotion: '📈',
      award: '🏆',
      certification: '📜',
      other: '⭐',
    };
    
    return emojiMap[category] || '⭐';
  }

  /**
   * Share contact information
   */
  static async shareContact(
    name: string, 
    email: string, 
    phone?: string, 
    company?: string
  ): Promise<boolean> {
    try {
      let contactText = `📱 PROTEC Alumni Contact\n\n`;
      contactText += `👤 ${name}\n`;
      contactText += `📧 ${email}\n`;
      
      if (phone) {
        contactText += `📞 ${phone}\n`;
      }
      
      if (company) {
        contactText += `🏢 ${company}\n`;
      }
      
      contactText += `\n#PROTECAlumni #Contact\n`;
      contactText += `Shared via PROTEC Alumni App`;
      
      return await this.shareText(contactText, 'PROTEC Alumni Contact');
    } catch (error) {
      console.error('Error sharing contact:', error);
      Alert.alert('Sharing Error', 'Failed to share contact. Please try again.');
      return false;
    }
  }

  /**
   * Share app invitation
   */
  static async shareAppInvitation(inviterName: string): Promise<boolean> {
    try {
      const inviteText = `🎓 Join the PROTEC Alumni Network! 🎓\n\n`;
      const message = `Hi! ${inviterName} invited you to join the PROTEC Alumni app.\n\n` +
        `Connect with fellow PROTEC graduates, discover networking opportunities, ` +
        `and stay updated on alumni events.\n\n` +
        `Download the app: [App Store/Play Store Link]\n\n` +
        `#PROTECAlumni #Networking #Community`;
      
      return await this.shareText(inviteText + message, 'Join PROTEC Alumni Network');
    } catch (error) {
      console.error('Error sharing app invitation:', error);
      Alert.alert('Sharing Error', 'Failed to share invitation. Please try again.');
      return false;
    }
  }

  /**
   * Create and share a QR code for quick contact sharing
   */
  static async shareQRCode(data: string, title: string): Promise<boolean> {
    try {
      // Note: You would need to implement QR code generation here
      // This is a placeholder for the QR code sharing functionality
      console.log('Sharing QR code with data:', data);
      
      // For now, share the data as text
      return await this.shareText(data, title);
    } catch (error) {
      console.error('Error sharing QR code:', error);
      Alert.alert('Sharing Error', 'Failed to share QR code. Please try again.');
      return false;
    }
  }

  /**
   * Share multiple items (iOS only)
   */
  static async shareMultipleItems(items: ShareableContent[]): Promise<boolean> {
    try {
      if (Platform.OS !== 'ios') {
        // For Android, share items one by one
        for (const item of items) {
          await this.shareContent(item);
        }
        return true;
      }

      // iOS implementation for sharing multiple items would go here
      // This is a simplified version
      const combinedText = items
        .map(item => item.message)
        .join('\n\n---\n\n');
      
      return await this.shareText(combinedText, 'PROTEC Alumni Content');
    } catch (error) {
      console.error('Error sharing multiple items:', error);
      Alert.alert('Sharing Error', 'Failed to share content. Please try again.');
      return false;
    }
  }

  /**
   * Generic content sharing method
   */
  static async shareContent(content: ShareableContent): Promise<boolean> {
    try {
      switch (content.type) {
        case 'text':
          return await this.shareText(content.message, content.title);
        
        case 'image':
          if (content.imageUri) {
            return await this.shareImage(content.imageUri, content.message, content.title);
          }
          break;
        
        case 'file':
          if (content.fileUri && content.mimeType) {
            return await this.shareFile(content.fileUri, content.mimeType, content.title);
          }
          break;
        
        case 'url':
          const urlText = content.url ? `${content.message}\n\n${content.url}` : content.message;
          return await this.shareText(urlText, content.title);
        
        default:
          return await this.shareText(content.message, content.title);
      }
      
      return false;
    } catch (error) {
      console.error('Error sharing content:', error);
      Alert.alert('Sharing Error', 'Failed to share content. Please try again.');
      return false;
    }
  }
}

export default SharingService;
