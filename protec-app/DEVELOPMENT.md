# PROTEC Alumni App - Development Guide

## Overview

This is the PROTEC Alumni mobile application built with Expo and React Native. The app provides a comprehensive platform for PROTEC alumni to connect, share opportunities, attend events, and build their professional network.

## Features Implemented

### 🔐 Authentication & Security
- Magic link authentication with deep linking
- Secure token storage using Expo SecureStore
- Session management with automatic token refresh
- Role-based access control

### 👥 Alumni Directory & Networking
- Advanced search and filtering capabilities
- Location-based alumni discovery
- Professional profile management
- Contact export to device contacts
- Skills and industry-based matching

### 📅 Events Management
- Event creation and management
- RSVP functionality with status tracking
- Calendar integration (expo-calendar)
- Location-based event discovery
- Event sharing capabilities

### 💬 Community Features
- Social feed with posts and interactions
- Like and comment system
- Content sharing and engagement
- Success story sharing
- Professional updates

### 📱 Native Integrations
- **expo-contacts**: Export alumni to device contacts
- **expo-location**: Location-based features and nearby discovery
- **expo-calendar**: Add events to device calendar
- **expo-sharing**: Share content and profiles
- **expo-auth-session**: OAuth and deep link authentication

### 🎨 UI/UX
- Material Design and iOS HIG compliance
- Dark/light theme support
- Smooth 60fps animations
- Accessibility features
- Professional, premium design

## Development Setup

### Prerequisites

- Node.js 18+ and npm/yarn
- Expo CLI (`npm install -g @expo/cli`)
- EAS CLI (`npm install -g eas-cli`)
- Android Studio (for Android development)
- Xcode (for iOS development, macOS only)

### Installation

1. **Clone and install dependencies:**
   ```bash
   cd protec-app
   npm install
   ```

2. **Set up environment variables:**
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your configuration
   ```

3. **Start the development server:**
   ```bash
   npm start
   ```

### Environment Configuration

Create `.env.local` with the following variables:

```env
# API Configuration
EXPO_PUBLIC_API_URL=http://localhost:3000
EXPO_PUBLIC_DEV_MODE=true

# Authentication
EXPO_PUBLIC_AUTH_REDIRECT_URI=protec-alumni://auth

# External Services (optional for development)
EXPO_PUBLIC_GOOGLE_MAPS_API_KEY=your_google_maps_key
EXPO_PUBLIC_SENTRY_DSN=your_sentry_dsn
```

## Building and Testing

### Development Builds

Development builds include the Expo development client for faster iteration:

```bash
# Build for development (includes dev client)
npm run build:dev

# Build for specific platform
eas build --profile development --platform ios
eas build --profile development --platform android
```

### Preview Builds

Preview builds are production-like builds for testing:

```bash
# Build preview version
npm run build:preview

# Install on device for testing
# iOS: Use TestFlight or direct installation
# Android: Install APK directly
```

### Production Builds

```bash
# Build for production
npm run build:prod

# Submit to app stores
npm run submit:ios
npm run submit:android
```

### Testing

```bash
# Run unit tests
npm test

# Run tests in watch mode
npm run test:watch

# Generate coverage report
npm run test:coverage

# Run tests for CI
npm run test:ci

# Run E2E tests (if configured)
npm run test:e2e
```

## Project Structure

```
protec-app/
├── app/                    # App screens (Expo Router)
│   ├── (auth)/            # Authentication screens
│   ├── (tabs)/            # Main tab navigation
│   └── +not-found.tsx     # 404 screen
├── components/            # Reusable components
│   ├── ui/               # UI components (Button, Card, etc.)
│   ├── feed/             # Feed-specific components
│   └── directory/        # Directory-specific components
├── lib/                  # Core application logic
│   ├── api/              # API configuration (tRPC)
│   ├── hooks/            # Custom React hooks
│   ├── providers/        # Context providers
│   ├── services/         # External service integrations
│   ├── stores/           # State management (Zustand)
│   └── types/            # TypeScript type definitions
├── constants/            # App constants and configuration
├── assets/              # Static assets (images, fonts)
└── __tests__/           # Test files
```

## Key Technologies

- **Expo SDK 52**: React Native framework
- **Expo Router**: File-based navigation
- **TypeScript**: Type safety
- **tRPC**: Type-safe API client
- **React Query**: Data fetching and caching
- **Zustand**: State management
- **Jest**: Unit testing
- **React Native Testing Library**: Component testing
- **EAS**: Build and deployment

## Native Features Integration

### Contacts Integration
```typescript
import { ContactsService } from '@/lib/services/contactsService';

// Add alumni to device contacts
await ContactsService.addAlumniToContacts(alumni);

// Export multiple alumni
await ContactsService.exportAlumniToContacts(alumniList);
```

### Location Services
```typescript
import { LocationService } from '@/lib/services/locationService';

// Get current location
const location = await LocationService.getCurrentLocation();

// Find nearby alumni
const nearbyAlumni = await LocationService.findNearbyAlumni(alumni, 50);
```

### Calendar Integration
```typescript
import { EventCalendarService } from '@/lib/services/eventCalendarService';

// Add event to calendar
await EventCalendarService.addEventToCalendar(eventData);
```

### Sharing
```typescript
import { SharingService } from '@/lib/services/sharingService';

// Share alumni profile
await SharingService.shareAlumniProfile(alumni);

// Share event
await SharingService.shareEvent(event);
```

## Development Workflow

### 1. Feature Development
1. Create feature branch from `main`
2. Implement feature with tests
3. Run tests and linting
4. Create pull request

### 2. Testing
1. Write unit tests for new components/services
2. Test on physical devices using development builds
3. Verify native integrations work correctly
4. Test authentication flows

### 3. Building
1. Test with preview builds
2. Submit to internal testing
3. Build production version
4. Submit to app stores

## Deployment

### EAS Updates (OTA)
```bash
# Deploy to development
npm run update:dev

# Deploy to preview
npm run update:preview

# Deploy to production
npm run update:prod
```

### App Store Deployment
1. Build production version
2. Test thoroughly
3. Submit to app stores
4. Monitor for issues

## Troubleshooting

### Common Issues

1. **Metro bundler issues**: Clear cache with `npx expo start --clear`
2. **Native module issues**: Rebuild development client
3. **Authentication issues**: Check redirect URI configuration
4. **API connection issues**: Verify API URL and network connectivity

### Debug Tools

- **Expo Dev Tools**: Built-in debugging
- **React Native Debugger**: Advanced debugging
- **Flipper**: Native debugging (for development builds)

## Contributing

1. Follow TypeScript best practices
2. Write tests for new features
3. Follow the established code style
4. Update documentation for new features
5. Test on both iOS and Android

## Performance Considerations

- Use React.memo for expensive components
- Implement proper list virtualization
- Optimize images and assets
- Use lazy loading for screens
- Monitor bundle size and performance

## Security

- Never commit sensitive data
- Use Expo SecureStore for tokens
- Validate all user inputs
- Implement proper error handling
- Follow OWASP mobile security guidelines
