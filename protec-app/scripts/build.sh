#!/bin/bash

# PROTEC Alumni App - Build Script
# This script handles building the app for different environments and platforms

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Default values
PLATFORM="all"
PROFILE="development"
CLEAR_CACHE=false
SUBMIT=false
LOCAL_BUILD=false

# Help function
show_help() {
    cat << EOF
PROTEC Alumni App Build Script

Usage: $0 [OPTIONS]

OPTIONS:
    -p, --platform PLATFORM    Platform to build (ios, android, all) [default: all]
    -e, --profile PROFILE       Build profile (development, preview, production) [default: development]
    -c, --clear-cache          Clear build cache before building
    -s, --submit               Submit to app stores after building (production only)
    -l, --local                Build locally instead of using EAS
    -h, --help                 Show this help message

EXAMPLES:
    $0                                          # Build for all platforms (development)
    $0 -p ios -e production                     # Build iOS production
    $0 -p android -e preview -c                 # Build Android preview with cache clear
    $0 -e production -s                         # Build production and submit to stores
    $0 -l -p ios                               # Build iOS locally

PROFILES:
    development    - Development build with debugging enabled
    preview        - Preview build for testing (similar to production)
    production     - Production build for app stores

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -p|--platform)
            PLATFORM="$2"
            shift 2
            ;;
        -e|--profile)
            PROFILE="$2"
            shift 2
            ;;
        -c|--clear-cache)
            CLEAR_CACHE=true
            shift
            ;;
        -s|--submit)
            SUBMIT=true
            shift
            ;;
        -l|--local)
            LOCAL_BUILD=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Validate platform
if [[ ! "$PLATFORM" =~ ^(ios|android|all)$ ]]; then
    print_error "Invalid platform: $PLATFORM. Must be ios, android, or all"
    exit 1
fi

# Validate profile
if [[ ! "$PROFILE" =~ ^(development|preview|production)$ ]]; then
    print_error "Invalid profile: $PROFILE. Must be development, preview, or production"
    exit 1
fi

# Check if submit is only used with production
if [[ "$SUBMIT" == true && "$PROFILE" != "production" ]]; then
    print_error "Submit option can only be used with production profile"
    exit 1
fi

# Check dependencies
check_dependencies() {
    print_status "Checking dependencies..."
    
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed"
        exit 1
    fi
    
    if [[ "$LOCAL_BUILD" == false ]]; then
        if ! command -v eas &> /dev/null; then
            print_error "EAS CLI is not installed. Install with: npm install -g eas-cli"
            exit 1
        fi
    else
        if ! command -v expo &> /dev/null; then
            print_error "Expo CLI is not installed. Install with: npm install -g @expo/cli"
            exit 1
        fi
    fi
    
    print_success "Dependencies check passed"
}

# Install project dependencies
install_dependencies() {
    print_status "Installing project dependencies..."
    npm install
    print_success "Project dependencies installed"
}

# Run pre-build checks
pre_build_checks() {
    print_status "Running pre-build checks..."
    
    # Type checking
    print_status "Type checking..."
    npm run type-check
    
    # Linting
    print_status "Linting..."
    npm run lint
    
    # Tests (skip for local builds to speed up)
    if [[ "$LOCAL_BUILD" == false ]]; then
        print_status "Running tests..."
        npm run test -- --watchAll=false
    fi
    
    print_success "Pre-build checks passed"
}

# Update version numbers
update_version() {
    if [[ "$PROFILE" == "production" ]]; then
        print_status "Updating version numbers..."
        
        # Get current version
        CURRENT_VERSION=$(node -p "require('./package.json').version")
        BUILD_NUMBER=$(date +%Y%m%d%H%M)
        
        print_status "Current version: $CURRENT_VERSION"
        print_status "Build number: $BUILD_NUMBER"
        
        # Update app.json with build numbers
        node -e "
            const fs = require('fs');
            const app = JSON.parse(fs.readFileSync('app.json', 'utf8'));
            app.expo.ios.buildNumber = '$BUILD_NUMBER';
            app.expo.android.versionCode = parseInt('$BUILD_NUMBER');
            fs.writeFileSync('app.json', JSON.stringify(app, null, 2));
        "
        
        print_success "Version numbers updated"
    fi
}

# Clear cache if requested
clear_cache() {
    if [[ "$CLEAR_CACHE" == true ]]; then
        print_status "Clearing build cache..."
        
        if [[ "$LOCAL_BUILD" == false ]]; then
            eas build:clear-cache
        else
            expo r -c
            rm -rf .expo/
            rm -rf node_modules/.cache/
        fi
        
        print_success "Cache cleared"
    fi
}

# Build the app
build_app() {
    print_status "Building app..."
    print_status "Platform: $PLATFORM"
    print_status "Profile: $PROFILE"
    print_status "Local build: $LOCAL_BUILD"
    
    if [[ "$LOCAL_BUILD" == true ]]; then
        # Local build using Expo
        print_status "Building locally with Expo..."
        
        if [[ "$PLATFORM" == "all" ]]; then
            expo export --platform all
        else
            expo export --platform $PLATFORM
        fi
        
        print_success "Local build completed"
    else
        # Cloud build using EAS
        print_status "Building with EAS..."
        
        BUILD_ARGS="--profile $PROFILE --non-interactive"
        
        if [[ "$CLEAR_CACHE" == true ]]; then
            BUILD_ARGS="$BUILD_ARGS --clear-cache"
        fi
        
        if [[ "$PLATFORM" == "all" ]]; then
            eas build --platform all $BUILD_ARGS
        else
            eas build --platform $PLATFORM $BUILD_ARGS
        fi
        
        print_success "EAS build completed"
    fi
}

# Submit to app stores
submit_app() {
    if [[ "$SUBMIT" == true ]]; then
        print_status "Submitting to app stores..."
        
        if [[ "$PLATFORM" == "all" ]]; then
            eas submit --platform all --profile production --non-interactive
        else
            eas submit --platform $PLATFORM --profile production --non-interactive
        fi
        
        print_success "App submitted to stores"
    fi
}

# Generate build report
generate_report() {
    print_status "Generating build report..."
    
    REPORT_FILE="build-report-$(date +%Y%m%d-%H%M%S).txt"
    
    cat > $REPORT_FILE << EOF
PROTEC Alumni App - Build Report
Generated: $(date)

Build Configuration:
- Platform: $PLATFORM
- Profile: $PROFILE
- Local Build: $LOCAL_BUILD
- Clear Cache: $CLEAR_CACHE
- Submit: $SUBMIT

Build Details:
- Node Version: $(node --version)
- npm Version: $(npm --version)
- Expo CLI Version: $(expo --version 2>/dev/null || echo "Not installed")
- EAS CLI Version: $(eas --version 2>/dev/null || echo "Not installed")

App Information:
- Version: $(node -p "require('./package.json').version")
- Bundle ID (iOS): $(node -p "require('./app.json').expo.ios.bundleIdentifier")
- Package Name (Android): $(node -p "require('./app.json').expo.android.package")

Build Status: ✅ Completed Successfully

EOF
    
    print_success "Build report generated: $REPORT_FILE"
}

# Main execution
main() {
    echo "🚀 PROTEC Alumni App - Build Script"
    echo "==================================="
    
    check_dependencies
    install_dependencies
    pre_build_checks
    update_version
    clear_cache
    build_app
    submit_app
    generate_report
    
    echo ""
    print_success "🎉 Build completed successfully!"
    echo ""
    
    if [[ "$LOCAL_BUILD" == true ]]; then
        echo "Local build artifacts are in the 'dist/' directory"
    else
        echo "Check EAS dashboard for build status: https://expo.dev"
    fi
    
    if [[ "$SUBMIT" == true ]]; then
        echo "Apps have been submitted to app stores"
        echo "Check App Store Connect and Google Play Console for review status"
    fi
}

# Run main function
main
