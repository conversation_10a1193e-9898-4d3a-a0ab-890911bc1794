{"cli": {"version": ">= 12.0.0", "appVersionSource": "remote"}, "build": {"development": {"developmentClient": true, "distribution": "internal", "ios": {"resourceClass": "m-medium", "simulator": true, "buildConfiguration": "Debug"}, "android": {"resourceClass": "medium", "buildType": "apk", "gradleCommand": ":app:assembleDebug"}, "channel": "development", "env": {"NODE_ENV": "development", "EXPO_PUBLIC_API_URL": "http://localhost:3000", "EXPO_PUBLIC_DEV_MODE": "true"}}, "preview": {"distribution": "internal", "ios": {"resourceClass": "m-medium", "simulator": false, "buildConfiguration": "Release"}, "android": {"resourceClass": "medium", "buildType": "apk", "gradleCommand": ":app:assembleRelease"}, "channel": "preview", "env": {"NODE_ENV": "production", "EXPO_PUBLIC_API_URL": "https://staging-api.protec-alumni.app", "EXPO_PUBLIC_DEV_MODE": "false"}}, "production": {"ios": {"resourceClass": "m-medium", "simulator": false, "buildConfiguration": "Release"}, "android": {"resourceClass": "medium", "buildType": "app-bundle"}, "channel": "production", "env": {"NODE_ENV": "production", "EXPO_PUBLIC_API_URL": "https://api.protec-alumni.app", "EXPO_PUBLIC_DEV_MODE": "false"}}}, "submit": {"production": {"ios": {"appleId": "<EMAIL>", "ascAppId": "your-app-store-connect-app-id", "appleTeamId": "your-apple-team-id"}, "android": {"serviceAccountKeyPath": "./google-service-account.json", "track": "internal"}}}, "updates": {"url": "https://u.expo.dev/your-project-id"}}