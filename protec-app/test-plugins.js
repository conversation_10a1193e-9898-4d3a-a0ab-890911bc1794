// Simple test script to verify plugin imports work correctly
// Run with: node test-plugins.js

console.log('🧪 Testing PROTEC Alumni Plugin Imports...\n');

try {
  // Test if we can import the services without errors
  console.log('📱 Testing Contacts Service...');
  const ContactsService = require('./services/contactsService.ts');
  console.log('✅ Contacts Service imported successfully');

  console.log('📍 Testing Location Service...');
  const LocationService = require('./services/locationService.ts');
  console.log('✅ Location Service imported successfully');

  console.log('📅 Testing Calendar Service...');
  const CalendarService = require('./services/calendarService.ts');
  console.log('✅ Calendar Service imported successfully');

  console.log('🔐 Testing Auth Service...');
  const AuthService = require('./services/authService.ts');
  console.log('✅ Auth Service imported successfully');

  console.log('📤 Testing Sharing Service...');
  const SharingService = require('./services/sharingService.ts');
  console.log('✅ Sharing Service imported successfully');

  console.log('📦 Testing Services Index...');
  const ServicesIndex = require('./services/index.ts');
  console.log('✅ Services Index imported successfully');

  console.log('\n🎉 All plugin services imported successfully!');
  console.log('✅ Build should work correctly with these plugins.');

} catch (error) {
  console.error('❌ Error importing plugins:', error.message);
  console.error('🔧 Please check the plugin configuration and dependencies.');
  process.exit(1);
}

console.log('\n📋 Plugin Summary:');
console.log('• expo-contacts - Alumni directory integration');
console.log('• expo-location - Event location services');
console.log('• expo-calendar - Event scheduling');
console.log('• expo-auth-session - Social authentication');
console.log('• expo-sharing - Content sharing');
console.log('\n🚀 Ready for development and production builds!');
