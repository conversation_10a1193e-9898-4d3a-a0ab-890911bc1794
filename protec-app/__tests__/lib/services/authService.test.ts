import { AuthService } from '@/lib/services/authService';
import * as SecureStore from 'expo-secure-store';

// Mock SecureStore
jest.mock('expo-secure-store');
const mockSecureStore = SecureStore as jest.Mocked<typeof SecureStore>;

describe('AuthService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset fetch mock
    (global.fetch as jest.Mock).mockClear();
  });

  describe('storeTokens', () => {
    it('should store access and refresh tokens', async () => {
      const tokens = {
        accessToken: 'access-token',
        refreshToken: 'refresh-token',
        expiresIn: 3600,
      };

      await AuthService.storeTokens(tokens);

      expect(mockSecureStore.setItemAsync).toHaveBeenCalledWith(
        'access_token',
        'access-token'
      );
      expect(mockSecureStore.setItemAsync).toHaveBeenCalledWith(
        'refresh_token',
        'refresh-token'
      );
    });
  });

  describe('getAccessToken', () => {
    it('should return stored access token', async () => {
      mockSecureStore.getItemAsync.mockResolvedValue('stored-token');

      const token = await AuthService.getAccessToken();

      expect(token).toBe('stored-token');
      expect(mockSecureStore.getItemAsync).toHaveBeenCalledWith('access_token');
    });

    it('should return null if no token stored', async () => {
      mockSecureStore.getItemAsync.mockResolvedValue(null);

      const token = await AuthService.getAccessToken();

      expect(token).toBeNull();
    });
  });

  describe('clearTokens', () => {
    it('should remove all stored tokens', async () => {
      await AuthService.clearTokens();

      expect(mockSecureStore.deleteItemAsync).toHaveBeenCalledWith('access_token');
      expect(mockSecureStore.deleteItemAsync).toHaveBeenCalledWith('refresh_token');
      expect(mockSecureStore.deleteItemAsync).toHaveBeenCalledWith('token_expiry');
    });
  });

  describe('sendMagicLink', () => {
    it('should send magic link successfully', async () => {
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ success: true }),
      });

      const result = await AuthService.sendMagicLink('<EMAIL>');

      expect(result.success).toBe(true);
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/auth/magic-link'),
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
          }),
          body: JSON.stringify({ email: '<EMAIL>' }),
        })
      );
    });

    it('should handle API errors', async () => {
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: false,
        status: 400,
        json: () => Promise.resolve({ error: 'Invalid email' }),
      });

      const result = await AuthService.sendMagicLink('invalid-email');

      expect(result.success).toBe(false);
      expect(result.error).toBe('Invalid email');
    });

    it('should handle network errors', async () => {
      (global.fetch as jest.Mock).mockRejectedValue(new Error('Network error'));

      const result = await AuthService.sendMagicLink('<EMAIL>');

      expect(result.success).toBe(false);
      expect(result.error).toBe('Network error occurred');
    });
  });

  describe('verifyMagicLink', () => {
    it('should verify magic link successfully', async () => {
      const mockTokens = {
        accessToken: 'new-access-token',
        refreshToken: 'new-refresh-token',
        expiresIn: 3600,
      };

      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          tokens: mockTokens,
          user: { id: '1', email: '<EMAIL>' },
        }),
      });

      const result = await AuthService.verifyMagicLink('valid-token');

      expect(result.success).toBe(true);
      expect(result.tokens).toEqual(mockTokens);
      expect(result.user).toEqual({ id: '1', email: '<EMAIL>' });
    });

    it('should handle invalid tokens', async () => {
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: false,
        status: 401,
        json: () => Promise.resolve({ error: 'Invalid or expired token' }),
      });

      const result = await AuthService.verifyMagicLink('invalid-token');

      expect(result.success).toBe(false);
      expect(result.error).toBe('Invalid or expired token');
    });
  });

  describe('refreshAccessToken', () => {
    it('should refresh token successfully', async () => {
      mockSecureStore.getItemAsync.mockResolvedValue('refresh-token');
      
      const newTokens = {
        accessToken: 'new-access-token',
        refreshToken: 'new-refresh-token',
        expiresIn: 3600,
      };

      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ tokens: newTokens }),
      });

      const result = await AuthService.refreshAccessToken();

      expect(result.success).toBe(true);
      expect(result.tokens).toEqual(newTokens);
    });

    it('should handle missing refresh token', async () => {
      mockSecureStore.getItemAsync.mockResolvedValue(null);

      const result = await AuthService.refreshAccessToken();

      expect(result.success).toBe(false);
      expect(result.error).toBe('No refresh token available');
    });
  });

  describe('isTokenExpired', () => {
    it('should return true for expired token', async () => {
      const pastTime = Date.now() - 1000;
      mockSecureStore.getItemAsync.mockResolvedValue(pastTime.toString());

      const isExpired = await AuthService.isTokenExpired();

      expect(isExpired).toBe(true);
    });

    it('should return false for valid token', async () => {
      const futureTime = Date.now() + 3600000;
      mockSecureStore.getItemAsync.mockResolvedValue(futureTime.toString());

      const isExpired = await AuthService.isTokenExpired();

      expect(isExpired).toBe(false);
    });

    it('should return true if no expiry stored', async () => {
      mockSecureStore.getItemAsync.mockResolvedValue(null);

      const isExpired = await AuthService.isTokenExpired();

      expect(isExpired).toBe(true);
    });
  });

  describe('logout', () => {
    it('should clear tokens and call logout endpoint', async () => {
      mockSecureStore.getItemAsync.mockResolvedValue('access-token');
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ success: true }),
      });

      const result = await AuthService.logout();

      expect(result.success).toBe(true);
      expect(mockSecureStore.deleteItemAsync).toHaveBeenCalledWith('access_token');
      expect(mockSecureStore.deleteItemAsync).toHaveBeenCalledWith('refresh_token');
      expect(mockSecureStore.deleteItemAsync).toHaveBeenCalledWith('token_expiry');
    });
  });
});
