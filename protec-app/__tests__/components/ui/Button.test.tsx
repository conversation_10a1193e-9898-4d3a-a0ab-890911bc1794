import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { Button } from '@/components/ui/Button';

describe('Button Component', () => {
  it('renders correctly with title', () => {
    const { getByText } = render(<Button title="Test Button" />);
    expect(getByText('Test Button')).toBeTruthy();
  });

  it('calls onPress when pressed', () => {
    const mockOnPress = jest.fn();
    const { getByText } = render(
      <Button title="Test Button" onPress={mockOnPress} />
    );
    
    fireEvent.press(getByText('Test Button'));
    expect(mockOnPress).toHaveBeenCalledTimes(1);
  });

  it('is disabled when disabled prop is true', () => {
    const mockOnPress = jest.fn();
    const { getByText } = render(
      <Button title="Test Button" onPress={mockOnPress} disabled />
    );
    
    const button = getByText('Test Button');
    fireEvent.press(button);
    expect(mockOnPress).not.toHaveBeenCalled();
  });

  it('shows loading state when loading prop is true', () => {
    const { getByTestId } = render(
      <Button title="Test Button" loading testID="button" />
    );
    
    expect(getByTestId('button')).toBeTruthy();
  });

  it('applies correct variant styles', () => {
    const { getByTestId } = render(
      <Button title="Test Button" variant="outline" testID="button" />
    );
    
    expect(getByTestId('button')).toBeTruthy();
  });

  it('applies correct size styles', () => {
    const { getByTestId } = render(
      <Button title="Test Button" size="sm" testID="button" />
    );
    
    expect(getByTestId('button')).toBeTruthy();
  });

  it('renders with custom style', () => {
    const customStyle = { backgroundColor: 'red' };
    const { getByTestId } = render(
      <Button title="Test Button" style={customStyle} testID="button" />
    );
    
    expect(getByTestId('button')).toBeTruthy();
  });
});
