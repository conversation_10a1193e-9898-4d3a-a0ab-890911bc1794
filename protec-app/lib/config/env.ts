import Constants from 'expo-constants';

interface Config {
  API_URL: string;
  WEB_URL: string;
  APP_ENV: 'development' | 'staging' | 'production';
  TIMEOUT: number;
  DEV_MODE: boolean;
  AUTH_PROVIDER: string;
  OAUTH_REDIRECT_URI: string;
  PUSH_NOTIFICATIONS_ENABLED: boolean;
  ENABLE_OFFLINE_MODE: boolean;
  ENABLE_ANALYTICS: boolean;
  LOG_LEVEL: 'debug' | 'info' | 'warn' | 'error';
}

const getConfig = (): Config => {
  const isDev = __DEV__;
  const extra = Constants.expoConfig?.extra || {};

  if (isDev) {
    // Development configuration
    const debuggerHost = Constants.expoConfig?.hostUri?.split(':')[0];
    return {
      API_URL: process.env.EXPO_PUBLIC_API_URL || `http://${debuggerHost || 'localhost'}:3000`,
      WEB_URL: process.env.EXPO_PUBLIC_WEB_URL || `http://${debuggerHost || 'localhost'}:3000`,
      APP_ENV: 'development',
      TIMEOUT: parseInt(process.env.EXPO_PUBLIC_API_TIMEOUT || '30000'),
      DEV_MODE: true,
      AUTH_PROVIDER: process.env.EXPO_PUBLIC_AUTH_PROVIDER || 'nextauth',
      OAUTH_REDIRECT_URI: process.env.EXPO_PUBLIC_OAUTH_REDIRECT_URI || 'protec-alumni://auth',
      PUSH_NOTIFICATIONS_ENABLED: process.env.EXPO_PUBLIC_PUSH_NOTIFICATIONS_ENABLED === 'true',
      ENABLE_OFFLINE_MODE: process.env.EXPO_PUBLIC_ENABLE_OFFLINE_MODE === 'true',
      ENABLE_ANALYTICS: process.env.EXPO_PUBLIC_ENABLE_ANALYTICS === 'true',
      LOG_LEVEL: (process.env.EXPO_PUBLIC_LOG_LEVEL as Config['LOG_LEVEL']) || 'debug',
    };
  }

  // Production configuration
  return {
    API_URL: process.env.EXPO_PUBLIC_API_URL || 'https://protec.co.za',
    WEB_URL: process.env.EXPO_PUBLIC_WEB_URL || 'https://protec.co.za',
    APP_ENV: 'production',
    TIMEOUT: parseInt(process.env.EXPO_PUBLIC_API_TIMEOUT || '30000'),
    DEV_MODE: false,
    AUTH_PROVIDER: process.env.EXPO_PUBLIC_AUTH_PROVIDER || 'nextauth',
    OAUTH_REDIRECT_URI: process.env.EXPO_PUBLIC_OAUTH_REDIRECT_URI || 'protec-alumni://auth',
    PUSH_NOTIFICATIONS_ENABLED: process.env.EXPO_PUBLIC_PUSH_NOTIFICATIONS_ENABLED === 'true',
    ENABLE_OFFLINE_MODE: process.env.EXPO_PUBLIC_ENABLE_OFFLINE_MODE === 'true',
    ENABLE_ANALYTICS: process.env.EXPO_PUBLIC_ENABLE_ANALYTICS === 'true',
    LOG_LEVEL: (process.env.EXPO_PUBLIC_LOG_LEVEL as Config['LOG_LEVEL']) || 'info',
  };
};

export const config = getConfig();
