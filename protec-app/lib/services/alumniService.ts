import { trpcClient } from '../api/trpc';
import { Alumni } from '../types';

export interface AlumniFilters {
  graduationYear?: number;
  industry?: string;
  location?: string;
  skills?: string[];
  province?: string;
  city?: string;
  country?: string;
  programme?: string;
}

export interface AlumniSearchParams {
  query?: string;
  filters?: AlumniFilters;
  page?: number;
  limit?: number;
  sortBy?: 'name' | 'graduationYear' | 'lastActive';
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedAlumniResponse {
  alumni: Alumni[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

export class AlumniService {
  /**
   * Fetch all alumni with optional filtering and pagination
   */
  static async getAlumni(params: AlumniSearchParams = {}): Promise<PaginatedAlumniResponse> {
    try {
      const response = await trpcClient.alumni.getAll.query({
        query: params.query,
        filters: params.filters,
        page: params.page || 1,
        limit: params.limit || 20,
        sortBy: params.sortBy || 'name',
        sortOrder: params.sortOrder || 'asc',
      });

      return response;
    } catch (error) {
      console.error('Failed to fetch alumni:', error);
      throw new Error('Failed to fetch alumni');
    }
  }

  /**
   * Get alumni by ID
   */
  static async getAlumniById(id: string): Promise<Alumni> {
    try {
      const alumni = await trpcClient.alumni.getById.query({ id });
      return alumni;
    } catch (error) {
      console.error('Failed to fetch alumni by ID:', error);
      throw new Error('Failed to fetch alumni details');
    }
  }

  /**
   * Search alumni
   */
  static async searchAlumni(query: string, filters?: AlumniFilters): Promise<Alumni[]> {
    try {
      const response = await trpcClient.alumni.search.query({
        query,
        filters,
      });

      return response;
    } catch (error) {
      console.error('Failed to search alumni:', error);
      throw new Error('Failed to search alumni');
    }
  }

  /**
   * Get alumni connections for current user
   */
  static async getConnections(): Promise<Alumni[]> {
    try {
      const connections = await trpcClient.alumni.getConnections.query();
      return connections;
    } catch (error) {
      console.error('Failed to fetch connections:', error);
      throw new Error('Failed to fetch connections');
    }
  }

  /**
   * Connect with an alumni
   */
  static async connectWithAlumni(alumniId: string): Promise<void> {
    try {
      await trpcClient.alumni.connect.mutate({ alumniId });
    } catch (error) {
      console.error('Failed to connect with alumni:', error);
      throw new Error('Failed to connect with alumni');
    }
  }

  /**
   * Disconnect from an alumni
   */
  static async disconnectFromAlumni(alumniId: string): Promise<void> {
    try {
      await trpcClient.alumni.disconnect.mutate({ alumniId });
    } catch (error) {
      console.error('Failed to disconnect from alumni:', error);
      throw new Error('Failed to disconnect from alumni');
    }
  }

  /**
   * Get recommended alumni for networking
   */
  static async getRecommendations(): Promise<Alumni[]> {
    try {
      const recommendations = await trpcClient.alumni.getRecommendations.query();
      return recommendations;
    } catch (error) {
      console.error('Failed to fetch recommendations:', error);
      throw new Error('Failed to fetch recommendations');
    }
  }

  /**
   * Get trending alumni (most active, popular, etc.)
   */
  static async getTrending(): Promise<Alumni[]> {
    try {
      const trending = await trpcClient.alumni.getTrending.query();
      return trending;
    } catch (error) {
      console.error('Failed to fetch trending alumni:', error);
      throw new Error('Failed to fetch trending alumni');
    }
  }

  /**
   * Get career path recommendations based on user's profile
   */
  static async getCareerPathRecommendations(): Promise<Alumni[]> {
    try {
      const recommendations = await trpcClient.alumni.getCareerPathRecommendations.query();
      return recommendations;
    } catch (error) {
      console.error('Failed to fetch career path recommendations:', error);
      throw new Error('Failed to fetch career path recommendations');
    }
  }

  /**
   * Update current user's alumni profile
   */
  static async updateProfile(data: Partial<Alumni>): Promise<Alumni> {
    try {
      const updatedProfile = await trpcClient.alumni.update.mutate(data);
      return updatedProfile;
    } catch (error) {
      console.error('Failed to update profile:', error);
      throw new Error('Failed to update profile');
    }
  }

  /**
   * Get current user's alumni profile
   */
  static async getMyProfile(): Promise<Alumni> {
    try {
      const profile = await trpcClient.alumni.me.query();
      return profile;
    } catch (error) {
      console.error('Failed to fetch profile:', error);
      throw new Error('Failed to fetch profile');
    }
  }
}

export const alumniService = AlumniService;
