import * as Sharing from 'expo-sharing';
import * as FileSystem from 'expo-file-system';
import { Alert, Share, Platform } from 'react-native';
import { Alumni, Event, Post } from '../types';

export interface ShareableContent {
  title: string;
  message: string;
  url?: string;
  imageUrl?: string;
}

export interface ShareOptions {
  dialogTitle?: string;
  excludedActivityTypes?: string[];
  tintColor?: string;
}

export class SharingService {
  /**
   * Check if sharing is available
   */
  static async isAvailable(): Promise<boolean> {
    try {
      return await Sharing.isAvailableAsync();
    } catch (error) {
      console.error('Error checking sharing availability:', error);
      return false;
    }
  }

  /**
   * Share alumni profile
   */
  static async shareAlumniProfile(
    alumni: Alumni,
    options?: ShareOptions
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const shareContent = this.generateAlumniShareContent(alumni);
      
      if (Platform.OS === 'ios') {
        const result = await Share.share(
          {
            title: shareContent.title,
            message: shareContent.message,
            url: shareContent.url,
          },
          {
            dialogTitle: options?.dialogTitle || 'Share Alumni Profile',
            excludedActivityTypes: options?.excludedActivityTypes,
            tintColor: options?.tintColor,
          }
        );
        
        return { success: !result.dismissedAction };
      } else {
        await Share.share({
          title: shareContent.title,
          message: shareContent.message,
        });
        
        return { success: true };
      }
    } catch (error) {
      console.error('Error sharing alumni profile:', error);
      return {
        success: false,
        error: 'Failed to share alumni profile'
      };
    }
  }

  /**
   * Share event
   */
  static async shareEvent(
    event: Event,
    options?: ShareOptions
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const shareContent = this.generateEventShareContent(event);
      
      if (Platform.OS === 'ios') {
        const result = await Share.share(
          {
            title: shareContent.title,
            message: shareContent.message,
            url: shareContent.url,
          },
          {
            dialogTitle: options?.dialogTitle || 'Share Event',
            excludedActivityTypes: options?.excludedActivityTypes,
            tintColor: options?.tintColor,
          }
        );
        
        return { success: !result.dismissedAction };
      } else {
        await Share.share({
          title: shareContent.title,
          message: shareContent.message,
        });
        
        return { success: true };
      }
    } catch (error) {
      console.error('Error sharing event:', error);
      return {
        success: false,
        error: 'Failed to share event'
      };
    }
  }

  /**
   * Share post/success story
   */
  static async sharePost(
    post: Post,
    options?: ShareOptions
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const shareContent = this.generatePostShareContent(post);
      
      if (Platform.OS === 'ios') {
        const result = await Share.share(
          {
            title: shareContent.title,
            message: shareContent.message,
            url: shareContent.url,
          },
          {
            dialogTitle: options?.dialogTitle || 'Share Post',
            excludedActivityTypes: options?.excludedActivityTypes,
            tintColor: options?.tintColor,
          }
        );
        
        return { success: !result.dismissedAction };
      } else {
        await Share.share({
          title: shareContent.title,
          message: shareContent.message,
        });
        
        return { success: true };
      }
    } catch (error) {
      console.error('Error sharing post:', error);
      return {
        success: false,
        error: 'Failed to share post'
      };
    }
  }

  /**
   * Share app invitation
   */
  static async shareAppInvitation(
    inviterName?: string,
    options?: ShareOptions
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const shareContent = this.generateAppInvitationContent(inviterName);
      
      if (Platform.OS === 'ios') {
        const result = await Share.share(
          {
            title: shareContent.title,
            message: shareContent.message,
            url: shareContent.url,
          },
          {
            dialogTitle: options?.dialogTitle || 'Invite to PROTEC Alumni',
            excludedActivityTypes: options?.excludedActivityTypes,
            tintColor: options?.tintColor,
          }
        );
        
        return { success: !result.dismissedAction };
      } else {
        await Share.share({
          title: shareContent.title,
          message: shareContent.message,
        });
        
        return { success: true };
      }
    } catch (error) {
      console.error('Error sharing app invitation:', error);
      return {
        success: false,
        error: 'Failed to share invitation'
      };
    }
  }

  /**
   * Share file (like CV, certificate, etc.)
   */
  static async shareFile(
    fileUri: string,
    mimeType?: string,
    options?: ShareOptions
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const isAvailable = await this.isAvailable();
      if (!isAvailable) {
        return {
          success: false,
          error: 'Sharing is not available on this device'
        };
      }

      // Check if file exists
      const fileInfo = await FileSystem.getInfoAsync(fileUri);
      if (!fileInfo.exists) {
        return {
          success: false,
          error: 'File not found'
        };
      }

      await Sharing.shareAsync(fileUri, {
        mimeType: mimeType || 'application/octet-stream',
        dialogTitle: options?.dialogTitle || 'Share File',
        UTI: mimeType,
      });

      return { success: true };
    } catch (error) {
      console.error('Error sharing file:', error);
      return {
        success: false,
        error: 'Failed to share file'
      };
    }
  }

  /**
   * Share image with text
   */
  static async shareImageWithText(
    imageUri: string,
    text: string,
    options?: ShareOptions
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const isAvailable = await this.isAvailable();
      if (!isAvailable) {
        // Fallback to text sharing
        return await this.shareText(text, options);
      }

      // Check if image exists
      const imageInfo = await FileSystem.getInfoAsync(imageUri);
      if (!imageInfo.exists) {
        // Fallback to text sharing
        return await this.shareText(text, options);
      }

      await Sharing.shareAsync(imageUri, {
        mimeType: 'image/jpeg',
        dialogTitle: options?.dialogTitle || 'Share',
      });

      return { success: true };
    } catch (error) {
      console.error('Error sharing image with text:', error);
      // Fallback to text sharing
      return await this.shareText(text, options);
    }
  }

  /**
   * Share plain text
   */
  static async shareText(
    text: string,
    options?: ShareOptions
  ): Promise<{ success: boolean; error?: string }> {
    try {
      if (Platform.OS === 'ios') {
        const result = await Share.share(
          { message: text },
          {
            dialogTitle: options?.dialogTitle || 'Share',
            excludedActivityTypes: options?.excludedActivityTypes,
            tintColor: options?.tintColor,
          }
        );
        
        return { success: !result.dismissedAction };
      } else {
        await Share.share({ message: text });
        return { success: true };
      }
    } catch (error) {
      console.error('Error sharing text:', error);
      return {
        success: false,
        error: 'Failed to share text'
      };
    }
  }

  /**
   * Generate alumni profile share content
   */
  private static generateAlumniShareContent(alumni: Alumni): ShareableContent {
    const title = `${alumni.name} - PROTEC Alumni`;
    
    let message = `Check out ${alumni.name}'s profile on the PROTEC Alumni app!\n\n`;
    
    if (alumni.currentRole && alumni.company) {
      message += `${alumni.currentRole} at ${alumni.company}\n`;
    } else if (alumni.currentRole) {
      message += `${alumni.currentRole}\n`;
    }
    
    if (alumni.graduationYear) {
      message += `Class of ${alumni.graduationYear}\n`;
    }
    
    if (alumni.programmes && alumni.programmes.length > 0) {
      message += `Programme: ${alumni.programmes.join(', ')}\n`;
    }
    
    if (alumni.city && alumni.province) {
      message += `Location: ${alumni.city}, ${alumni.province}\n`;
    }
    
    message += '\nConnect with fellow PROTEC alumni and expand your professional network!';
    
    return {
      title,
      message,
      url: `https://protec-alumni.app/profile/${alumni.id}`, // Deep link to profile
    };
  }

  /**
   * Generate event share content
   */
  private static generateEventShareContent(event: Event): ShareableContent {
    const title = `${event.title} - PROTEC Alumni Event`;
    
    let message = `Join us for "${event.title}"!\n\n`;
    message += `${event.description}\n\n`;
    
    const startDate = new Date(event.startTime);
    message += `📅 ${startDate.toLocaleDateString()} at ${startDate.toLocaleTimeString()}\n`;
    
    if (event.location) {
      if (event.location.type === 'virtual') {
        message += `🌐 Virtual Event\n`;
      } else {
        message += `📍 ${event.location.address || ''}, ${event.location.city || ''}\n`;
      }
    }
    
    if (event.organizer) {
      message += `👤 Organized by ${event.organizer.name}\n`;
    }
    
    message += '\nRSVP through the PROTEC Alumni app!';
    
    return {
      title,
      message,
      url: `https://protec-alumni.app/events/${event.id}`, // Deep link to event
    };
  }

  /**
   * Generate post share content
   */
  private static generatePostShareContent(post: Post): ShareableContent {
    const title = `${post.author.name} shared on PROTEC Alumni`;
    
    let message = `"${post.content}"\n\n`;
    message += `- ${post.author.name}`;
    
    if (post.author.currentRole) {
      message += `, ${post.author.currentRole}`;
    }
    
    if (post.author.graduationYear) {
      message += ` (Class of ${post.author.graduationYear})`;
    }
    
    message += '\n\nJoin the conversation on the PROTEC Alumni app!';
    
    return {
      title,
      message,
      url: `https://protec-alumni.app/posts/${post.id}`, // Deep link to post
    };
  }

  /**
   * Generate app invitation content
   */
  private static generateAppInvitationContent(inviterName?: string): ShareableContent {
    const title = 'Join the PROTEC Alumni Community';
    
    let message = '';
    
    if (inviterName) {
      message += `${inviterName} invited you to join the PROTEC Alumni community!\n\n`;
    }
    
    message += `🎓 Connect with fellow PROTEC graduates\n`;
    message += `💼 Discover career opportunities\n`;
    message += `📅 Stay updated on alumni events\n`;
    message += `🤝 Build your professional network\n\n`;
    message += `Download the PROTEC Alumni app and join thousands of graduates building their futures together!`;
    
    return {
      title,
      message,
      url: 'https://protec-alumni.app/download', // App download link
    };
  }

  /**
   * Show share options dialog
   */
  static async showShareDialog(
    content: ShareableContent,
    options?: ShareOptions
  ): Promise<{ success: boolean; error?: string }> {
    try {
      if (Platform.OS === 'ios') {
        const result = await Share.share(
          {
            title: content.title,
            message: content.message,
            url: content.url,
          },
          {
            dialogTitle: options?.dialogTitle || 'Share',
            excludedActivityTypes: options?.excludedActivityTypes,
            tintColor: options?.tintColor,
          }
        );
        
        return { success: !result.dismissedAction };
      } else {
        await Share.share({
          title: content.title,
          message: content.message,
        });
        
        return { success: true };
      }
    } catch (error) {
      console.error('Error showing share dialog:', error);
      return {
        success: false,
        error: 'Failed to show share dialog'
      };
    }
  }

  /**
   * Create shareable link for content
   */
  static createShareableLink(type: 'profile' | 'event' | 'post', id: string): string {
    const baseUrl = 'https://protec-alumni.app';
    
    switch (type) {
      case 'profile':
        return `${baseUrl}/profile/${id}`;
      case 'event':
        return `${baseUrl}/events/${id}`;
      case 'post':
        return `${baseUrl}/posts/${id}`;
      default:
        return baseUrl;
    }
  }

  /**
   * Generate QR code data for sharing
   */
  static generateQRCodeData(type: 'profile' | 'event' | 'post', id: string): string {
    return this.createShareableLink(type, id);
  }
}

export default SharingService;
