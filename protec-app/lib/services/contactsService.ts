import * as Contacts from 'expo-contacts';
import { Alert, Platform } from 'react-native';
import { Alumni } from '../types';

export interface AlumniContact {
  id: string;
  name: string;
  email?: string;
  phoneNumber?: string;
  company?: string;
  currentRole?: string;
  graduationYear?: number;
  photoUrl?: string;
  linkedInUrl?: string;
  notes?: string;
}

export class ContactsService {
  /**
   * Request contacts permissions
   */
  static async requestPermissions(): Promise<boolean> {
    try {
      const { status } = await Contacts.requestPermissionsAsync();
      return status === 'granted';
    } catch (error) {
      console.error('Error requesting contacts permissions:', error);
      return false;
    }
  }

  /**
   * Check if contacts permissions are granted
   */
  static async hasPermissions(): Promise<boolean> {
    try {
      const { status } = await Contacts.getPermissionsAsync();
      return status === 'granted';
    } catch (error) {
      console.error('Error checking contacts permissions:', error);
      return false;
    }
  }

  /**
   * Get all contacts from device
   */
  static async getAllContacts(): Promise<Contacts.Contact[]> {
    try {
      const hasPermission = await this.hasPermissions();
      if (!hasPermission) {
        const granted = await this.requestPermissions();
        if (!granted) {
          throw new Error('Contacts permission required');
        }
      }

      const { data } = await Contacts.getContactsAsync({
        fields: [
          Contacts.Fields.Name,
          Contacts.Fields.PhoneNumbers,
          Contacts.Fields.Emails,
          Contacts.Fields.Company,
          Contacts.Fields.JobTitle,
          Contacts.Fields.Note,
          Contacts.Fields.Image,
        ],
        sort: Contacts.SortTypes.FirstName,
      });

      return data;
    } catch (error) {
      console.error('Error getting contacts:', error);
      return [];
    }
  }

  /**
   * Search contacts by name or email
   */
  static async searchContacts(query: string): Promise<Contacts.Contact[]> {
    try {
      const hasPermission = await this.hasPermissions();
      if (!hasPermission) {
        return [];
      }

      const { data } = await Contacts.getContactsAsync({
        fields: [
          Contacts.Fields.Name,
          Contacts.Fields.PhoneNumbers,
          Contacts.Fields.Emails,
          Contacts.Fields.Company,
          Contacts.Fields.JobTitle,
        ],
        sort: Contacts.SortTypes.FirstName,
      });

      const searchTerm = query.toLowerCase();
      return data.filter(contact => 
        contact.name?.toLowerCase().includes(searchTerm) ||
        contact.emails?.some(email => email.email?.toLowerCase().includes(searchTerm)) ||
        contact.company?.toLowerCase().includes(searchTerm)
      );
    } catch (error) {
      console.error('Error searching contacts:', error);
      return [];
    }
  }

  /**
   * Add alumni to device contacts
   */
  static async addAlumniToContacts(alumni: Alumni): Promise<{ success: boolean; contactId?: string; error?: string }> {
    try {
      const hasPermission = await this.hasPermissions();
      if (!hasPermission) {
        const granted = await this.requestPermissions();
        if (!granted) {
          return {
            success: false,
            error: 'Contacts permission required to add alumni to contacts'
          };
        }
      }

      // Check if contact already exists
      const existingContacts = await this.searchContacts(alumni.email);
      const existingContact = existingContacts.find(contact => 
        contact.emails?.some(email => email.email === alumni.email)
      );

      if (existingContact) {
        return {
          success: false,
          error: 'Contact already exists in your contacts'
        };
      }

      // Prepare contact data
      const contactData: Contacts.ContactInput = {
        [Contacts.Fields.FirstName]: alumni.name.split(' ')[0] || '',
        [Contacts.Fields.LastName]: alumni.name.split(' ').slice(1).join(' ') || '',
        [Contacts.Fields.Emails]: alumni.email ? [{
          email: alumni.email,
          isPrimary: true,
          label: 'work',
        }] : [],
        [Contacts.Fields.Company]: alumni.company || '',
        [Contacts.Fields.JobTitle]: alumni.currentRole || '',
        [Contacts.Fields.Note]: this.generateAlumniNote(alumni),
      };

      // Add phone number if available
      if (alumni.phoneNumber) {
        contactData[Contacts.Fields.PhoneNumbers] = [{
          number: alumni.phoneNumber,
          isPrimary: true,
          label: 'mobile',
        }];
      }

      const contactId = await Contacts.addContactAsync(contactData);
      
      return {
        success: true,
        contactId
      };
    } catch (error) {
      console.error('Error adding alumni to contacts:', error);
      return {
        success: false,
        error: 'Failed to add alumni to contacts'
      };
    }
  }

  /**
   * Generate note for alumni contact
   */
  private static generateAlumniNote(alumni: Alumni): string {
    const notes = ['PROTEC Alumni'];
    
    if (alumni.graduationYear) {
      notes.push(`Class of ${alumni.graduationYear}`);
    }
    
    if (alumni.programmes && alumni.programmes.length > 0) {
      notes.push(`Programme: ${alumni.programmes.join(', ')}`);
    }
    
    if (alumni.industry) {
      notes.push(`Industry: ${alumni.industry}`);
    }
    
    if (alumni.city && alumni.province) {
      notes.push(`Location: ${alumni.city}, ${alumni.province}`);
    }
    
    if (alumni.skills && alumni.skills.length > 0) {
      notes.push(`Skills: ${alumni.skills.slice(0, 5).join(', ')}`);
    }
    
    if (alumni.linkedInUrl) {
      notes.push(`LinkedIn: ${alumni.linkedInUrl}`);
    }
    
    return notes.join('\n');
  }

  /**
   * Export multiple alumni to contacts
   */
  static async exportAlumniToContacts(
    alumniList: Alumni[],
    onProgress?: (current: number, total: number) => void
  ): Promise<{ success: number; failed: number; errors: string[] }> {
    const results = {
      success: 0,
      failed: 0,
      errors: [] as string[]
    };

    for (let i = 0; i < alumniList.length; i++) {
      const alumni = alumniList[i];
      
      try {
        const result = await this.addAlumniToContacts(alumni);
        
        if (result.success) {
          results.success++;
        } else {
          results.failed++;
          results.errors.push(`${alumni.name}: ${result.error}`);
        }
      } catch (error) {
        results.failed++;
        results.errors.push(`${alumni.name}: Failed to add contact`);
      }
      
      // Call progress callback
      if (onProgress) {
        onProgress(i + 1, alumniList.length);
      }
      
      // Small delay to prevent overwhelming the system
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    return results;
  }

  /**
   * Find PROTEC alumni in device contacts
   */
  static async findProtecAlumniInContacts(): Promise<Contacts.Contact[]> {
    try {
      const allContacts = await this.getAllContacts();
      
      return allContacts.filter(contact => 
        contact.note?.toLowerCase().includes('protec') ||
        contact.company?.toLowerCase().includes('protec') ||
        contact.emails?.some(email => 
          email.email?.includes('protec.co.za') ||
          email.email?.includes('protec.edu')
        )
      );
    } catch (error) {
      console.error('Error finding PROTEC alumni in contacts:', error);
      return [];
    }
  }

  /**
   * Show contact selection dialog and add alumni
   */
  static async showAddToContactsDialog(alumni: Alumni): Promise<boolean> {
    return new Promise((resolve) => {
      Alert.alert(
        'Add to Contacts',
        `Add ${alumni.name} to your device contacts?`,
        [
          {
            text: 'Cancel',
            style: 'cancel',
            onPress: () => resolve(false)
          },
          {
            text: 'Add Contact',
            onPress: async () => {
              const result = await this.addAlumniToContacts(alumni);
              
              if (result.success) {
                Alert.alert(
                  'Success',
                  `${alumni.name} has been added to your contacts.`,
                  [{ text: 'OK' }]
                );
                resolve(true);
              } else {
                Alert.alert(
                  'Error',
                  result.error || 'Failed to add contact. Please try again.',
                  [{ text: 'OK' }]
                );
                resolve(false);
              }
            }
          }
        ]
      );
    });
  }

  /**
   * Show bulk export dialog
   */
  static async showBulkExportDialog(
    alumniList: Alumni[],
    onProgress?: (current: number, total: number) => void
  ): Promise<boolean> {
    return new Promise((resolve) => {
      Alert.alert(
        'Export to Contacts',
        `Export ${alumniList.length} alumni to your device contacts?`,
        [
          {
            text: 'Cancel',
            style: 'cancel',
            onPress: () => resolve(false)
          },
          {
            text: 'Export All',
            onPress: async () => {
              const results = await this.exportAlumniToContacts(alumniList, onProgress);
              
              Alert.alert(
                'Export Complete',
                `Successfully exported ${results.success} contacts.\n${results.failed > 0 ? `Failed: ${results.failed}` : ''}`,
                [{ text: 'OK' }]
              );
              
              resolve(results.success > 0);
            }
          }
        ]
      );
    });
  }

  /**
   * Get contact by ID
   */
  static async getContactById(contactId: string): Promise<Contacts.Contact | null> {
    try {
      const hasPermission = await this.hasPermissions();
      if (!hasPermission) {
        return null;
      }

      const contact = await Contacts.getContactByIdAsync(contactId, {
        fields: [
          Contacts.Fields.Name,
          Contacts.Fields.PhoneNumbers,
          Contacts.Fields.Emails,
          Contacts.Fields.Company,
          Contacts.Fields.JobTitle,
          Contacts.Fields.Note,
          Contacts.Fields.Image,
        ],
      });

      return contact;
    } catch (error) {
      console.error('Error getting contact by ID:', error);
      return null;
    }
  }

  /**
   * Update existing contact with alumni information
   */
  static async updateContactWithAlumniInfo(
    contactId: string,
    alumni: Alumni
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const hasPermission = await this.hasPermissions();
      if (!hasPermission) {
        return {
          success: false,
          error: 'Contacts permission required'
        };
      }

      const existingContact = await this.getContactById(contactId);
      if (!existingContact) {
        return {
          success: false,
          error: 'Contact not found'
        };
      }

      // Update contact with alumni information
      const updatedNote = existingContact.note 
        ? `${existingContact.note}\n\n${this.generateAlumniNote(alumni)}`
        : this.generateAlumniNote(alumni);

      const updateData: Contacts.ContactInput = {
        [Contacts.Fields.Company]: alumni.company || existingContact.company,
        [Contacts.Fields.JobTitle]: alumni.currentRole || existingContact.jobTitle,
        [Contacts.Fields.Note]: updatedNote,
      };

      await Contacts.updateContactAsync(contactId, updateData);
      
      return { success: true };
    } catch (error) {
      console.error('Error updating contact:', error);
      return {
        success: false,
        error: 'Failed to update contact'
      };
    }
  }
}

export default ContactsService;
