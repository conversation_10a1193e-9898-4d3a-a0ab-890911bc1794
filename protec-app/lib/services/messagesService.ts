import { trpcClient } from '../api/trpc';
import { Conversation, Message } from '../types';

export interface MessageFilters {
  conversationId?: string;
  senderId?: string;
  dateFrom?: Date;
  dateTo?: Date;
  hasAttachments?: boolean;
}

export interface MessageSearchParams {
  query?: string;
  filters?: MessageFilters;
  page?: number;
  limit?: number;
  sortBy?: 'date' | 'sender';
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedMessagesResponse {
  messages: Message[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

export interface CreateConversationData {
  participantIds: string[];
  title?: string;
  isGroup?: boolean;
}

export interface SendMessageData {
  conversationId: string;
  content: string;
  attachments?: Array<{
    type: 'image' | 'document' | 'audio';
    url: string;
    name: string;
    size: number;
  }>;
  replyToId?: string;
}

export class MessagesService {
  /**
   * Get all conversations for the current user
   */
  static async getConversations(): Promise<Conversation[]> {
    try {
      const conversations = await trpcClient.messages.getConversations.query();
      return conversations;
    } catch (error) {
      console.error('Failed to fetch conversations:', error);
      throw new Error('Failed to fetch conversations');
    }
  }

  /**
   * Get messages for a specific conversation
   */
  static async getMessages(conversationId: string, params: MessageSearchParams = {}): Promise<PaginatedMessagesResponse> {
    try {
      const response = await trpcClient.messages.getMessages.query({
        conversationId,
        query: params.query,
        filters: params.filters,
        page: params.page || 1,
        limit: params.limit || 50,
        sortBy: params.sortBy || 'date',
        sortOrder: params.sortOrder || 'asc',
      });

      return response;
    } catch (error) {
      console.error('Failed to fetch messages:', error);
      throw new Error('Failed to fetch messages');
    }
  }

  /**
   * Send a message
   */
  static async sendMessage(data: SendMessageData): Promise<Message> {
    try {
      const message = await trpcClient.messages.sendMessage.mutate(data);
      return message;
    } catch (error) {
      console.error('Failed to send message:', error);
      throw new Error('Failed to send message');
    }
  }

  /**
   * Create a new conversation
   */
  static async createConversation(data: CreateConversationData): Promise<Conversation> {
    try {
      const conversation = await trpcClient.messages.createConversation.mutate(data);
      return conversation;
    } catch (error) {
      console.error('Failed to create conversation:', error);
      throw new Error('Failed to create conversation');
    }
  }

  /**
   * Mark messages as read
   */
  static async markAsRead(conversationId: string, messageIds?: string[]): Promise<void> {
    try {
      await trpcClient.messages.markAsRead.mutate({
        conversationId,
        messageIds,
      });
    } catch (error) {
      console.error('Failed to mark messages as read:', error);
      throw new Error('Failed to mark messages as read');
    }
  }

  /**
   * Search messages across all conversations
   */
  static async searchMessages(query: string, filters?: MessageFilters): Promise<Message[]> {
    try {
      const response = await trpcClient.messages.getMessages.query({
        query,
        filters,
        limit: 100,
      });

      return response.messages;
    } catch (error) {
      console.error('Failed to search messages:', error);
      throw new Error('Failed to search messages');
    }
  }

  /**
   * Get unread message count
   */
  static async getUnreadCount(): Promise<number> {
    try {
      const conversations = await trpcClient.messages.getConversations.query();
      return conversations.reduce((total, conv) => total + (conv.unreadCount || 0), 0);
    } catch (error) {
      console.error('Failed to get unread count:', error);
      return 0;
    }
  }

  /**
   * Get conversation by ID
   */
  static async getConversationById(id: string): Promise<Conversation> {
    try {
      const conversations = await trpcClient.messages.getConversations.query();
      const conversation = conversations.find(conv => conv.id === id);
      
      if (!conversation) {
        throw new Error('Conversation not found');
      }

      return conversation;
    } catch (error) {
      console.error('Failed to fetch conversation:', error);
      throw new Error('Failed to fetch conversation');
    }
  }

  /**
   * Delete a message
   */
  static async deleteMessage(messageId: string): Promise<void> {
    try {
      // This would need to be implemented in the backend
      console.log('Deleting message:', messageId);
    } catch (error) {
      console.error('Failed to delete message:', error);
      throw new Error('Failed to delete message');
    }
  }

  /**
   * Edit a message
   */
  static async editMessage(messageId: string, newContent: string): Promise<Message> {
    try {
      // This would need to be implemented in the backend
      throw new Error('Edit message not implemented');
    } catch (error) {
      console.error('Failed to edit message:', error);
      throw new Error('Failed to edit message');
    }
  }

  /**
   * Add participants to a group conversation
   */
  static async addParticipants(conversationId: string, participantIds: string[]): Promise<void> {
    try {
      // This would need to be implemented in the backend
      console.log('Adding participants to conversation:', conversationId, participantIds);
    } catch (error) {
      console.error('Failed to add participants:', error);
      throw new Error('Failed to add participants');
    }
  }

  /**
   * Remove participants from a group conversation
   */
  static async removeParticipants(conversationId: string, participantIds: string[]): Promise<void> {
    try {
      // This would need to be implemented in the backend
      console.log('Removing participants from conversation:', conversationId, participantIds);
    } catch (error) {
      console.error('Failed to remove participants:', error);
      throw new Error('Failed to remove participants');
    }
  }

  /**
   * Leave a group conversation
   */
  static async leaveConversation(conversationId: string): Promise<void> {
    try {
      // This would need to be implemented in the backend
      console.log('Leaving conversation:', conversationId);
    } catch (error) {
      console.error('Failed to leave conversation:', error);
      throw new Error('Failed to leave conversation');
    }
  }

  /**
   * Update conversation settings (title, etc.)
   */
  static async updateConversation(conversationId: string, updates: Partial<Conversation>): Promise<Conversation> {
    try {
      // This would need to be implemented in the backend
      throw new Error('Update conversation not implemented');
    } catch (error) {
      console.error('Failed to update conversation:', error);
      throw new Error('Failed to update conversation');
    }
  }

  /**
   * Get conversation participants
   */
  static async getParticipants(conversationId: string): Promise<any[]> {
    try {
      const conversation = await this.getConversationById(conversationId);
      return conversation.participants || [];
    } catch (error) {
      console.error('Failed to get participants:', error);
      throw new Error('Failed to get participants');
    }
  }
}

export const messagesService = MessagesService;
