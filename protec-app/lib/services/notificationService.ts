import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import { Platform } from 'react-native';
import Constants from 'expo-constants';

export interface NotificationData {
  type: 'message' | 'event' | 'connection' | 'donation' | 'post' | 'general';
  id: string;
  title: string;
  body: string;
  data?: Record<string, any>;
}

export interface NotificationPreferences {
  messages: boolean;
  events: boolean;
  connections: boolean;
  donations: boolean;
  posts: boolean;
  general: boolean;
  sound: boolean;
  vibration: boolean;
}

class NotificationService {
  private expoPushToken: string | null = null;
  private notificationListener: any = null;
  private responseListener: any = null;

  constructor() {
    this.setupNotificationHandler();
  }

  // Configure how notifications are handled when app is in foreground
  private setupNotificationHandler() {
    Notifications.setNotificationHandler({
      handleNotification: async () => ({
        shouldShowAlert: true,
        shouldPlaySound: true,
        shouldSetBadge: true,
        shouldShowBanner: true,
        shouldShowList: true,
      }),
    });
  }

  // Initialize push notifications
  async initialize(): Promise<string | null> {
    try {
      if (!Device.isDevice) {
        console.warn('Push notifications only work on physical devices');
        return null;
      }

      // Request permissions
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        console.warn('Failed to get push token for push notification!');
        return null;
      }

      // Get the push token
      const token = await Notifications.getExpoPushTokenAsync({
        projectId: Constants.expoConfig?.extra?.eas?.projectId,
      });

      this.expoPushToken = token.data;

      // Configure notification channel for Android
      if (Platform.OS === 'android') {
        await Notifications.setNotificationChannelAsync('default', {
          name: 'PROTEC Alumni',
          importance: Notifications.AndroidImportance.MAX,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#012A5B',
          sound: 'default',
        });

        // Create specific channels for different notification types
        await this.createNotificationChannels();
      }

      // Set up notification listeners
      this.setupNotificationListeners();

      return this.expoPushToken;
    } catch (error) {
      console.error('Error initializing notifications:', error);
      return null;
    }
  }

  // Create notification channels for Android
  private async createNotificationChannels() {
    const channels = [
      {
        id: 'messages',
        name: 'Messages',
        description: 'New messages from alumni',
        importance: Notifications.AndroidImportance.HIGH,
      },
      {
        id: 'events',
        name: 'Events',
        description: 'Event reminders and updates',
        importance: Notifications.AndroidImportance.DEFAULT,
      },
      {
        id: 'connections',
        name: 'Connections',
        description: 'New connection requests',
        importance: Notifications.AndroidImportance.DEFAULT,
      },
      {
        id: 'donations',
        name: 'Donations',
        description: 'Donation campaigns and updates',
        importance: Notifications.AndroidImportance.LOW,
      },
      {
        id: 'posts',
        name: 'Posts',
        description: 'New posts and interactions',
        importance: Notifications.AndroidImportance.LOW,
      },
    ];

    for (const channel of channels) {
      await Notifications.setNotificationChannelAsync(channel.id, {
        name: channel.name,
        description: channel.description,
        importance: channel.importance,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#012A5B',
        sound: 'default',
      });
    }
  }

  // Set up notification event listeners
  private setupNotificationListeners() {
    // Listener for notifications received while app is running
    this.notificationListener = Notifications.addNotificationReceivedListener(
      (notification) => {
        console.log('Notification received:', notification);
        this.handleNotificationReceived(notification);
      }
    );

    // Listener for when user taps on notification
    this.responseListener = Notifications.addNotificationResponseReceivedListener(
      (response) => {
        console.log('Notification response:', response);
        this.handleNotificationResponse(response);
      }
    );
  }

  // Handle notification received while app is running
  private handleNotificationReceived(notification: Notifications.Notification) {
    const { type, id } = notification.request.content.data || {};
    
    // Update app state based on notification type
    switch (type) {
      case 'message':
        // Update message store with new message indicator
        break;
      case 'event':
        // Update events store
        break;
      case 'connection':
        // Update connections count
        break;
      default:
        break;
    }
  }

  // Handle notification tap/response
  private handleNotificationResponse(response: Notifications.NotificationResponse) {
    const { type, id } = response.notification.request.content.data || {};
    
    // Navigate to appropriate screen based on notification type
    switch (type) {
      case 'message':
        // Navigate to message thread
        // router.push(`/messages/thread/${id}`);
        break;
      case 'event':
        // Navigate to event details
        // router.push(`/events/${id}`);
        break;
      case 'connection':
        // Navigate to connections screen
        // router.push('/connections');
        break;
      case 'donation':
        // Navigate to donations screen
        // router.push('/(tabs)/donations');
        break;
      case 'post':
        // Navigate to post details
        // router.push(`/posts/${id}`);
        break;
      default:
        // Navigate to dashboard
        // router.push('/(tabs)/');
        break;
    }
  }

  // Schedule a local notification
  async scheduleLocalNotification(notificationData: NotificationData, trigger?: Notifications.NotificationTriggerInput) {
    try {
      const { type, title, body, data } = notificationData;
      
      await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data: { type, ...data },
          sound: 'default',
          priority: Notifications.AndroidNotificationPriority.HIGH,
          categoryIdentifier: type,
        },
        trigger: trigger || null, // null means immediate
      });
    } catch (error) {
      console.error('Error scheduling local notification:', error);
    }
  }

  // Send push notification (for testing purposes)
  async sendTestNotification(notificationData: NotificationData) {
    if (!this.expoPushToken) {
      console.warn('No push token available');
      return;
    }

    try {
      const message = {
        to: this.expoPushToken,
        sound: 'default',
        title: notificationData.title,
        body: notificationData.body,
        data: { type: notificationData.type, id: notificationData.id, ...notificationData.data },
        priority: 'high' as const,
        channelId: notificationData.type,
      };

      // In a real app, this would be sent from your backend
      // For testing, we can use Expo's push service directly
      const response = await fetch('https://exp.host/--/api/v2/push/send', {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Accept-encoding': 'gzip, deflate',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(message),
      });

      const result = await response.json();
      console.log('Push notification sent:', result);
    } catch (error) {
      console.error('Error sending push notification:', error);
    }
  }

  // Get current notification permissions
  async getPermissions(): Promise<Notifications.NotificationPermissionsStatus> {
    return await Notifications.getPermissionsAsync();
  }

  // Request notification permissions
  async requestPermissions(): Promise<Notifications.NotificationPermissionsStatus> {
    return await Notifications.requestPermissionsAsync();
  }

  // Get the current push token
  getPushToken(): string | null {
    return this.expoPushToken;
  }

  // Set notification badge count
  async setBadgeCount(count: number) {
    try {
      await Notifications.setBadgeCountAsync(count);
    } catch (error) {
      console.error('Error setting badge count:', error);
    }
  }

  // Clear all notifications
  async clearAllNotifications() {
    try {
      await Notifications.dismissAllNotificationsAsync();
      await this.setBadgeCount(0);
    } catch (error) {
      console.error('Error clearing notifications:', error);
    }
  }

  // Cleanup listeners
  cleanup() {
    if (this.notificationListener) {
      Notifications.removeNotificationSubscription(this.notificationListener);
    }
    if (this.responseListener) {
      Notifications.removeNotificationSubscription(this.responseListener);
    }
  }
}

// Export singleton instance
export const notificationService = new NotificationService();
