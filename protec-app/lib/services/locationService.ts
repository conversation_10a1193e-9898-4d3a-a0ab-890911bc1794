import * as Location from 'expo-location';
import { Alert, Platform } from 'react-native';

export interface LocationCoordinates {
  latitude: number;
  longitude: number;
  accuracy?: number;
  altitude?: number;
  heading?: number;
  speed?: number;
}

export interface LocationAddress {
  street?: string;
  city?: string;
  region?: string; // Province/State
  country?: string;
  postalCode?: string;
  name?: string; // Place name
}

export interface LocationSearchResult {
  coordinates: LocationCoordinates;
  address: LocationAddress;
  distance?: number; // Distance from user's location in km
}

export class LocationService {
  private static currentLocation: LocationCoordinates | null = null;
  private static watchId: Location.LocationSubscription | null = null;

  /**
   * Request location permissions
   */
  static async requestPermissions(): Promise<{
    foreground: boolean;
    background: boolean;
  }> {
    try {
      // Request foreground permissions
      const { status: foregroundStatus } = await Location.requestForegroundPermissionsAsync();
      
      // Request background permissions (optional)
      let backgroundStatus = Location.PermissionStatus.DENIED;
      if (foregroundStatus === Location.PermissionStatus.GRANTED) {
        const { status } = await Location.requestBackgroundPermissionsAsync();
        backgroundStatus = status;
      }

      return {
        foreground: foregroundStatus === Location.PermissionStatus.GRANTED,
        background: backgroundStatus === Location.PermissionStatus.GRANTED,
      };
    } catch (error) {
      console.error('Error requesting location permissions:', error);
      return { foreground: false, background: false };
    }
  }

  /**
   * Check if location permissions are granted
   */
  static async hasPermissions(): Promise<{
    foreground: boolean;
    background: boolean;
  }> {
    try {
      const { status: foregroundStatus } = await Location.getForegroundPermissionsAsync();
      const { status: backgroundStatus } = await Location.getBackgroundPermissionsAsync();

      return {
        foreground: foregroundStatus === Location.PermissionStatus.GRANTED,
        background: backgroundStatus === Location.PermissionStatus.GRANTED,
      };
    } catch (error) {
      console.error('Error checking location permissions:', error);
      return { foreground: false, background: false };
    }
  }

  /**
   * Get current location
   */
  static async getCurrentLocation(options?: {
    accuracy?: Location.Accuracy;
    timeout?: number;
    maximumAge?: number;
  }): Promise<LocationCoordinates | null> {
    try {
      const permissions = await this.hasPermissions();
      if (!permissions.foreground) {
        const granted = await this.requestPermissions();
        if (!granted.foreground) {
          throw new Error('Location permission required');
        }
      }

      const location = await Location.getCurrentPositionAsync({
        accuracy: options?.accuracy || Location.Accuracy.Balanced,
        timeInterval: options?.timeout || 10000,
        distanceInterval: 0,
      });

      const coordinates: LocationCoordinates = {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        accuracy: location.coords.accuracy || undefined,
        altitude: location.coords.altitude || undefined,
        heading: location.coords.heading || undefined,
        speed: location.coords.speed || undefined,
      };

      this.currentLocation = coordinates;
      return coordinates;
    } catch (error) {
      console.error('Error getting current location:', error);
      return null;
    }
  }

  /**
   * Get cached current location
   */
  static getCachedLocation(): LocationCoordinates | null {
    return this.currentLocation;
  }

  /**
   * Start watching location changes
   */
  static async startLocationWatch(
    callback: (location: LocationCoordinates) => void,
    options?: {
      accuracy?: Location.Accuracy;
      timeInterval?: number;
      distanceInterval?: number;
    }
  ): Promise<boolean> {
    try {
      const permissions = await this.hasPermissions();
      if (!permissions.foreground) {
        return false;
      }

      // Stop existing watch if any
      if (this.watchId) {
        this.stopLocationWatch();
      }

      this.watchId = await Location.watchPositionAsync(
        {
          accuracy: options?.accuracy || Location.Accuracy.Balanced,
          timeInterval: options?.timeInterval || 5000,
          distanceInterval: options?.distanceInterval || 10,
        },
        (location) => {
          const coordinates: LocationCoordinates = {
            latitude: location.coords.latitude,
            longitude: location.coords.longitude,
            accuracy: location.coords.accuracy || undefined,
            altitude: location.coords.altitude || undefined,
            heading: location.coords.heading || undefined,
            speed: location.coords.speed || undefined,
          };

          this.currentLocation = coordinates;
          callback(coordinates);
        }
      );

      return true;
    } catch (error) {
      console.error('Error starting location watch:', error);
      return false;
    }
  }

  /**
   * Stop watching location changes
   */
  static stopLocationWatch(): void {
    if (this.watchId) {
      this.watchId.remove();
      this.watchId = null;
    }
  }

  /**
   * Reverse geocode coordinates to address
   */
  static async reverseGeocode(coordinates: LocationCoordinates): Promise<LocationAddress | null> {
    try {
      const addresses = await Location.reverseGeocodeAsync({
        latitude: coordinates.latitude,
        longitude: coordinates.longitude,
      });

      if (addresses.length === 0) {
        return null;
      }

      const address = addresses[0];
      return {
        street: address.street || undefined,
        city: address.city || undefined,
        region: address.region || undefined,
        country: address.country || undefined,
        postalCode: address.postalCode || undefined,
        name: address.name || undefined,
      };
    } catch (error) {
      console.error('Error reverse geocoding:', error);
      return null;
    }
  }

  /**
   * Forward geocode address to coordinates
   */
  static async geocode(address: string): Promise<LocationCoordinates[]> {
    try {
      const locations = await Location.geocodeAsync(address);
      
      return locations.map(location => ({
        latitude: location.latitude,
        longitude: location.longitude,
        accuracy: location.accuracy || undefined,
      }));
    } catch (error) {
      console.error('Error geocoding address:', error);
      return [];
    }
  }

  /**
   * Calculate distance between two coordinates (in kilometers)
   */
  static calculateDistance(
    coord1: LocationCoordinates,
    coord2: LocationCoordinates
  ): number {
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(coord2.latitude - coord1.latitude);
    const dLon = this.toRadians(coord2.longitude - coord1.longitude);
    
    const a = 
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(coord1.latitude)) * 
      Math.cos(this.toRadians(coord2.latitude)) * 
      Math.sin(dLon / 2) * Math.sin(dLon / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;
    
    return Math.round(distance * 100) / 100; // Round to 2 decimal places
  }

  /**
   * Convert degrees to radians
   */
  private static toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  /**
   * Find nearby alumni based on location
   */
  static async findNearbyAlumni(
    alumni: any[],
    maxDistance: number = 50, // km
    userLocation?: LocationCoordinates
  ): Promise<Array<any & { distance: number }>> {
    try {
      const currentLocation = userLocation || await this.getCurrentLocation();
      if (!currentLocation) {
        return [];
      }

      const nearbyAlumni = alumni
        .map(alumnus => {
          // Try to get coordinates from alumni data
          let alumniCoords: LocationCoordinates | null = null;
          
          if (alumnus.coordinates) {
            alumniCoords = alumnus.coordinates;
          } else if (alumnus.city && alumnus.province) {
            // We would need to geocode this, but for now return null
            // In a real implementation, you might cache geocoded results
            return null;
          }

          if (!alumniCoords) {
            return null;
          }

          const distance = this.calculateDistance(currentLocation, alumniCoords);
          
          if (distance <= maxDistance) {
            return {
              ...alumnus,
              distance,
            };
          }
          
          return null;
        })
        .filter(Boolean)
        .sort((a, b) => a!.distance - b!.distance);

      return nearbyAlumni as Array<any & { distance: number }>;
    } catch (error) {
      console.error('Error finding nearby alumni:', error);
      return [];
    }
  }

  /**
   * Find nearby events based on location
   */
  static async findNearbyEvents(
    events: any[],
    maxDistance: number = 100, // km
    userLocation?: LocationCoordinates
  ): Promise<Array<any & { distance: number }>> {
    try {
      const currentLocation = userLocation || await this.getCurrentLocation();
      if (!currentLocation) {
        return [];
      }

      const nearbyEvents = events
        .map(event => {
          // Try to get coordinates from event location
          let eventCoords: LocationCoordinates | null = null;
          
          if (event.location?.coordinates) {
            eventCoords = event.location.coordinates;
          } else if (event.location?.address) {
            // We would need to geocode this
            return null;
          }

          if (!eventCoords) {
            return null;
          }

          const distance = this.calculateDistance(currentLocation, eventCoords);
          
          if (distance <= maxDistance) {
            return {
              ...event,
              distance,
            };
          }
          
          return null;
        })
        .filter(Boolean)
        .sort((a, b) => a!.distance - b!.distance);

      return nearbyEvents as Array<any & { distance: number }>;
    } catch (error) {
      console.error('Error finding nearby events:', error);
      return [];
    }
  }

  /**
   * Get location-based search suggestions
   */
  static async getLocationSuggestions(query: string): Promise<LocationSearchResult[]> {
    try {
      if (query.length < 3) {
        return [];
      }

      const coordinates = await this.geocode(query);
      const currentLocation = this.getCachedLocation();
      
      const results: LocationSearchResult[] = [];
      
      for (const coord of coordinates) {
        const address = await this.reverseGeocode(coord);
        
        const result: LocationSearchResult = {
          coordinates: coord,
          address: address || {},
        };

        // Add distance if we have current location
        if (currentLocation) {
          result.distance = this.calculateDistance(currentLocation, coord);
        }

        results.push(result);
      }

      // Sort by distance if available
      if (currentLocation) {
        results.sort((a, b) => (a.distance || 0) - (b.distance || 0));
      }

      return results.slice(0, 10); // Limit to 10 results
    } catch (error) {
      console.error('Error getting location suggestions:', error);
      return [];
    }
  }

  /**
   * Show location permission dialog
   */
  static async showLocationPermissionDialog(): Promise<boolean> {
    return new Promise((resolve) => {
      Alert.alert(
        'Location Permission',
        'This app needs location access to find nearby alumni and events. Your location data is kept private and secure.',
        [
          {
            text: 'Not Now',
            style: 'cancel',
            onPress: () => resolve(false)
          },
          {
            text: 'Allow Location',
            onPress: async () => {
              const permissions = await this.requestPermissions();
              resolve(permissions.foreground);
            }
          }
        ]
      );
    });
  }

  /**
   * Format distance for display
   */
  static formatDistance(distance: number): string {
    if (distance < 1) {
      return `${Math.round(distance * 1000)}m away`;
    } else if (distance < 10) {
      return `${distance.toFixed(1)}km away`;
    } else {
      return `${Math.round(distance)}km away`;
    }
  }

  /**
   * Get location display name
   */
  static getLocationDisplayName(address: LocationAddress): string {
    const parts = [];
    
    if (address.name) {
      parts.push(address.name);
    }
    
    if (address.city) {
      parts.push(address.city);
    }
    
    if (address.region && address.region !== address.city) {
      parts.push(address.region);
    }
    
    if (address.country && address.country !== 'South Africa') {
      parts.push(address.country);
    }
    
    return parts.join(', ') || 'Unknown location';
  }

  /**
   * Check if location services are enabled
   */
  static async isLocationEnabled(): Promise<boolean> {
    try {
      return await Location.hasServicesEnabledAsync();
    } catch (error) {
      console.error('Error checking location services:', error);
      return false;
    }
  }
}

export default LocationService;
