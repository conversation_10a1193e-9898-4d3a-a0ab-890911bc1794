import { trpcClient } from '../api/trpc';
import { Event } from '../types';

export interface EventFilters {
  type?: string;
  location?: string;
  startDate?: Date;
  endDate?: Date;
  isVirtual?: boolean;
  category?: string;
}

export interface EventSearchParams {
  query?: string;
  filters?: EventFilters;
  page?: number;
  limit?: number;
  sortBy?: 'date' | 'title' | 'attendees';
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedEventsResponse {
  events: Event[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

export interface CreateEventData {
  title: string;
  description: string;
  startDate: Date;
  endDate: Date;
  location?: string;
  isVirtual: boolean;
  virtualLink?: string;
  maxAttendees?: number;
  category: string;
  tags?: string[];
  requiresApproval?: boolean;
  isPrivate?: boolean;
}

export class EventsService {
  /**
   * Fetch all events with optional filtering and pagination
   */
  static async getEvents(params: EventSearchParams = {}): Promise<PaginatedEventsResponse> {
    try {
      const response = await trpcClient.events.getAll.query({
        query: params.query,
        filters: params.filters,
        page: params.page || 1,
        limit: params.limit || 20,
        sortBy: params.sortBy || 'date',
        sortOrder: params.sortOrder || 'asc',
      });

      return response;
    } catch (error) {
      console.error('Failed to fetch events:', error);
      throw new Error('Failed to fetch events');
    }
  }

  /**
   * Get event by ID
   */
  static async getEventById(id: string): Promise<Event> {
    try {
      const event = await trpcClient.events.getById.query({ id });
      return event;
    } catch (error) {
      console.error('Failed to fetch event by ID:', error);
      throw new Error('Failed to fetch event details');
    }
  }

  /**
   * Create a new event
   */
  static async createEvent(data: CreateEventData): Promise<Event> {
    try {
      const event = await trpcClient.events.create.mutate(data);
      return event;
    } catch (error) {
      console.error('Failed to create event:', error);
      throw new Error('Failed to create event');
    }
  }

  /**
   * Update an existing event
   */
  static async updateEvent(id: string, data: Partial<CreateEventData>): Promise<Event> {
    try {
      const event = await trpcClient.events.update.mutate({ id, ...data });
      return event;
    } catch (error) {
      console.error('Failed to update event:', error);
      throw new Error('Failed to update event');
    }
  }

  /**
   * Delete an event
   */
  static async deleteEvent(id: string): Promise<void> {
    try {
      await trpcClient.events.delete.mutate({ id });
    } catch (error) {
      console.error('Failed to delete event:', error);
      throw new Error('Failed to delete event');
    }
  }

  /**
   * RSVP to an event
   */
  static async rsvpToEvent(eventId: string, status: 'attending' | 'not_attending' | 'maybe'): Promise<void> {
    try {
      await trpcClient.events.rsvp.mutate({ eventId, status });
    } catch (error) {
      console.error('Failed to RSVP to event:', error);
      throw new Error('Failed to RSVP to event');
    }
  }

  /**
   * Cancel RSVP to an event
   */
  static async cancelRsvp(eventId: string): Promise<void> {
    try {
      await trpcClient.events.cancelRsvp.mutate({ eventId });
    } catch (error) {
      console.error('Failed to cancel RSVP:', error);
      throw new Error('Failed to cancel RSVP');
    }
  }

  /**
   * Get events the current user has RSVP'd to
   */
  static async getMyEvents(): Promise<Event[]> {
    try {
      const events = await trpcClient.events.getMyEvents.query();
      return events;
    } catch (error) {
      console.error('Failed to fetch my events:', error);
      throw new Error('Failed to fetch my events');
    }
  }

  /**
   * Get upcoming events
   */
  static async getUpcomingEvents(limit?: number): Promise<Event[]> {
    try {
      const events = await trpcClient.events.getUpcoming.query({ limit });
      return events;
    } catch (error) {
      console.error('Failed to fetch upcoming events:', error);
      throw new Error('Failed to fetch upcoming events');
    }
  }

  /**
   * Search events
   */
  static async searchEvents(query: string, filters?: EventFilters): Promise<Event[]> {
    try {
      const response = await trpcClient.events.getAll.query({
        query,
        filters,
        limit: 50,
      });

      return response.events;
    } catch (error) {
      console.error('Failed to search events:', error);
      throw new Error('Failed to search events');
    }
  }

  /**
   * Get events by category
   */
  static async getEventsByCategory(category: string): Promise<Event[]> {
    try {
      const response = await trpcClient.events.getAll.query({
        filters: { category },
        limit: 50,
      });

      return response.events;
    } catch (error) {
      console.error('Failed to fetch events by category:', error);
      throw new Error('Failed to fetch events by category');
    }
  }

  /**
   * Get events by location
   */
  static async getEventsByLocation(location: string): Promise<Event[]> {
    try {
      const response = await trpcClient.events.getAll.query({
        filters: { location },
        limit: 50,
      });

      return response.events;
    } catch (error) {
      console.error('Failed to fetch events by location:', error);
      throw new Error('Failed to fetch events by location');
    }
  }

  /**
   * Get virtual events
   */
  static async getVirtualEvents(): Promise<Event[]> {
    try {
      const response = await trpcClient.events.getAll.query({
        filters: { isVirtual: true },
        limit: 50,
      });

      return response.events;
    } catch (error) {
      console.error('Failed to fetch virtual events:', error);
      throw new Error('Failed to fetch virtual events');
    }
  }
}

export const eventsService = EventsService;
