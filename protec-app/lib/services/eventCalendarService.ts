import * as Calendar from 'expo-calendar';
import { Alert, Platform } from 'react-native';

export interface EventCalendarData {
  id: string;
  title: string;
  description: string;
  startTime: Date;
  endTime: Date;
  location?: {
    type: 'physical' | 'virtual' | 'hybrid';
    address?: string;
    city?: string;
    province?: string;
    virtualLink?: string;
  };
  organizer?: {
    name: string;
    email?: string;
  };
}

export class EventCalendarService {
  /**
   * Request calendar permissions
   */
  static async requestPermissions(): Promise<boolean> {
    try {
      const { status } = await Calendar.requestCalendarPermissionsAsync();
      return status === 'granted';
    } catch (error) {
      console.error('Error requesting calendar permissions:', error);
      return false;
    }
  }

  /**
   * Check if calendar permissions are granted
   */
  static async hasPermissions(): Promise<boolean> {
    try {
      const { status } = await Calendar.getCalendarPermissionsAsync();
      return status === 'granted';
    } catch (error) {
      console.error('Error checking calendar permissions:', error);
      return false;
    }
  }

  /**
   * Get available calendars
   */
  static async getCalendars(): Promise<Calendar.Calendar[]> {
    try {
      const hasPermission = await this.hasPermissions();
      if (!hasPermission) {
        const granted = await this.requestPermissions();
        if (!granted) {
          throw new Error('Calendar permission required');
        }
      }

      const calendars = await Calendar.getCalendarsAsync(Calendar.EntityTypes.EVENT);
      return calendars.filter(calendar => 
        calendar.allowsModifications && 
        calendar.accessLevel === Calendar.CalendarAccessLevel.OWNER
      );
    } catch (error) {
      console.error('Error getting calendars:', error);
      return [];
    }
  }

  /**
   * Get default calendar for adding events
   */
  static async getDefaultCalendar(): Promise<Calendar.Calendar | null> {
    try {
      const calendars = await this.getCalendars();
      
      // Find PROTEC Alumni calendar first
      const protecCalendar = calendars.find(cal => 
        cal.title?.toLowerCase().includes('protec') || 
        cal.title?.toLowerCase().includes('alumni')
      );
      
      if (protecCalendar) {
        return protecCalendar;
      }

      // Fall back to default calendar
      const defaultCalendar = calendars.find(cal => cal.isPrimary) || calendars[0];
      return defaultCalendar || null;
    } catch (error) {
      console.error('Error getting default calendar:', error);
      return null;
    }
  }

  /**
   * Create PROTEC Alumni calendar if it doesn't exist
   */
  static async createProtecCalendar(): Promise<Calendar.Calendar | null> {
    try {
      const hasPermission = await this.hasPermissions();
      if (!hasPermission) {
        const granted = await this.requestPermissions();
        if (!granted) {
          throw new Error('Calendar permission required');
        }
      }

      // Check if calendar already exists
      const existingCalendars = await this.getCalendars();
      const existingProtecCalendar = existingCalendars.find(cal => 
        cal.title?.toLowerCase().includes('protec alumni')
      );

      if (existingProtecCalendar) {
        return existingProtecCalendar;
      }

      // Get default source for creating calendar
      const defaultSource = Platform.OS === 'ios' 
        ? await Calendar.getDefaultCalendarAsync()
        : { source: { id: undefined, name: 'Default' } };

      const calendarId = await Calendar.createCalendarAsync({
        title: 'PROTEC Alumni Events',
        color: '#012A5B', // PROTEC brand color
        entityType: Calendar.EntityTypes.EVENT,
        sourceId: Platform.OS === 'ios' ? defaultSource?.source?.id : undefined,
        source: Platform.OS === 'ios' ? defaultSource?.source : { id: undefined, name: 'Default' },
        name: 'PROTEC Alumni Events',
        ownerAccount: 'personal',
        accessLevel: Calendar.CalendarAccessLevel.OWNER,
      });

      // Get the created calendar
      const calendars = await this.getCalendars();
      return calendars.find(cal => cal.id === calendarId) || null;
    } catch (error) {
      console.error('Error creating PROTEC calendar:', error);
      return null;
    }
  }

  /**
   * Add event to calendar
   */
  static async addEventToCalendar(
    eventData: EventCalendarData,
    calendarId?: string
  ): Promise<{ success: boolean; eventId?: string; error?: string }> {
    try {
      const hasPermission = await this.hasPermissions();
      if (!hasPermission) {
        const granted = await this.requestPermissions();
        if (!granted) {
          return {
            success: false,
            error: 'Calendar permission required to add events'
          };
        }
      }

      let targetCalendarId = calendarId;
      if (!targetCalendarId) {
        const defaultCalendar = await this.getDefaultCalendar();
        if (!defaultCalendar) {
          // Try to create PROTEC calendar
          const protecCalendar = await this.createProtecCalendar();
          if (!protecCalendar) {
            return {
              success: false,
              error: 'No writable calendar found'
            };
          }
          targetCalendarId = protecCalendar.id;
        } else {
          targetCalendarId = defaultCalendar.id;
        }
      }

      // Format location string
      let locationString = '';
      if (eventData.location) {
        if (eventData.location.type === 'virtual') {
          locationString = eventData.location.virtualLink || 'Virtual Event';
        } else if (eventData.location.type === 'hybrid') {
          locationString = `${eventData.location.address || ''}, ${eventData.location.city || ''} (Hybrid Event)`.trim();
        } else {
          locationString = `${eventData.location.address || ''}, ${eventData.location.city || ''}, ${eventData.location.province || ''}`.trim();
        }
      }

      // Format notes with event details
      let notes = eventData.description;
      if (eventData.organizer) {
        notes += `\n\nOrganizer: ${eventData.organizer.name}`;
        if (eventData.organizer.email) {
          notes += ` (${eventData.organizer.email})`;
        }
      }
      notes += `\n\nEvent ID: ${eventData.id}`;

      const eventDetails = {
        title: eventData.title,
        startDate: eventData.startTime,
        endDate: eventData.endTime,
        location: locationString || undefined,
        notes,
        allDay: false,
        timeZone: 'Africa/Johannesburg',
        alarms: [
          { relativeOffset: -60 * 24 }, // 1 day before
          { relativeOffset: -60 }, // 1 hour before
        ],
        availability: Calendar.Availability.BUSY,
        status: Calendar.EventStatus.CONFIRMED,
      };

      const eventId = await Calendar.createEventAsync(targetCalendarId, eventDetails);
      
      return {
        success: true,
        eventId
      };
    } catch (error) {
      console.error('Error adding event to calendar:', error);
      return {
        success: false,
        error: 'Failed to add event to calendar'
      };
    }
  }

  /**
   * Show calendar selection dialog and add event
   */
  static async showCalendarSelectionAndAddEvent(
    eventData: EventCalendarData
  ): Promise<{ success: boolean; eventId?: string; error?: string }> {
    try {
      const calendars = await this.getCalendars();
      
      if (calendars.length === 0) {
        return {
          success: false,
          error: 'No writable calendars found'
        };
      }

      if (calendars.length === 1) {
        // Only one calendar, use it directly
        return await this.addEventToCalendar(eventData, calendars[0].id);
      }

      // Multiple calendars - show selection
      return new Promise((resolve) => {
        const calendarOptions = calendars.map(cal => ({
          text: cal.title || 'Unnamed Calendar',
          onPress: async () => {
            const result = await this.addEventToCalendar(eventData, cal.id);
            resolve(result);
          }
        }));

        calendarOptions.push({
          text: 'Cancel',
          onPress: () => resolve({ success: false, error: 'Cancelled by user' }),
          style: 'cancel' as const
        });

        Alert.alert(
          'Select Calendar',
          'Choose which calendar to add this event to:',
          calendarOptions
        );
      });
    } catch (error) {
      console.error('Error showing calendar selection:', error);
      return {
        success: false,
        error: 'Failed to show calendar selection'
      };
    }
  }

  /**
   * Remove event from calendar
   */
  static async removeEventFromCalendar(eventId: string): Promise<boolean> {
    try {
      const hasPermission = await this.hasPermissions();
      if (!hasPermission) {
        return false;
      }

      await Calendar.deleteEventAsync(eventId);
      return true;
    } catch (error) {
      console.error('Error removing event from calendar:', error);
      return false;
    }
  }

  /**
   * Check if event exists in calendar
   */
  static async eventExistsInCalendar(protecEventId: string): Promise<string | null> {
    try {
      const hasPermission = await this.hasPermissions();
      if (!hasPermission) {
        return null;
      }

      const calendars = await this.getCalendars();
      const now = new Date();
      const futureDate = new Date(now.getTime() + (365 * 24 * 60 * 60 * 1000)); // 1 year from now

      for (const calendar of calendars) {
        const events = await Calendar.getEventsAsync(
          [calendar.id],
          now,
          futureDate
        );

        const matchingEvent = events.find(event => 
          event.notes?.includes(`Event ID: ${protecEventId}`)
        );

        if (matchingEvent) {
          return matchingEvent.id;
        }
      }

      return null;
    } catch (error) {
      console.error('Error checking if event exists in calendar:', error);
      return null;
    }
  }
}

export default EventCalendarService;
