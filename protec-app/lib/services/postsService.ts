import { trpcClient } from '../api/trpc';
import { Post, Comment } from '../types';

export interface PostFilters {
  category?: string;
  author?: string;
  tags?: string[];
  dateFrom?: Date;
  dateTo?: Date;
}

export interface PostSearchParams {
  query?: string;
  filters?: PostFilters;
  page?: number;
  limit?: number;
  sortBy?: 'date' | 'likes' | 'comments';
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedPostsResponse {
  posts: Post[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

export interface CreatePostData {
  title: string;
  content: string;
  category: string;
  tags?: string[];
  images?: string[];
  isPrivate?: boolean;
  allowComments?: boolean;
}

export interface CreateCommentData {
  postId: string;
  content: string;
  parentId?: string; // For replies
}

export class PostsService {
  /**
   * Fetch all posts with optional filtering and pagination
   */
  static async getPosts(params: PostSearchParams = {}): Promise<PaginatedPostsResponse> {
    try {
      const response = await trpcClient.posts.getAll.query({
        query: params.query,
        filters: params.filters,
        page: params.page || 1,
        limit: params.limit || 20,
        sortBy: params.sortBy || 'date',
        sortOrder: params.sortOrder || 'desc',
      });

      return response;
    } catch (error) {
      console.error('Failed to fetch posts:', error);
      throw new Error('Failed to fetch posts');
    }
  }

  /**
   * Get post by ID
   */
  static async getPostById(id: string): Promise<Post> {
    try {
      const post = await trpcClient.posts.getById.query({ id });
      return post;
    } catch (error) {
      console.error('Failed to fetch post by ID:', error);
      throw new Error('Failed to fetch post details');
    }
  }

  /**
   * Create a new post
   */
  static async createPost(data: CreatePostData): Promise<Post> {
    try {
      const post = await trpcClient.posts.create.mutate(data);
      return post;
    } catch (error) {
      console.error('Failed to create post:', error);
      throw new Error('Failed to create post');
    }
  }

  /**
   * Update an existing post
   */
  static async updatePost(id: string, data: Partial<CreatePostData>): Promise<Post> {
    try {
      const post = await trpcClient.posts.update.mutate({ id, ...data });
      return post;
    } catch (error) {
      console.error('Failed to update post:', error);
      throw new Error('Failed to update post');
    }
  }

  /**
   * Delete a post
   */
  static async deletePost(id: string): Promise<void> {
    try {
      await trpcClient.posts.delete.mutate({ id });
    } catch (error) {
      console.error('Failed to delete post:', error);
      throw new Error('Failed to delete post');
    }
  }

  /**
   * Like a post
   */
  static async likePost(postId: string): Promise<void> {
    try {
      await trpcClient.posts.like.mutate({ postId });
    } catch (error) {
      console.error('Failed to like post:', error);
      throw new Error('Failed to like post');
    }
  }

  /**
   * Unlike a post
   */
  static async unlikePost(postId: string): Promise<void> {
    try {
      await trpcClient.posts.unlike.mutate({ postId });
    } catch (error) {
      console.error('Failed to unlike post:', error);
      throw new Error('Failed to unlike post');
    }
  }

  /**
   * Add a comment to a post
   */
  static async addComment(data: CreateCommentData): Promise<Comment> {
    try {
      const comment = await trpcClient.posts.comment.mutate(data);
      return comment;
    } catch (error) {
      console.error('Failed to add comment:', error);
      throw new Error('Failed to add comment');
    }
  }

  /**
   * Delete a comment
   */
  static async deleteComment(commentId: string): Promise<void> {
    try {
      await trpcClient.posts.deleteComment.mutate({ commentId });
    } catch (error) {
      console.error('Failed to delete comment:', error);
      throw new Error('Failed to delete comment');
    }
  }

  /**
   * Search posts
   */
  static async searchPosts(query: string, filters?: PostFilters): Promise<Post[]> {
    try {
      const response = await trpcClient.posts.getAll.query({
        query,
        filters,
        limit: 50,
      });

      return response.posts;
    } catch (error) {
      console.error('Failed to search posts:', error);
      throw new Error('Failed to search posts');
    }
  }

  /**
   * Get posts by category
   */
  static async getPostsByCategory(category: string): Promise<Post[]> {
    try {
      const response = await trpcClient.posts.getAll.query({
        filters: { category },
        limit: 50,
      });

      return response.posts;
    } catch (error) {
      console.error('Failed to fetch posts by category:', error);
      throw new Error('Failed to fetch posts by category');
    }
  }

  /**
   * Get posts by author
   */
  static async getPostsByAuthor(authorId: string): Promise<Post[]> {
    try {
      const response = await trpcClient.posts.getAll.query({
        filters: { author: authorId },
        limit: 50,
      });

      return response.posts;
    } catch (error) {
      console.error('Failed to fetch posts by author:', error);
      throw new Error('Failed to fetch posts by author');
    }
  }

  /**
   * Get trending posts (most liked, commented, etc.)
   */
  static async getTrendingPosts(limit?: number): Promise<Post[]> {
    try {
      const response = await trpcClient.posts.getAll.query({
        sortBy: 'likes',
        sortOrder: 'desc',
        limit: limit || 20,
      });

      return response.posts;
    } catch (error) {
      console.error('Failed to fetch trending posts:', error);
      throw new Error('Failed to fetch trending posts');
    }
  }

  /**
   * Get recent posts
   */
  static async getRecentPosts(limit?: number): Promise<Post[]> {
    try {
      const response = await trpcClient.posts.getAll.query({
        sortBy: 'date',
        sortOrder: 'desc',
        limit: limit || 20,
      });

      return response.posts;
    } catch (error) {
      console.error('Failed to fetch recent posts:', error);
      throw new Error('Failed to fetch recent posts');
    }
  }
}

export const postsService = PostsService;
