import { trpcClient } from '../api/trpc';
import { Donation, DonationCampaign } from '../types';

export interface DonationFilters {
  campaign?: string;
  amount?: { min?: number; max?: number };
  dateFrom?: Date;
  dateTo?: Date;
  anonymous?: boolean;
}

export interface DonationSearchParams {
  query?: string;
  filters?: DonationFilters;
  page?: number;
  limit?: number;
  sortBy?: 'date' | 'amount' | 'campaign';
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedDonationsResponse {
  donations: Donation[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

export interface CreateDonationData {
  campaignId: string;
  amount: number;
  isAnonymous?: boolean;
  message?: string;
  paymentMethod: 'card' | 'eft' | 'payfast';
  recurringType?: 'none' | 'monthly' | 'quarterly' | 'annually';
}

export interface PaymentResult {
  success: boolean;
  transactionId?: string;
  paymentUrl?: string;
  error?: string;
}

export interface DonationStats {
  totalDonated: number;
  totalDonations: number;
  averageDonation: number;
  topCampaigns: Array<{
    campaign: DonationCampaign;
    totalAmount: number;
    donationCount: number;
  }>;
  monthlyStats: Array<{
    month: string;
    amount: number;
    count: number;
  }>;
}

export class DonationsService {
  /**
   * Fetch all donations with optional filtering and pagination
   */
  static async getDonations(params: DonationSearchParams = {}): Promise<PaginatedDonationsResponse> {
    try {
      const response = await trpcClient.donations.getAll.query({
        query: params.query,
        filters: params.filters,
        page: params.page || 1,
        limit: params.limit || 20,
        sortBy: params.sortBy || 'date',
        sortOrder: params.sortOrder || 'desc',
      });

      return response;
    } catch (error) {
      console.error('Failed to fetch donations:', error);
      throw new Error('Failed to fetch donations');
    }
  }

  /**
   * Get donation by ID
   */
  static async getDonationById(id: string): Promise<Donation> {
    try {
      const donation = await trpcClient.donations.getById.query({ id });
      return donation;
    } catch (error) {
      console.error('Failed to fetch donation by ID:', error);
      throw new Error('Failed to fetch donation details');
    }
  }

  /**
   * Create a new donation
   */
  static async createDonation(data: CreateDonationData): Promise<Donation> {
    try {
      const donation = await trpcClient.donations.create.mutate(data);
      return donation;
    } catch (error) {
      console.error('Failed to create donation:', error);
      throw new Error('Failed to create donation');
    }
  }

  /**
   * Process payment for a donation
   */
  static async processPayment(donationId: string, paymentData: any): Promise<PaymentResult> {
    try {
      const result = await trpcClient.donations.processPayment.mutate({
        donationId,
        paymentData,
      });

      return result;
    } catch (error) {
      console.error('Failed to process payment:', error);
      throw new Error('Failed to process payment');
    }
  }

  /**
   * Get all donation campaigns
   */
  static async getCampaigns(): Promise<DonationCampaign[]> {
    try {
      const campaigns = await trpcClient.donations.getCampaigns.query();
      return campaigns;
    } catch (error) {
      console.error('Failed to fetch campaigns:', error);
      throw new Error('Failed to fetch campaigns');
    }
  }

  /**
   * Get active donation campaigns
   */
  static async getActiveCampaigns(): Promise<DonationCampaign[]> {
    try {
      const campaigns = await trpcClient.donations.getCampaigns.query();
      return campaigns.filter(campaign => campaign.isActive && new Date(campaign.endDate) > new Date());
    } catch (error) {
      console.error('Failed to fetch active campaigns:', error);
      throw new Error('Failed to fetch active campaigns');
    }
  }

  /**
   * Get donation statistics
   */
  static async getDonationStats(): Promise<DonationStats> {
    try {
      const stats = await trpcClient.donations.getStats.query();
      return stats;
    } catch (error) {
      console.error('Failed to fetch donation stats:', error);
      throw new Error('Failed to fetch donation stats');
    }
  }

  /**
   * Get user's donation history
   */
  static async getMyDonations(): Promise<Donation[]> {
    try {
      const response = await trpcClient.donations.getAll.query({
        filters: { /* user-specific filter will be applied on backend */ },
        limit: 100,
      });

      return response.donations;
    } catch (error) {
      console.error('Failed to fetch my donations:', error);
      throw new Error('Failed to fetch my donations');
    }
  }

  /**
   * Get donations for a specific campaign
   */
  static async getDonationsByCampaign(campaignId: string): Promise<Donation[]> {
    try {
      const response = await trpcClient.donations.getAll.query({
        filters: { campaign: campaignId },
        limit: 100,
      });

      return response.donations;
    } catch (error) {
      console.error('Failed to fetch donations by campaign:', error);
      throw new Error('Failed to fetch donations by campaign');
    }
  }

  /**
   * Get top donors (leaderboard)
   */
  static async getTopDonors(limit?: number): Promise<Array<{
    donor: any;
    totalAmount: number;
    donationCount: number;
  }>> {
    try {
      const stats = await trpcClient.donations.getStats.query();
      // This would need to be implemented in the backend to return top donors
      // For now, return empty array
      return [];
    } catch (error) {
      console.error('Failed to fetch top donors:', error);
      throw new Error('Failed to fetch top donors');
    }
  }

  /**
   * Get recent donations (for activity feed)
   */
  static async getRecentDonations(limit?: number): Promise<Donation[]> {
    try {
      const response = await trpcClient.donations.getAll.query({
        sortBy: 'date',
        sortOrder: 'desc',
        limit: limit || 20,
      });

      return response.donations;
    } catch (error) {
      console.error('Failed to fetch recent donations:', error);
      throw new Error('Failed to fetch recent donations');
    }
  }

  /**
   * Cancel a recurring donation
   */
  static async cancelRecurringDonation(donationId: string): Promise<void> {
    try {
      // This would need to be implemented in the backend
      // For now, just log the action
      console.log('Cancelling recurring donation:', donationId);
    } catch (error) {
      console.error('Failed to cancel recurring donation:', error);
      throw new Error('Failed to cancel recurring donation');
    }
  }

  /**
   * Update recurring donation amount
   */
  static async updateRecurringDonation(donationId: string, newAmount: number): Promise<void> {
    try {
      // This would need to be implemented in the backend
      // For now, just log the action
      console.log('Updating recurring donation:', donationId, newAmount);
    } catch (error) {
      console.error('Failed to update recurring donation:', error);
      throw new Error('Failed to update recurring donation');
    }
  }
}

export const donationsService = DonationsService;
