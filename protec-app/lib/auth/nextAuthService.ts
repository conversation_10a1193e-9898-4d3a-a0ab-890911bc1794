import * as AuthSession from 'expo-auth-session';
import * as <PERSON><PERSON>rowser from 'expo-web-browser';
import { config } from '../config/env';
import { getAuthToken, setAuthToken, removeAuthToken, setUserData, clearAllAuthData } from './storage';

// Configure WebBrowser for auth session
WebBrowser.maybeCompleteAuthSession();

interface AuthResult {
  success: boolean;
  token?: string;
  user?: any;
  error?: string;
}

interface LoginCredentials {
  email: string;
  password: string;
}

interface RegisterData {
  email: string;
  password: string;
  name: string;
  graduationYear: number;
  programmes: string[];
  province: string;
  city: string;
  country: string;
}

export class NextAuthService {
  private baseUrl: string;
  private redirectUri: string;

  constructor() {
    this.baseUrl = config.WEB_URL;
    this.redirectUri = AuthSession.makeRedirectUri({
      scheme: 'protec-alumni',
      path: 'auth',
    });
  }

  /**
   * OAuth authentication flow using NextAuth
   */
  async authenticateWithOAuth(provider: 'google' | 'github'): Promise<AuthResult> {
    try {
      // Create auth URL that redirects to NextAuth
      const authUrl = `${this.baseUrl}/api/auth/signin/${provider}?callbackUrl=${encodeURIComponent(this.redirectUri)}`;
      
      const result = await AuthSession.startAsync({
        authUrl,
        returnUrl: this.redirectUri,
      });

      if (result.type === 'success' && result.url) {
        // Extract session token from callback URL
        const url = new URL(result.url);
        const sessionToken = url.searchParams.get('session_token');
        
        if (sessionToken) {
          // Verify session and get user data
          const user = await this.verifySession(sessionToken);
          
          if (user) {
            await setAuthToken(sessionToken);
            await setUserData(user);
            
            return {
              success: true,
              token: sessionToken,
              user,
            };
          }
        }
      }

      return {
        success: false,
        error: 'Authentication failed',
      };
    } catch (error) {
      console.error('OAuth authentication error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Email/password login
   */
  async login(credentials: LoginCredentials): Promise<AuthResult> {
    try {
      // Check if we're in development mode and API server might not be running
      const isDev = __DEV__;

      const response = await fetch(`${this.baseUrl}/api/auth/credentials`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
        timeout: 10000, // 10 second timeout
      });

      if (!response.ok) {
        const error = await response.json();
        return {
          success: false,
          error: error.message || 'Login failed',
        };
      }

      const { user, token } = await response.json();

      await setAuthToken(token);
      await setUserData(user);

      return {
        success: true,
        token,
        user,
      };
    } catch (error) {
      console.error('Login error:', error);

      // In development mode, provide a fallback mock login for testing
      const isDev = __DEV__;
      if (isDev && error instanceof Error && error.message.includes('Network request failed')) {
        console.warn('API server not available, using mock login for development');

        // Mock user data for development
        const mockUser = {
          id: 'dev-user-1',
          email: credentials.email,
          name: 'Development User',
          role: 'alumni',
          isActive: true,
          province: 'Western Cape',
          city: 'Cape Town',
          country: 'South Africa',
        };

        const mockToken = 'dev-token-' + Date.now();

        await setAuthToken(mockToken);
        await setUserData(mockUser);

        return {
          success: true,
          token: mockToken,
          user: mockUser,
        };
      }

      // Provide more specific error messages
      let errorMessage = 'Login failed';
      if (error instanceof Error) {
        if (error.message.includes('Network request failed') || error.message.includes('fetch')) {
          errorMessage = 'Unable to connect to server. Please check your internet connection and ensure the API server is running.';
        } else {
          errorMessage = error.message;
        }
      }

      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * Register new user
   */
  async register(data: RegisterData): Promise<AuthResult> {
    try {
      const response = await fetch(`${this.baseUrl}/api/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        return {
          success: false,
          error: error.message || 'Registration failed',
        };
      }

      const { user, token } = await response.json();
      
      await setAuthToken(token);
      await setUserData(user);
      
      return {
        success: true,
        token,
        user,
      };
    } catch (error) {
      console.error('Registration error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Registration failed',
      };
    }
  }

  /**
   * Verify session token with backend
   */
  async verifySession(token: string): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/api/auth/session`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        return null;
      }

      const session = await response.json();
      return session.user;
    } catch (error) {
      console.error('Session verification error:', error);
      return null;
    }
  }

  /**
   * Logout user
   */
  async logout(): Promise<void> {
    try {
      const token = await getAuthToken();
      
      if (token) {
        // Call logout endpoint
        await fetch(`${this.baseUrl}/api/auth/signout`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Clear local storage regardless of API call success
      await clearAllAuthData();
    }
  }

  /**
   * Get current user from stored session
   */
  async getCurrentUser(): Promise<any> {
    try {
      const token = await getAuthToken();
      
      if (!token) {
        return null;
      }

      return await this.verifySession(token);
    } catch (error) {
      console.error('Get current user error:', error);
      return null;
    }
  }

  /**
   * Refresh authentication token
   */
  async refreshToken(): Promise<string | null> {
    try {
      const token = await getAuthToken();
      
      if (!token) {
        return null;
      }

      const response = await fetch(`${this.baseUrl}/api/auth/refresh`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        return null;
      }

      const { token: newToken } = await response.json();
      
      if (newToken) {
        await setAuthToken(newToken);
        return newToken;
      }

      return null;
    } catch (error) {
      console.error('Token refresh error:', error);
      return null;
    }
  }
}

export const nextAuthService = new NextAuthService();
