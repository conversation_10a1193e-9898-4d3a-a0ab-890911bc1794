import { create } from 'zustand';
import { Alumni } from '../types';

interface CareerEntry {
  id: string;
  title: string;
  company: string;
  startDate: string;
  endDate?: string;
  current: boolean;
  description: string;
  location: string;
}

interface EducationEntry {
  id: string;
  institution: string;
  degree: string;
  field: string;
  startYear: number;
  endYear?: number;
  current: boolean;
  description?: string;
}

interface PrivacySettings {
  profileVisibility: 'public' | 'alumni_only' | 'private';
  showEmail: boolean;
  showPhone: boolean;
  showLocation: boolean;
  showCareerHistory: boolean;
  showEducation: boolean;
  allowMessages: boolean;
  allowConnectionRequests: boolean;
  showInDirectory: boolean;
  emailNotifications: boolean;
  pushNotifications: boolean;
}

interface ProfileStats {
  connections: number;
  posts: number;
  events: number;
  donations: number;
}

interface ProfileState {
  // Profile data
  profile: Alumni | null;
  careerHistory: CareerEntry[];
  education: EducationEntry[];
  privacySettings: PrivacySettings;
  stats: ProfileStats;
  
  // UI state
  isLoading: boolean;
  isUpdating: boolean;
  error: string | null;
  
  // Actions
  setProfile: (profile: Alumni) => void;
  updateProfile: (updates: Partial<Alumni>) => void;
  setCareerHistory: (history: CareerEntry[]) => void;
  addCareerEntry: (entry: CareerEntry) => void;
  updateCareerEntry: (id: string, updates: Partial<CareerEntry>) => void;
  removeCareerEntry: (id: string) => void;
  setEducation: (education: EducationEntry[]) => void;
  addEducationEntry: (entry: EducationEntry) => void;
  updateEducationEntry: (id: string, updates: Partial<EducationEntry>) => void;
  removeEducationEntry: (id: string) => void;
  setPrivacySettings: (settings: PrivacySettings) => void;
  updatePrivacySettings: (updates: Partial<PrivacySettings>) => void;
  setStats: (stats: ProfileStats) => void;
  setLoading: (loading: boolean) => void;
  setUpdating: (updating: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  reset: () => void;
}

const defaultPrivacySettings: PrivacySettings = {
  profileVisibility: 'alumni_only',
  showEmail: false,
  showPhone: false,
  showLocation: true,
  showCareerHistory: true,
  showEducation: true,
  allowMessages: true,
  allowConnectionRequests: true,
  showInDirectory: true,
  emailNotifications: true,
  pushNotifications: true,
};

const defaultStats: ProfileStats = {
  connections: 0,
  posts: 0,
  events: 0,
  donations: 0,
};

export const useProfileStore = create<ProfileState>((set, get) => ({
  // Initial state
  profile: null,
  careerHistory: [],
  education: [],
  privacySettings: defaultPrivacySettings,
  stats: defaultStats,
  isLoading: false,
  isUpdating: false,
  error: null,

  // Actions
  setProfile: (profile) => set({ profile }),
  
  updateProfile: (updates) => {
    const { profile } = get();
    if (profile) {
      set({ profile: { ...profile, ...updates } });
    }
  },
  
  setCareerHistory: (history) => set({ careerHistory: history }),
  
  addCareerEntry: (entry) => {
    const { careerHistory } = get();
    set({ careerHistory: [entry, ...careerHistory] });
  },
  
  updateCareerEntry: (id, updates) => {
    const { careerHistory } = get();
    set({
      careerHistory: careerHistory.map(entry =>
        entry.id === id ? { ...entry, ...updates } : entry
      ),
    });
  },
  
  removeCareerEntry: (id) => {
    const { careerHistory } = get();
    set({ careerHistory: careerHistory.filter(entry => entry.id !== id) });
  },
  
  setEducation: (education) => set({ education }),
  
  addEducationEntry: (entry) => {
    const { education } = get();
    set({ education: [entry, ...education] });
  },
  
  updateEducationEntry: (id, updates) => {
    const { education } = get();
    set({
      education: education.map(entry =>
        entry.id === id ? { ...entry, ...updates } : entry
      ),
    });
  },
  
  removeEducationEntry: (id) => {
    const { education } = get();
    set({ education: education.filter(entry => entry.id !== id) });
  },
  
  setPrivacySettings: (settings) => set({ privacySettings: settings }),
  
  updatePrivacySettings: (updates) => {
    const { privacySettings } = get();
    set({ privacySettings: { ...privacySettings, ...updates } });
  },
  
  setStats: (stats) => set({ stats }),
  
  setLoading: (loading) => set({ isLoading: loading }),
  setUpdating: (updating) => set({ isUpdating: updating }),
  setError: (error) => set({ error }),
  clearError: () => set({ error: null }),
  
  reset: () => set({
    profile: null,
    careerHistory: [],
    education: [],
    privacySettings: defaultPrivacySettings,
    stats: defaultStats,
    isLoading: false,
    isUpdating: false,
    error: null,
  }),
}));
