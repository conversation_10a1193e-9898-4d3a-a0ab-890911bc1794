import { create } from 'zustand';
import { Conversation, Message, Alumni, UserRole } from '@/lib/types';

interface MessagesState {
  // Conversations
  conversations: Conversation[];
  currentConversation: Conversation | null;
  isLoadingConversations: boolean;
  
  // Messages
  messages: Message[];
  isLoadingMessages: boolean;
  
  // Actions
  fetchConversations: () => Promise<void>;
  fetchConversation: (conversationId: string) => Promise<void>;
  fetchMessages: (conversationId: string) => Promise<void>;
  sendMessage: (conversationId: string, content: string) => Promise<Message>;
  createConversation: (recipientId: string, initialMessage: string) => Promise<Conversation>;
  markAsRead: (conversationId: string) => Promise<void>;
  
  // Real-time updates
  addMessage: (message: Message) => void;
  updateConversation: (conversation: Conversation) => void;
}

export const useMessagesStore = create<MessagesState>((set, get) => ({
  // Initial state
  conversations: [],
  currentConversation: null,
  isLoadingConversations: false,
  messages: [],
  isLoadingMessages: false,

  // Fetch all conversations for the current user
  fetchConversations: async () => {
    set({ isLoadingConversations: true });
    
    try {
      // TODO: Replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock data
      const mockConversations: Conversation[] = [
        {
          id: '1',
          participants: [
            {
              id: 'current-user',
              name: 'Current User',
              email: '<EMAIL>',
              role: UserRole.ALUMNI,
              isActive: true,
              province: 'Gauteng',
              city: 'Johannesburg',
              country: 'South Africa',
            },
            {
              id: '2',
              name: 'Jane Smith',
              email: '<EMAIL>',
              role: UserRole.ALUMNI,
              isActive: true,
              province: 'Western Cape',
              city: 'Cape Town',
              country: 'South Africa',
              currentRole: 'Project Manager',
              company: 'Build Corp',
            },
          ],
          lastMessage: {
            id: '1',
            conversationId: '1',
            senderId: '2',
            content: 'Thanks for connecting! Looking forward to our chat.',
            createdAt: new Date('2024-01-15T14:30:00'),
            updatedAt: new Date('2024-01-15T14:30:00'),
          },
          unreadCount: 1,
          createdAt: new Date('2024-01-15T10:00:00'),
          updatedAt: new Date('2024-01-15T14:30:00'),
        },
      ];
      
      set({ conversations: mockConversations });
    } catch (error) {
      console.error('Failed to fetch conversations:', error);
    } finally {
      set({ isLoadingConversations: false });
    }
  },

  // Fetch a specific conversation
  fetchConversation: async (conversationId: string) => {
    try {
      // TODO: Replace with actual API call
      const { conversations } = get();
      const conversation = conversations.find(c => c.id === conversationId);
      
      if (conversation) {
        set({ currentConversation: conversation });
      }
    } catch (error) {
      console.error('Failed to fetch conversation:', error);
    }
  },

  // Fetch messages for a conversation
  fetchMessages: async (conversationId: string) => {
    set({ isLoadingMessages: true });
    
    try {
      // TODO: Replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 800));
      
      // Mock messages
      const mockMessages: Message[] = [
        {
          id: '1',
          conversationId,
          senderId: 'current-user',
          content: 'Hi Jane! I saw your profile and would love to connect. We both work in similar fields.',
          createdAt: new Date('2024-01-15T10:00:00'),
          updatedAt: new Date('2024-01-15T10:00:00'),
        },
        {
          id: '2',
          conversationId,
          senderId: '2',
          content: 'Hi! Thanks for reaching out. I\'d be happy to connect and share experiences.',
          createdAt: new Date('2024-01-15T10:15:00'),
          updatedAt: new Date('2024-01-15T10:15:00'),
        },
        {
          id: '3',
          conversationId,
          senderId: '2',
          content: 'Thanks for connecting! Looking forward to our chat.',
          createdAt: new Date('2024-01-15T14:30:00'),
          updatedAt: new Date('2024-01-15T14:30:00'),
        },
      ];
      
      set({ messages: mockMessages });
    } catch (error) {
      console.error('Failed to fetch messages:', error);
    } finally {
      set({ isLoadingMessages: false });
    }
  },

  // Send a new message
  sendMessage: async (conversationId: string, content: string) => {
    try {
      // TODO: Replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const newMessage: Message = {
        id: Date.now().toString(),
        conversationId,
        senderId: 'current-user', // TODO: Get from auth store
        content,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      
      // Add message to current messages
      const { messages } = get();
      set({ messages: [...messages, newMessage] });
      
      // Update conversation's last message
      const { conversations } = get();
      const updatedConversations = conversations.map(conv => {
        if (conv.id === conversationId) {
          return {
            ...conv,
            lastMessage: newMessage,
            updatedAt: new Date(),
          };
        }
        return conv;
      });
      set({ conversations: updatedConversations });
      
      return newMessage;
    } catch (error) {
      console.error('Failed to send message:', error);
      throw error;
    }
  },

  // Create a new conversation
  createConversation: async (recipientId: string, initialMessage: string) => {
    try {
      // TODO: Replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const conversationId = Date.now().toString();
      
      // Create the conversation
      const newConversation: Conversation = {
        id: conversationId,
        participants: [
          {
            id: 'current-user',
            name: 'Current User',
            email: '<EMAIL>',
            role: UserRole.ALUMNI,
            isActive: true,
            province: 'Gauteng',
            city: 'Johannesburg',
            country: 'South Africa',
          },
          // TODO: Get recipient details from API
          {
            id: recipientId,
            name: 'Recipient',
            email: '<EMAIL>',
            role: UserRole.ALUMNI,
            isActive: true,
            province: 'Western Cape',
            city: 'Cape Town',
            country: 'South Africa',
          },
        ],
        lastMessage: {
          id: Date.now().toString(),
          conversationId,
          senderId: 'current-user',
          content: initialMessage,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        unreadCount: 0,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      
      // Add to conversations list
      const { conversations } = get();
      set({ conversations: [newConversation, ...conversations] });
      
      return newConversation;
    } catch (error) {
      console.error('Failed to create conversation:', error);
      throw error;
    }
  },

  // Mark conversation as read
  markAsRead: async (conversationId: string) => {
    try {
      // TODO: Replace with actual API call
      
      const { conversations } = get();
      const updatedConversations = conversations.map(conv => {
        if (conv.id === conversationId) {
          return { ...conv, unreadCount: 0 };
        }
        return conv;
      });
      set({ conversations: updatedConversations });
    } catch (error) {
      console.error('Failed to mark as read:', error);
    }
  },

  // Real-time message addition
  addMessage: (message: Message) => {
    const { messages } = get();
    set({ messages: [...messages, message] });
    
    // Update conversation's last message
    const { conversations } = get();
    const updatedConversations = conversations.map(conv => {
      if (conv.id === message.conversationId) {
        return {
          ...conv,
          lastMessage: message,
          updatedAt: new Date(),
          unreadCount: message.senderId !== 'current-user' ? conv.unreadCount + 1 : conv.unreadCount,
        };
      }
      return conv;
    });
    set({ conversations: updatedConversations });
  },

  // Update conversation
  updateConversation: (conversation: Conversation) => {
    const { conversations } = get();
    const updatedConversations = conversations.map(conv => 
      conv.id === conversation.id ? conversation : conv
    );
    set({ conversations: updatedConversations });
  },
}));
