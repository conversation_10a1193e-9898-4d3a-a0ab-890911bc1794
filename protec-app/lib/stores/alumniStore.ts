import { create } from 'zustand';
import { Alumni } from '../types';

interface AlumniState {
  // Alumni data
  alumni: Alumni[];
  selectedAlumni: Alumni | null;
  searchQuery: string;
  filters: {
    graduationYear?: number;
    industry?: string;
    location?: string;
    skills?: string[];
  };
  
  // Pagination
  currentPage: number;
  hasMore: boolean;
  isLoading: boolean;
  
  // Actions
  setAlumni: (alumni: Alumni[]) => void;
  addAlumni: (alumni: Alumni[]) => void;
  setSelectedAlumni: (alumni: Alumni | null) => void;
  setSearchQuery: (query: string) => void;
  setFilters: (filters: Partial<AlumniState['filters']>) => void;
  clearFilters: () => void;
  setCurrentPage: (page: number) => void;
  setHasMore: (hasMore: boolean) => void;
  setLoading: (loading: boolean) => void;
  reset: () => void;
}

export const useAlumniStore = create<AlumniState>((set, get) => ({
  // Initial state
  alumni: [],
  selectedAlumni: null,
  searchQuery: '',
  filters: {},
  currentPage: 1,
  hasMore: true,
  isLoading: false,

  // Actions
  setAlumni: (alumni) => set({ alumni }),
  
  addAlumni: (newAlumni) => {
    const { alumni } = get();
    set({ alumni: [...alumni, ...newAlumni] });
  },
  
  setSelectedAlumni: (alumni) => set({ selectedAlumni: alumni }),
  
  setSearchQuery: (query) => set({ searchQuery: query, currentPage: 1 }),
  
  setFilters: (newFilters) => {
    const { filters } = get();
    set({ 
      filters: { ...filters, ...newFilters },
      currentPage: 1 
    });
  },
  
  clearFilters: () => set({ 
    filters: {},
    searchQuery: '',
    currentPage: 1 
  }),
  
  setCurrentPage: (page) => set({ currentPage: page }),
  setHasMore: (hasMore) => set({ hasMore }),
  setLoading: (loading) => set({ isLoading: loading }),
  
  reset: () => set({
    alumni: [],
    selectedAlumni: null,
    searchQuery: '',
    filters: {},
    currentPage: 1,
    hasMore: true,
    isLoading: false,
  }),
}));
