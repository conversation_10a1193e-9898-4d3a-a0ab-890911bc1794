import { create } from 'zustand';
import { Event } from '../types';

interface EventsState {
  // Events data
  events: Event[];
  selectedEvent: Event | null;
  myEvents: Event[]; // Events user has RSVP'd to
  
  // Filters
  filters: {
    category?: string;
    startDate?: Date;
    endDate?: Date;
    location?: string;
  };
  
  // UI state
  isLoading: boolean;
  currentPage: number;
  hasMore: boolean;
  
  // Actions
  setEvents: (events: Event[]) => void;
  addEvents: (events: Event[]) => void;
  setSelectedEvent: (event: Event | null) => void;
  setMyEvents: (events: Event[]) => void;
  addToMyEvents: (event: Event) => void;
  removeFromMyEvents: (eventId: string) => void;
  setFilters: (filters: Partial<EventsState['filters']>) => void;
  clearFilters: () => void;
  setLoading: (loading: boolean) => void;
  setCurrentPage: (page: number) => void;
  setHasMore: (hasMore: boolean) => void;
  reset: () => void;
}

export const useEventsStore = create<EventsState>((set, get) => ({
  // Initial state
  events: [],
  selectedEvent: null,
  myEvents: [],
  filters: {},
  isLoading: false,
  currentPage: 1,
  hasMore: true,

  // Actions
  setEvents: (events) => set({ events }),
  
  addEvents: (newEvents) => {
    const { events } = get();
    set({ events: [...events, ...newEvents] });
  },
  
  setSelectedEvent: (event) => set({ selectedEvent: event }),
  
  setMyEvents: (events) => set({ myEvents: events }),
  
  addToMyEvents: (event) => {
    const { myEvents } = get();
    if (!myEvents.find(e => e.id === event.id)) {
      set({ myEvents: [...myEvents, event] });
    }
  },
  
  removeFromMyEvents: (eventId) => {
    const { myEvents } = get();
    set({ myEvents: myEvents.filter(e => e.id !== eventId) });
  },
  
  setFilters: (newFilters) => {
    const { filters } = get();
    set({ 
      filters: { ...filters, ...newFilters },
      currentPage: 1 
    });
  },
  
  clearFilters: () => set({ 
    filters: {},
    currentPage: 1 
  }),
  
  setLoading: (loading) => set({ isLoading: loading }),
  setCurrentPage: (page) => set({ currentPage: page }),
  setHasMore: (hasMore) => set({ hasMore }),
  
  reset: () => set({
    events: [],
    selectedEvent: null,
    myEvents: [],
    filters: {},
    isLoading: false,
    currentPage: 1,
    hasMore: true,
  }),
}));
