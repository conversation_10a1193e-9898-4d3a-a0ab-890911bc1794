import { create } from 'zustand';
import {
  getAuthToken,
  setUserData
} from '../auth/storage';

interface User {
  id: string;
  email: string;
  name: string;
  image?: string;
  role: string;
  isActive: boolean;
  province?: string;
  city?: string;
  country?: string;
}

interface LoginCredentials {
  email: string;
  password: string;
}

interface RegisterData {
  email: string;
  password: string;
  name: string;
  graduationYear: number;
  programmes: string[];
  province: string;
  city: string;
  country: string;
}

interface AuthState {
  user: User | null;
  token: string | null;
  isLoading: boolean;
  isAuthenticated: boolean;

  // Actions
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  logout: () => Promise<void>;
  loadAuthState: () => Promise<void>;
  updateUser: (user: Partial<User>) => Promise<void>;
  updateProfile: (data: Partial<User>) => Promise<void>;
}

export const useAuthStore = create<AuthState>((set, get) => ({
  user: null,
  token: null,
  isLoading: true,
  isAuthenticated: false,

  login: async (credentials: LoginCredentials) => {
    try {
      set({ isLoading: true });

      const { nextAuthService } = await import('../auth/nextAuthService');
      const result = await nextAuthService.login(credentials);

      if (!result.success) {
        throw new Error(result.error || 'Login failed');
      }

      set({
        token: result.token!,
        user: result.user!,
        isAuthenticated: true,
        isLoading: false,
      });
    } catch (error) {
      set({ isLoading: false });
      console.error('Login error:', error);
      throw error;
    }
  },

  register: async (data: RegisterData) => {
    try {
      set({ isLoading: true });

      const { nextAuthService } = await import('../auth/nextAuthService');
      const result = await nextAuthService.register(data);

      if (!result.success) {
        throw new Error(result.error || 'Registration failed');
      }

      set({
        token: result.token!,
        user: result.user!,
        isAuthenticated: true,
        isLoading: false,
      });
    } catch (error) {
      set({ isLoading: false });
      console.error('Registration error:', error);
      throw error;
    }
  },

  logout: async () => {
    try {
      set({ isLoading: true });

      const { nextAuthService } = await import('../auth/nextAuthService');
      await nextAuthService.logout();

      set({
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
      });
    } catch (error) {
      set({ isLoading: false });
      console.error('Logout error:', error);
    }
  },

  loadAuthState: async () => {
    try {
      set({ isLoading: true });

      const { nextAuthService } = await import('../auth/nextAuthService');
      const user = await nextAuthService.getCurrentUser();
      const token = await getAuthToken();

      if (token && user) {
        set({
          token,
          user,
          isAuthenticated: true,
          isLoading: false,
        });
      } else {
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          isLoading: false,
        });
      }
    } catch (error) {
      console.error('Load auth state error:', error);
      set({
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
      });
    }
  },

  updateUser: async (updatedUser: Partial<User>) => {
    const { user } = get();
    if (!user) return;

    const newUser = { ...user, ...updatedUser };

    try {
      await setUserData(newUser);
      set({ user: newUser });
    } catch (error) {
      console.error('Update user error:', error);
      throw error;
    }
  },

  updateProfile: async (data: Partial<User>) => {
    const { user } = get();
    if (!user) return;

    try {
      set({ isLoading: true });

      // TODO: Replace with actual API call
      const response = await fetch('/api/auth/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error('Profile update failed');
      }

      const { user: updatedUser } = await response.json();

      await setUserData(updatedUser);
      set({
        user: updatedUser,
        isLoading: false,
      });
    } catch (error) {
      set({ isLoading: false });
      console.error('Update profile error:', error);
      throw error;
    }
  },
}));
