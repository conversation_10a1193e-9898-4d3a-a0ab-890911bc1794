import { renderHook, act } from '@testing-library/react-native';
import { useNotificationsStore } from '../notificationsStore';
import { notificationService } from '@/lib/services/notificationService';

// Mock the notification service
jest.mock('@/lib/services/notificationService');
const mockNotificationService = notificationService as jest.Mocked<typeof notificationService>;

describe('NotificationsStore', () => {
  beforeEach(() => {
    // Reset the store state before each test
    useNotificationsStore.setState({
      preferences: {
        messages: true,
        events: true,
        connections: true,
        donations: false,
        posts: false,
        general: true,
        sound: true,
        vibration: true,
      },
      isInitialized: false,
      pushToken: null,
      permissionStatus: null,
      notifications: [],
      unreadCount: 0,
    });
    jest.clearAllMocks();
  });

  describe('Initial State', () => {
    it('should have correct initial state', () => {
      const { result } = renderHook(() => useNotificationsStore());
      
      expect(result.current.isInitialized).toBe(false);
      expect(result.current.pushToken).toBeNull();
      expect(result.current.permissionStatus).toBeNull();
      expect(result.current.notifications).toEqual([]);
      expect(result.current.unreadCount).toBe(0);
      expect(result.current.preferences.messages).toBe(true);
      expect(result.current.preferences.donations).toBe(false);
    });
  });

  describe('Initialize Notifications', () => {
    it('should initialize notifications successfully', async () => {
      const { result } = renderHook(() => useNotificationsStore());
      
      const mockToken = 'test-push-token';
      const mockPermissions = { status: 'granted' };

      mockNotificationService.initialize.mockResolvedValue(mockToken);
      mockNotificationService.getPermissions.mockResolvedValue(mockPermissions as any);

      await act(async () => {
        await result.current.initializeNotifications();
      });

      expect(result.current.isInitialized).toBe(true);
      expect(result.current.pushToken).toBe(mockToken);
      expect(result.current.permissionStatus).toBe('granted');
      expect(mockNotificationService.initialize).toHaveBeenCalled();
      expect(mockNotificationService.getPermissions).toHaveBeenCalled();
    });

    it('should handle initialization failure gracefully', async () => {
      const { result } = renderHook(() => useNotificationsStore());
      
      mockNotificationService.initialize.mockRejectedValue(new Error('Init failed'));

      await act(async () => {
        await result.current.initializeNotifications();
      });

      expect(result.current.isInitialized).toBe(true);
      expect(result.current.pushToken).toBeNull();
    });
  });

  describe('Update Preferences', () => {
    it('should update notification preferences', async () => {
      const { result } = renderHook(() => useNotificationsStore());
      
      const newPreferences = {
        messages: false,
        donations: true,
      };

      await act(async () => {
        await result.current.updatePreferences(newPreferences);
      });

      expect(result.current.preferences.messages).toBe(false);
      expect(result.current.preferences.donations).toBe(true);
      expect(result.current.preferences.events).toBe(true); // Should remain unchanged
    });
  });

  describe('Request Permissions', () => {
    it('should request permissions successfully', async () => {
      const { result } = renderHook(() => useNotificationsStore());
      
      const mockPermissions = { status: 'granted' };
      mockNotificationService.requestPermissions.mockResolvedValue(mockPermissions as any);
      mockNotificationService.initialize.mockResolvedValue('new-token');
      mockNotificationService.getPermissions.mockResolvedValue(mockPermissions as any);

      await act(async () => {
        const granted = await result.current.requestPermissions();
        expect(granted).toBe(true);
      });

      expect(result.current.permissionStatus).toBe('granted');
      expect(mockNotificationService.requestPermissions).toHaveBeenCalled();
    });

    it('should handle permission denial', async () => {
      const { result } = renderHook(() => useNotificationsStore());
      
      const mockPermissions = { status: 'denied' };
      mockNotificationService.requestPermissions.mockResolvedValue(mockPermissions as any);

      await act(async () => {
        const granted = await result.current.requestPermissions();
        expect(granted).toBe(false);
      });

      expect(result.current.permissionStatus).toBe('denied');
    });
  });

  describe('Send Test Notification', () => {
    it('should send test notification', async () => {
      const { result } = renderHook(() => useNotificationsStore());
      
      mockNotificationService.sendTestNotification.mockResolvedValue();

      await act(async () => {
        await result.current.sendTestNotification('message');
      });

      expect(mockNotificationService.sendTestNotification).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'message',
          title: 'New Message',
          body: 'You have a new message from John Doe',
        })
      );

      // Should add notification to local store
      expect(result.current.notifications).toHaveLength(1);
      expect(result.current.notifications[0].type).toBe('message');
    });
  });

  describe('Notification Management', () => {
    it('should add notification and update unread count', () => {
      const { result } = renderHook(() => useNotificationsStore());
      
      const notification = {
        type: 'message' as const,
        id: 'test-notification',
        title: 'Test Notification',
        body: 'Test body',
      };

      act(() => {
        result.current.addNotification(notification);
      });

      expect(result.current.notifications).toHaveLength(1);
      expect(result.current.unreadCount).toBe(1);
      expect(result.current.notifications[0]).toMatchObject(notification);
    });

    it('should not add notification if type is disabled', () => {
      const { result } = renderHook(() => useNotificationsStore());
      
      // Disable donations notifications
      act(() => {
        useNotificationsStore.setState({
          preferences: {
            ...result.current.preferences,
            donations: false,
          },
        });
      });

      const notification = {
        type: 'donation' as const,
        id: 'test-donation',
        title: 'Donation Update',
        body: 'New campaign',
      };

      act(() => {
        result.current.addNotification(notification);
      });

      expect(result.current.notifications).toHaveLength(0);
      expect(result.current.unreadCount).toBe(0);
    });

    it('should mark notification as read', () => {
      const { result } = renderHook(() => useNotificationsStore());
      
      // Add a notification first
      const notification = {
        type: 'message' as const,
        id: 'test-notification',
        title: 'Test Notification',
        body: 'Test body',
      };

      act(() => {
        result.current.addNotification(notification);
      });

      expect(result.current.unreadCount).toBe(1);

      // Mark as read
      act(() => {
        result.current.markAsRead('test-notification');
      });

      expect(result.current.unreadCount).toBe(0);
      expect((result.current.notifications[0] as any).read).toBe(true);
    });

    it('should mark all notifications as read', () => {
      const { result } = renderHook(() => useNotificationsStore());
      
      // Add multiple notifications
      const notifications = [
        { type: 'message' as const, id: 'msg1', title: 'Message 1', body: 'Body 1' },
        { type: 'event' as const, id: 'event1', title: 'Event 1', body: 'Body 2' },
      ];

      act(() => {
        notifications.forEach(notification => {
          result.current.addNotification(notification);
        });
      });

      expect(result.current.unreadCount).toBe(2);

      // Mark all as read
      act(() => {
        result.current.markAllAsRead();
      });

      expect(result.current.unreadCount).toBe(0);
      expect(result.current.notifications.every((n: any) => n.read)).toBe(true);
    });

    it('should clear all notifications', () => {
      const { result } = renderHook(() => useNotificationsStore());
      
      // Add notifications
      const notification = {
        type: 'message' as const,
        id: 'test-notification',
        title: 'Test Notification',
        body: 'Test body',
      };

      act(() => {
        result.current.addNotification(notification);
      });

      expect(result.current.notifications).toHaveLength(1);

      // Clear all
      act(() => {
        result.current.clearNotifications();
      });

      expect(result.current.notifications).toHaveLength(0);
      expect(result.current.unreadCount).toBe(0);
      expect(mockNotificationService.clearAllNotifications).toHaveBeenCalled();
    });
  });

  describe('Badge Management', () => {
    it('should update badge count', async () => {
      const { result } = renderHook(() => useNotificationsStore());
      
      mockNotificationService.setBadgeCount.mockResolvedValue();

      // Set unread count
      act(() => {
        useNotificationsStore.setState({ unreadCount: 5 });
      });

      await act(async () => {
        await result.current.updateBadgeCount();
      });

      expect(mockNotificationService.setBadgeCount).toHaveBeenCalledWith(5);
    });

    it('should clear badge', async () => {
      const { result } = renderHook(() => useNotificationsStore());
      
      mockNotificationService.setBadgeCount.mockResolvedValue();

      await act(async () => {
        await result.current.clearBadge();
      });

      expect(mockNotificationService.setBadgeCount).toHaveBeenCalledWith(0);
    });
  });
});
