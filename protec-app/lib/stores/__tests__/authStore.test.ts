import { renderHook, act } from '@testing-library/react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAuthStore } from '../authStore';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage');
const mockAsyncStorage = AsyncStorage as jest.Mocked<typeof AsyncStorage>;

describe('AuthStore', () => {
  beforeEach(() => {
    // Reset the store state before each test
    useAuthStore.setState({
      user: null,
      isAuthenticated: false,
      isLoading: false,
    });
    jest.clearAllMocks();
  });

  describe('Initial State', () => {
    it('should have correct initial state', () => {
      const { result } = renderHook(() => useAuthStore());
      
      expect(result.current.user).toBeNull();
      expect(result.current.isAuthenticated).toBe(false);
      expect(result.current.isLoading).toBe(false);
    });
  });

  describe('Login', () => {
    it('should login user successfully', async () => {
      const { result } = renderHook(() => useAuthStore());
      
      const mockUser = {
        id: 'test-id',
        email: '<EMAIL>',
        name: 'Test User',
        role: 'ALUMNI' as const,
        isActive: true,
        province: 'Gauteng',
        city: 'Johannesburg',
        country: 'South Africa',
      };

      const mockCredentials = {
        email: '<EMAIL>',
        password: 'password123',
      };

      // Mock successful API response
      global.fetch = jest.fn(() =>
        Promise.resolve({
          ok: true,
          json: () => Promise.resolve({
            user: mockUser,
            token: 'mock-token',
          }),
        })
      ) as jest.Mock;

      await act(async () => {
        await result.current.login(mockCredentials);
      });

      expect(result.current.user).toEqual(mockUser);
      expect(result.current.isAuthenticated).toBe(true);
      expect(result.current.isLoading).toBe(false);
      expect(mockAsyncStorage.setItem).toHaveBeenCalledWith(
        'auth-token',
        'mock-token'
      );
    });

    it('should handle login failure', async () => {
      const { result } = renderHook(() => useAuthStore());
      
      const mockCredentials = {
        email: '<EMAIL>',
        password: 'wrongpassword',
      };

      // Mock failed API response
      global.fetch = jest.fn(() =>
        Promise.resolve({
          ok: false,
          status: 401,
          json: () => Promise.resolve({
            error: 'Invalid credentials',
          }),
        })
      ) as jest.Mock;

      await act(async () => {
        try {
          await result.current.login(mockCredentials);
        } catch (error) {
          expect(error).toBeDefined();
        }
      });

      expect(result.current.user).toBeNull();
      expect(result.current.isAuthenticated).toBe(false);
      expect(result.current.isLoading).toBe(false);
    });
  });

  describe('Register', () => {
    it('should register user successfully', async () => {
      const { result } = renderHook(() => useAuthStore());
      
      const mockRegistrationData = {
        email: '<EMAIL>',
        password: 'password123',
        name: 'New User',
        graduationYear: 2020,
        programmes: ['Computer Science'],
        province: 'Gauteng',
        city: 'Johannesburg',
        country: 'South Africa',
      };

      const mockUser = {
        id: 'new-user-id',
        ...mockRegistrationData,
        role: 'ALUMNI' as const,
        isActive: true,
      };

      // Mock successful API response
      global.fetch = jest.fn(() =>
        Promise.resolve({
          ok: true,
          json: () => Promise.resolve({
            user: mockUser,
            token: 'new-user-token',
          }),
        })
      ) as jest.Mock;

      await act(async () => {
        await result.current.register(mockRegistrationData);
      });

      expect(result.current.user).toEqual(mockUser);
      expect(result.current.isAuthenticated).toBe(true);
      expect(mockAsyncStorage.setItem).toHaveBeenCalledWith(
        'auth-token',
        'new-user-token'
      );
    });
  });

  describe('Logout', () => {
    it('should logout user successfully', async () => {
      const { result } = renderHook(() => useAuthStore());
      
      // Set initial authenticated state
      act(() => {
        useAuthStore.setState({
          user: {
            id: 'test-id',
            email: '<EMAIL>',
            name: 'Test User',
            role: 'ALUMNI',
            isActive: true,
            province: 'Gauteng',
            city: 'Johannesburg',
            country: 'South Africa',
          },
          isAuthenticated: true,
        });
      });

      await act(async () => {
        await result.current.logout();
      });

      expect(result.current.user).toBeNull();
      expect(result.current.isAuthenticated).toBe(false);
      expect(mockAsyncStorage.removeItem).toHaveBeenCalledWith('auth-token');
    });
  });

  describe('Load Auth State', () => {
    it('should load auth state from storage', async () => {
      const { result } = renderHook(() => useAuthStore());
      
      const mockToken = 'stored-token';
      const mockUser = {
        id: 'stored-user-id',
        email: '<EMAIL>',
        name: 'Stored User',
        role: 'ALUMNI' as const,
        isActive: true,
        province: 'Gauteng',
        city: 'Johannesburg',
        country: 'South Africa',
      };

      mockAsyncStorage.getItem.mockResolvedValue(mockToken);
      
      // Mock API response for token validation
      global.fetch = jest.fn(() =>
        Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ user: mockUser }),
        })
      ) as jest.Mock;

      await act(async () => {
        await result.current.loadAuthState();
      });

      expect(result.current.user).toEqual(mockUser);
      expect(result.current.isAuthenticated).toBe(true);
      expect(mockAsyncStorage.getItem).toHaveBeenCalledWith('auth-token');
    });

    it('should handle invalid stored token', async () => {
      const { result } = renderHook(() => useAuthStore());
      
      const mockToken = 'invalid-token';
      mockAsyncStorage.getItem.mockResolvedValue(mockToken);
      
      // Mock API response for invalid token
      global.fetch = jest.fn(() =>
        Promise.resolve({
          ok: false,
          status: 401,
        })
      ) as jest.Mock;

      await act(async () => {
        await result.current.loadAuthState();
      });

      expect(result.current.user).toBeNull();
      expect(result.current.isAuthenticated).toBe(false);
      expect(mockAsyncStorage.removeItem).toHaveBeenCalledWith('auth-token');
    });

    it('should handle no stored token', async () => {
      const { result } = renderHook(() => useAuthStore());
      
      mockAsyncStorage.getItem.mockResolvedValue(null);

      await act(async () => {
        await result.current.loadAuthState();
      });

      expect(result.current.user).toBeNull();
      expect(result.current.isAuthenticated).toBe(false);
      expect(result.current.isLoading).toBe(false);
    });
  });

  describe('Update Profile', () => {
    it('should update user profile successfully', async () => {
      const { result } = renderHook(() => useAuthStore());
      
      // Set initial authenticated state
      const initialUser = {
        id: 'test-id',
        email: '<EMAIL>',
        name: 'Test User',
        role: 'ALUMNI' as const,
        isActive: true,
        province: 'Gauteng',
        city: 'Johannesburg',
        country: 'South Africa',
      };

      act(() => {
        useAuthStore.setState({
          user: initialUser,
          isAuthenticated: true,
        });
      });

      const updateData = {
        name: 'Updated Name',
        currentRole: 'Senior Developer',
        company: 'Tech Corp',
      };

      const updatedUser = { ...initialUser, ...updateData };

      // Mock successful API response
      global.fetch = jest.fn(() =>
        Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ user: updatedUser }),
        })
      ) as jest.Mock;

      await act(async () => {
        await result.current.updateProfile(updateData);
      });

      expect(result.current.user).toEqual(updatedUser);
    });
  });
});
