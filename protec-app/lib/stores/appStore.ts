import { create } from 'zustand';

interface AppState {
  // UI State
  isLoading: boolean;
  error: string | null;
  
  // Theme and preferences
  isDarkMode: boolean;
  notificationsEnabled: boolean;
  
  // Network state
  isOnline: boolean;
  
  // Actions
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setDarkMode: (enabled: boolean) => void;
  setNotificationsEnabled: (enabled: boolean) => void;
  setOnlineStatus: (online: boolean) => void;
  clearError: () => void;
}

export const useAppStore = create<AppState>((set) => ({
  // Initial state
  isLoading: false,
  error: null,
  isDarkMode: false,
  notificationsEnabled: true,
  isOnline: true,

  // Actions
  setLoading: (loading) => set({ isLoading: loading }),
  setError: (error) => set({ error }),
  setDarkMode: (enabled) => set({ isDarkMode: enabled }),
  setNotificationsEnabled: (enabled) => set({ notificationsEnabled: enabled }),
  setOnlineStatus: (online) => set({ isOnline: online }),
  clearError: () => set({ error: null }),
}));
