// User and Authentication Types
export interface User {
  id: string;
  email: string;
  name: string;
  image?: string;
  role: UserRole;
  isActive: boolean;
}

export enum UserRole {
  ALUMNI = 'ALUMNI',
  ADMIN = 'ADMIN',
  EVENT_ORGANIZER = 'EVENT_ORGANIZER',
  DONOR_COORDINATOR = 'DONOR_COORDINATOR',
}

// Alumni Profile Types
export interface Alumni {
  id: string;
  email: string;
  name: string;
  photoUrl?: string;
  bio?: string;
  graduationYear: number;
  programmes: string[];
  currentRole?: string;
  company?: string;
  industry?: string;
  skills: string[];
  interests: string[];
  province: string;
  city: string;
  country: string;
  socialLinks?: any;
  privacy: any;
  role: UserRole;
  isActive: boolean;
  lastLoginAt?: Date;
}

// Event Types
export interface Event {
  id: string;
  title: string;
  description: string;
  category: string;
  startTime: Date;
  endTime: Date;
  startDate: Date; // Alias for startTime
  endDate: Date; // Alias for endTime
  location?: string;
  organizer: Alumni;
  organizerId: string;
  isPublic: boolean;
  isVirtual?: boolean;
  virtualLink?: string;
  allDay?: boolean;
  maxAttendees?: number;
  tags: string[];
  imageUrl?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Post Types
export interface Post {
  id: string;
  author: Alumni;
  authorId: string;
  content: string;
  mediaUrls: string[];
  tags: string[];
  likes: string[];
  comments: Comment[];
  isModerated: boolean;
  moderatedBy?: string;
  moderatedAt?: Date;
  isPublic: boolean;
  isPinned: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Comment {
  id: string;
  post: Post;
  postId: string;
  author: Alumni;
  authorId: string;
  text: string;
  createdAt: Date;
  updatedAt: Date;
}

// Message Types
export interface Message {
  id: string;
  conversationId: string;
  senderId: string;
  content: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Conversation {
  id: string;
  participants: Alumni[];
  lastMessage?: Message;
  unreadCount: number;
  createdAt: Date;
  updatedAt: Date;
}

export enum MessageStatus {
  SENT = 'SENT',
  DELIVERED = 'DELIVERED',
  READ = 'READ',
}

// Donation Types
export interface Donation {
  id: string;
  donor: Alumni;
  donorId: string;
  amount: number;
  currency: string;
  frequency: DonationFrequency;
  campaign?: string;
  isAnonymous: boolean;
  paymentMethod: string;
  paymentReference?: string;
  status: string;
  createdAt: Date;
  updatedAt: Date;
}

export enum DonationFrequency {
  ONE_TIME = 'ONE_TIME',
  MONTHLY = 'MONTHLY',
  QUARTERLY = 'QUARTERLY',
  ANNUALLY = 'ANNUALLY',
}

// Navigation Types
export interface TabParamList {
  Dashboard: undefined;
  Directory: undefined;
  Events: undefined;
  Feed: undefined;
  Profile: undefined;
}

export interface StackParamList {
  Main: undefined;
  Login: undefined;
  EventDetails: { eventId: string };
  AlumniProfile: { alumniId: string };
  PostDetails: { postId: string };
  Messages: undefined;
  MessageThread: { recipientId: string };
  Donations: undefined;
  Settings: undefined;
}
