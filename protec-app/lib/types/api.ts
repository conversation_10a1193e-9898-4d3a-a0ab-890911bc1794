// This file should import the AppRouter type from the web application
// For now, we'll create a placeholder that matches the web app structure

export interface AppRouter {
  alumni: any;
  events: any;
  posts: any;
  donations: any;
  users: any;
  messages: any;
  profile: any;
  onboarding: any;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Pagination Types
export interface PaginationParams {
  limit?: number;
  cursor?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  nextCursor?: string;
  hasMore: boolean;
  total?: number;
}
