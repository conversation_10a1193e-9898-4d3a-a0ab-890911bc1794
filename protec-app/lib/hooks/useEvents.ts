import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '../api/trpc';
import { useEventsStore } from '../stores/eventsStore';
import { useAuth } from '../providers/AuthProvider';

// Query keys
export const eventsKeys = {
  all: ['events'] as const,
  lists: () => [...eventsKeys.all, 'list'] as const,
  list: (filters: any) => [...eventsKeys.lists(), { filters }] as const,
  details: () => [...eventsKeys.all, 'detail'] as const,
  detail: (id: string) => [...eventsKeys.details(), id] as const,
  myEvents: () => [...eventsKeys.all, 'myEvents'] as const,
};

// Get all events with filters
export function useEvents(filters?: any) {
  const { setEvents, setLoading } = useEventsStore();
  const { isAuthenticated } = useAuth();

  return useQuery({
    queryKey: eventsKeys.list(filters),
    queryFn: async () => {
      setLoading(true);
      try {
        const result = await api.events.getAll.query({
          category: filters?.category,
          startDate: filters?.startDate,
          endDate: filters?.endDate,
          location: filters?.location,
          limit: filters?.limit || 20,
          cursor: filters?.cursor,
        });

        setEvents(result.events);
        return result;
      } finally {
        setLoading(false);
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: isAuthenticated,
  });
}

// Get single event
export function useEvent(id: string) {
  const { isAuthenticated } = useAuth();

  return useQuery({
    queryKey: eventsKeys.detail(id),
    queryFn: async () => {
      return await api.events.getById.query({ id });
    },
    enabled: !!id && isAuthenticated,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Get user's RSVP'd events
export function useMyRSVPs() {
  const { setMyEvents } = useEventsStore();
  const { isAuthenticated } = useAuth();

  return useQuery({
    queryKey: eventsKeys.myEvents(),
    queryFn: async () => {
      const rsvps = await api.events.getMyRSVPs.query();
      const events = rsvps.map(rsvp => rsvp.event);
      setMyEvents(events);
      return rsvps;
    },
    enabled: isAuthenticated,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

// Get events organized by user
export function useMyEvents() {
  const { isAuthenticated } = useAuth();

  return useQuery({
    queryKey: [...eventsKeys.all, 'organized'],
    queryFn: async () => {
      return await api.events.getMyEvents.query();
    },
    enabled: isAuthenticated,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// RSVP to event
export function useRSVPEvent() {
  const queryClient = useQueryClient();
  const { addToMyEvents, removeFromMyEvents } = useEventsStore();

  return useMutation({
    mutationFn: async ({ eventId, status }: { eventId: string; status: 'attending' | 'not_attending' | 'maybe' }) => {
      return await api.events.rsvp.mutate({ eventId, status });
    },
    onSuccess: (data, variables) => {
      // Invalidate events queries
      queryClient.invalidateQueries({ queryKey: eventsKeys.all });

      // Update my events based on RSVP status
      if (variables.status === 'attending') {
        const event = queryClient.getQueryData(eventsKeys.detail(variables.eventId));
        if (event) {
          addToMyEvents(event as any);
        }
      } else if (variables.status === 'not_attending') {
        removeFromMyEvents(variables.eventId);
      }
    },
  });
}

// Create new event
export function useCreateEvent() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (eventData: any) => {
      return await api.events.create.mutate({
        title: eventData.title,
        description: eventData.description,
        category: eventData.category,
        startTime: new Date(eventData.startTime),
        endTime: new Date(eventData.endTime),
        location: eventData.location,
      });
    },
    onSuccess: () => {
      // Invalidate events queries to refetch
      queryClient.invalidateQueries({ queryKey: eventsKeys.all });
    },
  });
}

// Update event
export function useUpdateEvent() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (eventData: any) => {
      return await api.events.update.mutate({
        id: eventData.id,
        title: eventData.title,
        description: eventData.description,
        category: eventData.category,
        startTime: eventData.startTime ? new Date(eventData.startTime) : undefined,
        endTime: eventData.endTime ? new Date(eventData.endTime) : undefined,
        location: eventData.location,
      });
    },
    onSuccess: (data, variables) => {
      // Invalidate events queries to refetch
      queryClient.invalidateQueries({ queryKey: eventsKeys.all });

      // Update specific event in cache
      queryClient.setQueryData(eventsKeys.detail(variables.id), data);
    },
  });
}

// Delete event
export function useDeleteEvent() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (eventId: string) => {
      return await api.events.delete.mutate({ id: eventId });
    },
    onSuccess: () => {
      // Invalidate events queries to refetch
      queryClient.invalidateQueries({ queryKey: eventsKeys.all });
    },
  });
}
