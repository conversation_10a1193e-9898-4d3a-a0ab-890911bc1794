import { useEffect, useCallback } from 'react';
import { AppState, AppStateStatus } from 'react-native';
import { useNotificationsStore } from '@/lib/stores/notificationsStore';
import { notificationService, NotificationData } from '@/lib/services/notificationService';

export interface UseNotificationsReturn {
  // State
  isInitialized: boolean;
  hasPermission: boolean;
  unreadCount: number;
  notifications: NotificationData[];
  
  // Actions
  requestPermissions: () => Promise<boolean>;
  sendTestNotification: (type: NotificationData['type']) => Promise<void>;
  markAsRead: (notificationId: string) => void;
  markAllAsRead: () => void;
  clearNotifications: () => void;
  
  // Preferences
  updatePreferences: (preferences: any) => Promise<void>;
  preferences: any;
}

export const useNotifications = (): UseNotificationsReturn => {
  const {
    isInitialized,
    permissionStatus,
    unreadCount,
    notifications,
    preferences,
    initializeNotifications,
    requestPermissions,
    sendTestNotification,
    markAsRead,
    markAllAsRead,
    clearNotifications,
    updatePreferences,
    updateBadgeCount,
  } = useNotificationsStore();

  // Initialize notifications when hook is first used
  useEffect(() => {
    if (!isInitialized) {
      initializeNotifications();
    }
  }, [isInitialized, initializeNotifications]);

  // Handle app state changes
  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (nextAppState === 'active') {
        // App became active, update badge count
        updateBadgeCount();
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, [updateBadgeCount]);

  // Cleanup notification service on unmount
  useEffect(() => {
    return () => {
      notificationService.cleanup();
    };
  }, []);

  const hasPermission = permissionStatus === 'granted';

  return {
    // State
    isInitialized,
    hasPermission,
    unreadCount,
    notifications,
    preferences,
    
    // Actions
    requestPermissions,
    sendTestNotification,
    markAsRead,
    markAllAsRead,
    clearNotifications,
    updatePreferences,
  };
};

// Hook for handling specific notification types
export const useNotificationHandler = () => {
  const { addNotification, preferences } = useNotificationsStore();

  const handleNewMessage = useCallback((messageData: {
    conversationId: string;
    senderName: string;
    content: string;
  }) => {
    if (!preferences.messages) return;

    const notification: NotificationData = {
      type: 'message',
      id: `message-${Date.now()}`,
      title: `New message from ${messageData.senderName}`,
      body: messageData.content,
      data: { conversationId: messageData.conversationId },
    };

    addNotification(notification);
  }, [addNotification, preferences.messages]);

  const handleEventReminder = useCallback((eventData: {
    eventId: string;
    title: string;
    startTime: Date;
  }) => {
    if (!preferences.events) return;

    const notification: NotificationData = {
      type: 'event',
      id: `event-${eventData.eventId}`,
      title: 'Event Reminder',
      body: `${eventData.title} starts soon`,
      data: { eventId: eventData.eventId },
    };

    addNotification(notification);
  }, [addNotification, preferences.events]);

  const handleConnectionRequest = useCallback((connectionData: {
    userId: string;
    userName: string;
  }) => {
    if (!preferences.connections) return;

    const notification: NotificationData = {
      type: 'connection',
      id: `connection-${connectionData.userId}`,
      title: 'New Connection Request',
      body: `${connectionData.userName} wants to connect with you`,
      data: { userId: connectionData.userId },
    };

    addNotification(notification);
  }, [addNotification, preferences.connections]);

  const handleDonationUpdate = useCallback((donationData: {
    campaignId: string;
    campaignTitle: string;
    message: string;
  }) => {
    if (!preferences.donations) return;

    const notification: NotificationData = {
      type: 'donation',
      id: `donation-${donationData.campaignId}`,
      title: donationData.campaignTitle,
      body: donationData.message,
      data: { campaignId: donationData.campaignId },
    };

    addNotification(notification);
  }, [addNotification, preferences.donations]);

  const handlePostInteraction = useCallback((postData: {
    postId: string;
    authorName: string;
    action: 'like' | 'comment';
  }) => {
    if (!preferences.posts) return;

    const actionText = postData.action === 'like' ? 'liked' : 'commented on';
    const notification: NotificationData = {
      type: 'post',
      id: `post-${postData.postId}-${Date.now()}`,
      title: 'Post Interaction',
      body: `${postData.authorName} ${actionText} your post`,
      data: { postId: postData.postId },
    };

    addNotification(notification);
  }, [addNotification, preferences.posts]);

  const handleGeneralNotification = useCallback((notificationData: {
    title: string;
    body: string;
    data?: Record<string, any>;
  }) => {
    if (!preferences.general) return;

    const notification: NotificationData = {
      type: 'general',
      id: `general-${Date.now()}`,
      title: notificationData.title,
      body: notificationData.body,
      data: notificationData.data,
    };

    addNotification(notification);
  }, [addNotification, preferences.general]);

  return {
    handleNewMessage,
    handleEventReminder,
    handleConnectionRequest,
    handleDonationUpdate,
    handlePostInteraction,
    handleGeneralNotification,
  };
};

// Hook for scheduling local notifications
export const useLocalNotifications = () => {
  const scheduleEventReminder = useCallback(async (eventData: {
    eventId: string;
    title: string;
    startTime: Date;
  }) => {
    // Schedule notification 1 hour before event
    const reminderTime = new Date(eventData.startTime.getTime() - 60 * 60 * 1000);
    
    if (reminderTime > new Date()) {
      const notification: NotificationData = {
        type: 'event',
        id: `reminder-${eventData.eventId}`,
        title: 'Event Reminder',
        body: `${eventData.title} starts in 1 hour`,
        data: { eventId: eventData.eventId },
      };

      await notificationService.scheduleLocalNotification(
        notification,
        { date: reminderTime }
      );
    }
  }, []);

  const scheduleDonationReminder = useCallback(async (campaignData: {
    campaignId: string;
    title: string;
    endDate: Date;
  }) => {
    // Schedule notification 3 days before campaign ends
    const reminderTime = new Date(campaignData.endDate.getTime() - 3 * 24 * 60 * 60 * 1000);
    
    if (reminderTime > new Date()) {
      const notification: NotificationData = {
        type: 'donation',
        id: `campaign-reminder-${campaignData.campaignId}`,
        title: 'Campaign Ending Soon',
        body: `${campaignData.title} ends in 3 days`,
        data: { campaignId: campaignData.campaignId },
      };

      await notificationService.scheduleLocalNotification(
        notification,
        { date: reminderTime }
      );
    }
  }, []);

  return {
    scheduleEventReminder,
    scheduleDonationReminder,
  };
};
