import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '../api/trpc';
import { useAlumniStore } from '../stores/alumniStore';
import { useAuth } from '../providers/AuthProvider';

// Query keys
export const alumniKeys = {
  all: ['alumni'] as const,
  lists: () => [...alumniKeys.all, 'list'] as const,
  list: (filters: any) => [...alumniKeys.lists(), { filters }] as const,
  details: () => [...alumniKeys.all, 'detail'] as const,
  detail: (id: string) => [...alumniKeys.details(), id] as const,
};

// Get all alumni with enhanced search and filters
export function useAlumni(filters?: any) {
  const { setAlumni, setLoading } = useAlumniStore();
  const { isAuthenticated } = useAuth();

  return useQuery({
    queryKey: alumniKeys.list(filters),
    queryFn: async () => {
      setLoading(true);
      try {
        const result = await api.alumni.enhancedSearch.query({
          ...filters,
          limit: filters?.limit || 20,
          sortBy: filters?.sortBy || 'name',
          sortOrder: filters?.sortOrder || 'asc',
        });

        setAlumni(result.alumni);
        return result;
      } finally {
        setLoading(false);
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: isAuthenticated,
  });
}

// Get alumni with basic search
export function useSearchAlumni(query: string, filters?: any) {
  const { isAuthenticated } = useAuth();

  return useQuery({
    queryKey: [...alumniKeys.lists(), 'search', query, filters],
    queryFn: async () => {
      if (!query.trim()) return { alumni: [], nextCursor: undefined };

      const result = await api.alumni.search.query({
        query: query.trim(),
        ...filters,
        limit: filters?.limit || 20,
      });

      return result;
    },
    enabled: query.length > 2 && isAuthenticated,
    staleTime: 30 * 1000, // 30 seconds
  });
}

// Get single alumni profile
export function useAlumniProfile(id: string) {
  const { isAuthenticated } = useAuth();

  return useQuery({
    queryKey: alumniKeys.detail(id),
    queryFn: async () => {
      return await api.alumni.getById.query({ id });
    },
    enabled: !!id && isAuthenticated,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Get current user's profile
export function useMyProfile() {
  const { isAuthenticated } = useAuth();

  return useQuery({
    queryKey: ['alumni', 'me'],
    queryFn: async () => {
      return await api.alumni.me.query();
    },
    enabled: isAuthenticated,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Update alumni profile
export function useUpdateAlumniProfile() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: any) => {
      return await api.alumni.update.mutate(data);
    },
    onSuccess: (data, variables) => {
      // Invalidate and refetch alumni queries
      queryClient.invalidateQueries({ queryKey: alumniKeys.all });
      queryClient.invalidateQueries({ queryKey: ['alumni', 'me'] });

      // Update specific alumni in cache if we have the ID
      if (variables.id) {
        queryClient.setQueryData(
          alumniKeys.detail(variables.id),
          data
        );
      }
    },
  });
}

// Get alumni recommendations
export function useAlumniRecommendations(options?: {
  limit?: number;
  excludeConnected?: boolean;
  minScore?: number;
  focusAreas?: string[];
}) {
  const { isAuthenticated } = useAuth();

  return useQuery({
    queryKey: ['alumni', 'recommendations', options],
    queryFn: async () => {
      return await api.alumni.getRecommendations.query({
        limit: options?.limit || 10,
        excludeConnected: options?.excludeConnected ?? true,
        minScore: options?.minScore || 0.3,
        focusAreas: options?.focusAreas as any,
      });
    },
    enabled: isAuthenticated,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Get trending alumni
export function useTrendingAlumni(limit: number = 10) {
  return useQuery({
    queryKey: ['alumni', 'trending', limit],
    queryFn: async () => {
      return await api.alumni.getTrending.query({ limit });
    },
    staleTime: 30 * 60 * 1000, // 30 minutes
  });
}

// Connection management hooks
export function useConnectAlumni() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ alumniId, message }: { alumniId: string; message?: string }) => {
      return await api.alumni.sendConnectionRequest.mutate({ alumniId, message });
    },
    onSuccess: () => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['alumni'] });
      queryClient.invalidateQueries({ queryKey: ['connections'] });
    },
  });
}

export function useDisconnectAlumni() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ alumniId }: { alumniId: string }) => {
      return await api.alumni.disconnect.mutate({ alumniId });
    },
    onSuccess: () => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['alumni'] });
      queryClient.invalidateQueries({ queryKey: ['connections'] });
    },
  });
}

export function useRespondToConnectionRequest() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ requestId, action }: { requestId: string; action: 'accept' | 'decline' }) => {
      return await api.alumni.respondToConnectionRequest.mutate({ requestId, action });
    },
    onSuccess: () => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['alumni'] });
      queryClient.invalidateQueries({ queryKey: ['connections'] });
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
    },
  });
}
