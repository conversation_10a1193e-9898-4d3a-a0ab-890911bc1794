/**
 * Error Handling Utilities
 * 
 * Comprehensive error handling system with user-friendly messages,
 * logging, and recovery strategies.
 */

export interface AppError {
  code: string;
  message: string;
  userMessage: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  recoverable: boolean;
  metadata?: Record<string, any>;
}

export interface ErrorHandlerOptions {
  showToUser?: boolean;
  logError?: boolean;
  reportToService?: boolean;
  fallbackAction?: () => void;
}

/**
 * Error codes and their corresponding user-friendly messages
 */
export const ERROR_CODES = {
  // Network errors
  NETWORK_ERROR: {
    code: 'NETWORK_ERROR',
    message: 'Network request failed',
    userMessage: 'Please check your internet connection and try again.',
    severity: 'medium' as const,
    recoverable: true,
  },
  TIMEOUT_ERROR: {
    code: 'TIMEOUT_ERROR',
    message: 'Request timed out',
    userMessage: 'The request is taking longer than expected. Please try again.',
    severity: 'medium' as const,
    recoverable: true,
  },
  SERVER_ERROR: {
    code: 'SERVER_ERROR',
    message: 'Server error occurred',
    userMessage: 'Something went wrong on our end. Please try again later.',
    severity: 'high' as const,
    recoverable: true,
  },

  // Authentication errors
  AUTH_INVALID_EMAIL: {
    code: 'AUTH_INVALID_EMAIL',
    message: 'Invalid email format',
    userMessage: 'Please enter a valid email address.',
    severity: 'low' as const,
    recoverable: true,
  },
  AUTH_EMAIL_NOT_FOUND: {
    code: 'AUTH_EMAIL_NOT_FOUND',
    message: 'Email not found',
    userMessage: 'No account found with this email address.',
    severity: 'medium' as const,
    recoverable: true,
  },
  AUTH_MAGIC_LINK_EXPIRED: {
    code: 'AUTH_MAGIC_LINK_EXPIRED',
    message: 'Magic link expired',
    userMessage: 'This magic link has expired. Please request a new one.',
    severity: 'medium' as const,
    recoverable: true,
  },
  AUTH_MAGIC_LINK_INVALID: {
    code: 'AUTH_MAGIC_LINK_INVALID',
    message: 'Invalid magic link',
    userMessage: 'This magic link is invalid. Please request a new one.',
    severity: 'medium' as const,
    recoverable: true,
  },
  AUTH_RATE_LIMITED: {
    code: 'AUTH_RATE_LIMITED',
    message: 'Too many requests',
    userMessage: 'Too many attempts. Please wait a few minutes before trying again.',
    severity: 'medium' as const,
    recoverable: true,
  },

  // Validation errors
  VALIDATION_REQUIRED_FIELD: {
    code: 'VALIDATION_REQUIRED_FIELD',
    message: 'Required field missing',
    userMessage: 'Please fill in all required fields.',
    severity: 'low' as const,
    recoverable: true,
  },
  VALIDATION_INVALID_FORMAT: {
    code: 'VALIDATION_INVALID_FORMAT',
    message: 'Invalid format',
    userMessage: 'Please check the format of your input.',
    severity: 'low' as const,
    recoverable: true,
  },

  // Permission errors
  PERMISSION_DENIED: {
    code: 'PERMISSION_DENIED',
    message: 'Permission denied',
    userMessage: 'You don\'t have permission to perform this action.',
    severity: 'medium' as const,
    recoverable: false,
  },

  // Generic errors
  UNKNOWN_ERROR: {
    code: 'UNKNOWN_ERROR',
    message: 'Unknown error occurred',
    userMessage: 'Something unexpected happened. Please try again.',
    severity: 'medium' as const,
    recoverable: true,
  },
} as const;

/**
 * Create an AppError from various input types
 */
export const createError = (
  input: string | Error | AppError | any,
  metadata?: Record<string, any>
): AppError => {
  // If it's already an AppError, return it
  if (input && typeof input === 'object' && 'code' in input && 'userMessage' in input) {
    return { ...input, metadata: { ...input.metadata, ...metadata } };
  }

  // If it's a standard Error
  if (input instanceof Error) {
    return {
      ...ERROR_CODES.UNKNOWN_ERROR,
      message: input.message,
      metadata: { originalError: input, ...metadata },
    };
  }

  // If it's a string
  if (typeof input === 'string') {
    // Try to match with known error codes
    const errorCode = Object.values(ERROR_CODES).find(
      error => error.message.toLowerCase().includes(input.toLowerCase())
    );

    if (errorCode) {
      return { ...errorCode, metadata };
    }

    return {
      ...ERROR_CODES.UNKNOWN_ERROR,
      message: input,
      metadata,
    };
  }

  // For any other type, return unknown error
  return {
    ...ERROR_CODES.UNKNOWN_ERROR,
    metadata: { originalInput: input, ...metadata },
  };
};

/**
 * Handle network errors specifically
 */
export const handleNetworkError = (error: any): AppError => {
  if (!error) {
    return createError(ERROR_CODES.UNKNOWN_ERROR);
  }

  // Check for specific network error types
  if (error.code === 'NETWORK_ERROR' || error.message?.includes('Network')) {
    return createError(ERROR_CODES.NETWORK_ERROR);
  }

  if (error.code === 'TIMEOUT' || error.message?.includes('timeout')) {
    return createError(ERROR_CODES.TIMEOUT_ERROR);
  }

  // Check HTTP status codes
  if (error.status || error.statusCode) {
    const status = error.status || error.statusCode;
    
    if (status >= 500) {
      return createError(ERROR_CODES.SERVER_ERROR);
    }
    
    if (status === 429) {
      return createError(ERROR_CODES.AUTH_RATE_LIMITED);
    }
    
    if (status === 403) {
      return createError(ERROR_CODES.PERMISSION_DENIED);
    }
  }

  return createError(error);
};

/**
 * Handle authentication errors specifically
 */
export const handleAuthError = (error: any): AppError => {
  if (!error) {
    return createError(ERROR_CODES.UNKNOWN_ERROR);
  }

  const message = error.message || error.error || '';

  if (message.includes('email') && message.includes('invalid')) {
    return createError(ERROR_CODES.AUTH_INVALID_EMAIL);
  }

  if (message.includes('email') && message.includes('not found')) {
    return createError(ERROR_CODES.AUTH_EMAIL_NOT_FOUND);
  }

  if (message.includes('expired')) {
    return createError(ERROR_CODES.AUTH_MAGIC_LINK_EXPIRED);
  }

  if (message.includes('invalid') && message.includes('link')) {
    return createError(ERROR_CODES.AUTH_MAGIC_LINK_INVALID);
  }

  if (message.includes('rate') || message.includes('too many')) {
    return createError(ERROR_CODES.AUTH_RATE_LIMITED);
  }

  return handleNetworkError(error);
};

/**
 * Log error for debugging and monitoring
 */
export const logError = (error: AppError, context?: string) => {
  const logData = {
    timestamp: new Date().toISOString(),
    context,
    error: {
      code: error.code,
      message: error.message,
      userMessage: error.userMessage,
      severity: error.severity,
      recoverable: error.recoverable,
      metadata: error.metadata,
    },
  };

  // In development, log to console
  if (__DEV__) {
    console.group(`🚨 Error: ${error.code}`);
    console.error('Message:', error.message);
    console.error('User Message:', error.userMessage);
    console.error('Severity:', error.severity);
    console.error('Recoverable:', error.recoverable);
    if (error.metadata) {
      console.error('Metadata:', error.metadata);
    }
    if (context) {
      console.error('Context:', context);
    }
    console.groupEnd();
  }

  // In production, you would send this to your error reporting service
  // Example: Sentry, Bugsnag, etc.
  // errorReportingService.captureError(logData);
};

/**
 * Main error handler function
 */
export const handleError = (
  error: any,
  options: ErrorHandlerOptions = {},
  context?: string
): AppError => {
  const {
    showToUser = true,
    logError: shouldLog = true,
    reportToService = true,
    fallbackAction,
  } = options;

  // Create standardized error
  const appError = createError(error);

  // Log error if requested
  if (shouldLog) {
    logError(appError, context);
  }

  // Execute fallback action if provided
  if (fallbackAction) {
    try {
      fallbackAction();
    } catch (fallbackError) {
      logError(createError(fallbackError), `Fallback action failed for ${context}`);
    }
  }

  return appError;
};

/**
 * Retry mechanism for recoverable errors
 */
export const withRetry = async <T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> => {
  let lastError: any;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      const appError = createError(error);

      // Don't retry if error is not recoverable
      if (!appError.recoverable) {
        throw appError;
      }

      // Don't retry on last attempt
      if (attempt === maxRetries) {
        throw appError;
      }

      // Wait before retrying (exponential backoff)
      await new Promise(resolve => setTimeout(resolve, delay * attempt));
    }
  }

  throw createError(lastError);
};

/**
 * Error boundary helper for React components
 */
export const createErrorBoundaryHandler = (componentName: string) => {
  return (error: Error, errorInfo: any) => {
    const appError = handleError(error, {
      showToUser: false,
      logError: true,
      reportToService: true,
    }, `ErrorBoundary: ${componentName}`);

    return appError;
  };
};
