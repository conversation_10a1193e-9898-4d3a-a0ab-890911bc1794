/**
 * Form Validation Utilities
 *
 * Comprehensive validation functions for forms with proper error messaging
 * and user experience patterns.
 */

import React from 'react';

export interface ValidationResult {
  isValid: boolean;
  error?: string;
}

export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: string) => ValidationResult;
}

export interface FormValidationRules {
  [fieldName: string]: ValidationRule;
}

export interface FormErrors {
  [fieldName: string]: string;
}

export interface FormValues {
  [fieldName: string]: string;
}

/**
 * Email validation with comprehensive checks
 */
export const validateEmail = (email: string): ValidationResult => {
  if (!email || !email.trim()) {
    return {
      isValid: false,
      error: 'Email address is required',
    };
  }

  const trimmedEmail = email.trim();

  // Basic format check
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(trimmedEmail)) {
    return {
      isValid: false,
      error: 'Please enter a valid email address',
    };
  }

  // Length check
  if (trimmedEmail.length > 254) {
    return {
      isValid: false,
      error: 'Email address is too long',
    };
  }

  // Local part length check (before @)
  const localPart = trimmedEmail.split('@')[0];
  if (localPart.length > 64) {
    return {
      isValid: false,
      error: 'Email address is invalid',
    };
  }

  // Common typos check
  const commonDomains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com'];
  const domain = trimmedEmail.split('@')[1]?.toLowerCase();
  
  if (domain) {
    // Check for common typos
    const typoChecks = [
      { typo: 'gmial.com', correct: 'gmail.com' },
      { typo: 'gmai.com', correct: 'gmail.com' },
      { typo: 'yahooo.com', correct: 'yahoo.com' },
      { typo: 'hotmial.com', correct: 'hotmail.com' },
    ];

    for (const check of typoChecks) {
      if (domain === check.typo) {
        return {
          isValid: false,
          error: `Did you mean ${check.correct}?`,
        };
      }
    }
  }

  return { isValid: true };
};

/**
 * Password validation with strength requirements
 */
export const validatePassword = (password: string): ValidationResult => {
  if (!password) {
    return {
      isValid: false,
      error: 'Password is required',
    };
  }

  if (password.length < 8) {
    return {
      isValid: false,
      error: 'Password must be at least 8 characters long',
    };
  }

  if (password.length > 128) {
    return {
      isValid: false,
      error: 'Password is too long',
    };
  }

  // Check for at least one uppercase letter
  if (!/[A-Z]/.test(password)) {
    return {
      isValid: false,
      error: 'Password must contain at least one uppercase letter',
    };
  }

  // Check for at least one lowercase letter
  if (!/[a-z]/.test(password)) {
    return {
      isValid: false,
      error: 'Password must contain at least one lowercase letter',
    };
  }

  // Check for at least one number
  if (!/\d/.test(password)) {
    return {
      isValid: false,
      error: 'Password must contain at least one number',
    };
  }

  // Check for at least one special character
  if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    return {
      isValid: false,
      error: 'Password must contain at least one special character',
    };
  }

  return { isValid: true };
};

/**
 * Name validation
 */
export const validateName = (name: string): ValidationResult => {
  if (!name || !name.trim()) {
    return {
      isValid: false,
      error: 'Name is required',
    };
  }

  const trimmedName = name.trim();

  if (trimmedName.length < 2) {
    return {
      isValid: false,
      error: 'Name must be at least 2 characters long',
    };
  }

  if (trimmedName.length > 50) {
    return {
      isValid: false,
      error: 'Name is too long',
    };
  }

  // Check for valid characters (letters, spaces, hyphens, apostrophes)
  if (!/^[a-zA-Z\s\-']+$/.test(trimmedName)) {
    return {
      isValid: false,
      error: 'Name can only contain letters, spaces, hyphens, and apostrophes',
    };
  }

  return { isValid: true };
};

/**
 * Phone number validation
 */
export const validatePhoneNumber = (phone: string): ValidationResult => {
  if (!phone || !phone.trim()) {
    return {
      isValid: false,
      error: 'Phone number is required',
    };
  }

  // Remove all non-digit characters for validation
  const digitsOnly = phone.replace(/\D/g, '');

  if (digitsOnly.length < 10) {
    return {
      isValid: false,
      error: 'Phone number must be at least 10 digits',
    };
  }

  if (digitsOnly.length > 15) {
    return {
      isValid: false,
      error: 'Phone number is too long',
    };
  }

  return { isValid: true };
};

/**
 * Generic field validation based on rules
 */
export const validateField = (value: string, rules: ValidationRule): ValidationResult => {
  // Required check
  if (rules.required && (!value || !value.trim())) {
    return {
      isValid: false,
      error: 'This field is required',
    };
  }

  // Skip other validations if field is empty and not required
  if (!value || !value.trim()) {
    return { isValid: true };
  }

  const trimmedValue = value.trim();

  // Length checks
  if (rules.minLength && trimmedValue.length < rules.minLength) {
    return {
      isValid: false,
      error: `Must be at least ${rules.minLength} characters long`,
    };
  }

  if (rules.maxLength && trimmedValue.length > rules.maxLength) {
    return {
      isValid: false,
      error: `Must be no more than ${rules.maxLength} characters long`,
    };
  }

  // Pattern check
  if (rules.pattern && !rules.pattern.test(trimmedValue)) {
    return {
      isValid: false,
      error: 'Invalid format',
    };
  }

  // Custom validation
  if (rules.custom) {
    return rules.custom(trimmedValue);
  }

  return { isValid: true };
};

/**
 * Validate entire form based on rules
 */
export const validateForm = (values: FormValues, rules: FormValidationRules): {
  isValid: boolean;
  errors: FormErrors;
} => {
  const errors: FormErrors = {};
  let isValid = true;

  for (const [fieldName, fieldRules] of Object.entries(rules)) {
    const value = values[fieldName] || '';
    const result = validateField(value, fieldRules);
    
    if (!result.isValid) {
      errors[fieldName] = result.error || 'Invalid value';
      isValid = false;
    }
  }

  return { isValid, errors };
};

/**
 * Real-time validation hook for forms
 */
export const useFormValidation = (initialValues: FormValues, rules: FormValidationRules) => {
  const [values, setValues] = React.useState(initialValues);
  const [errors, setErrors] = React.useState<FormErrors>({});
  const [touched, setTouched] = React.useState<{ [key: string]: boolean }>({});

  const validateSingleField = (fieldName: string, value: string) => {
    const fieldRules = rules[fieldName];
    if (!fieldRules) return;

    const result = validateField(value, fieldRules);
    setErrors(prev => ({
      ...prev,
      [fieldName]: result.isValid ? '' : (result.error || 'Invalid value'),
    }));
  };

  const setValue = (fieldName: string, value: string) => {
    setValues(prev => ({ ...prev, [fieldName]: value }));
    
    // Validate on change if field has been touched
    if (touched[fieldName]) {
      validateSingleField(fieldName, value);
    }
  };

  const setFieldTouched = (fieldName: string) => {
    setTouched(prev => ({ ...prev, [fieldName]: true }));
    validateSingleField(fieldName, values[fieldName] || '');
  };

  const validateAll = () => {
    const result = validateForm(values, rules);
    setErrors(result.errors);
    
    // Mark all fields as touched
    const allTouched = Object.keys(rules).reduce((acc, key) => {
      acc[key] = true;
      return acc;
    }, {} as { [key: string]: boolean });
    setTouched(allTouched);

    return result.isValid;
  };

  const reset = () => {
    setValues(initialValues);
    setErrors({});
    setTouched({});
  };

  return {
    values,
    errors,
    touched,
    setValue,
    setFieldTouched,
    validateAll,
    reset,
    isValid: Object.values(errors).every(error => !error),
  };
};

// Export commonly used validation rules
export const commonValidationRules = {
  email: {
    required: true,
    custom: validateEmail,
  },
  password: {
    required: true,
    custom: validatePassword,
  },
  name: {
    required: true,
    custom: validateName,
  },
  phoneNumber: {
    required: true,
    custom: validatePhoneNumber,
  },
} as const;
