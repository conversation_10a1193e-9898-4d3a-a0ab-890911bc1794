import AsyncStorage from '@react-native-async-storage/async-storage';

interface CacheItem<T> {
  data: T;
  timestamp: number;
  expiresIn: number; // milliseconds
}

class CacheManager {
  private prefix = 'protec_cache_';

  async set<T>(key: string, data: T, expiresIn: number = 5 * 60 * 1000): Promise<void> {
    try {
      const cacheItem: CacheItem<T> = {
        data,
        timestamp: Date.now(),
        expiresIn,
      };
      
      await AsyncStorage.setItem(
        `${this.prefix}${key}`,
        JSON.stringify(cacheItem)
      );
    } catch (error) {
      console.error('Cache set error:', error);
    }
  }

  async get<T>(key: string): Promise<T | null> {
    try {
      const cached = await AsyncStorage.getItem(`${this.prefix}${key}`);
      if (!cached) return null;

      const cacheItem: CacheItem<T> = JSON.parse(cached);
      const now = Date.now();
      
      // Check if cache has expired
      if (now - cacheItem.timestamp > cacheItem.expiresIn) {
        await this.remove(key);
        return null;
      }

      return cacheItem.data;
    } catch (error) {
      console.error('Cache get error:', error);
      return null;
    }
  }

  async remove(key: string): Promise<void> {
    try {
      await AsyncStorage.removeItem(`${this.prefix}${key}`);
    } catch (error) {
      console.error('Cache remove error:', error);
    }
  }

  async clear(): Promise<void> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith(this.prefix));
      await AsyncStorage.multiRemove(cacheKeys);
    } catch (error) {
      console.error('Cache clear error:', error);
    }
  }

  async isExpired(key: string): Promise<boolean> {
    try {
      const cached = await AsyncStorage.getItem(`${this.prefix}${key}`);
      if (!cached) return true;

      const cacheItem: CacheItem<any> = JSON.parse(cached);
      const now = Date.now();
      
      return now - cacheItem.timestamp > cacheItem.expiresIn;
    } catch (error) {
      console.error('Cache isExpired error:', error);
      return true;
    }
  }

  async getSize(): Promise<number> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith(this.prefix));
      return cacheKeys.length;
    } catch (error) {
      console.error('Cache getSize error:', error);
      return 0;
    }
  }
}

export const cache = new CacheManager();

// Cache keys for different data types
export const cacheKeys = {
  alumni: (filters?: any) => `alumni_${JSON.stringify(filters || {})}`,
  alumniProfile: (id: string) => `alumni_profile_${id}`,
  events: (filters?: any) => `events_${JSON.stringify(filters || {})}`,
  eventDetail: (id: string) => `event_detail_${id}`,
  posts: (filters?: any) => `posts_${JSON.stringify(filters || {})}`,
  userProfile: (id: string) => `user_profile_${id}`,
  myEvents: () => 'my_events',
  myPosts: () => 'my_posts',
};

// Cache durations (in milliseconds)
export const cacheDurations = {
  short: 2 * 60 * 1000,      // 2 minutes
  medium: 5 * 60 * 1000,     // 5 minutes
  long: 15 * 60 * 1000,      // 15 minutes
  veryLong: 60 * 60 * 1000,  // 1 hour
};
