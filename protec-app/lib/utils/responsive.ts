/**
 * Responsive Design Utilities
 * 
 * Utilities for creating responsive layouts that work across different screen sizes
 * and orientations.
 */

import { Dimensions, Platform, StatusBar } from 'react-native';
import { DesignSystem } from '@/constants/DesignSystem';

/**
 * Get current screen dimensions
 */
export const getScreenDimensions = () => {
  const { width, height } = Dimensions.get('window');
  const screenData = Dimensions.get('screen');
  
  return {
    window: { width, height },
    screen: screenData,
    isLandscape: width > height,
    isPortrait: height > width,
  };
};

/**
 * Device type detection
 */
export const getDeviceType = () => {
  const { width, height } = getScreenDimensions().window;
  const minDimension = Math.min(width, height);
  const maxDimension = Math.max(width, height);
  
  // Tablet detection (simplified)
  const isTablet = minDimension >= 600;
  
  // Phone size categories
  const isSmallPhone = maxDimension < 667; // iPhone SE and smaller
  const isRegularPhone = maxDimension >= 667 && maxDimension < 812; // iPhone 8 and similar
  const isLargePhone = maxDimension >= 812; // iPhone X and larger
  
  return {
    isTablet,
    isPhone: !isTablet,
    isSmallPhone: !isTablet && isSmallPhone,
    isRegularPhone: !isTablet && isRegularPhone,
    isLargePhone: !isTablet && isLargePhone,
    platform: Platform.OS,
  };
};

/**
 * Breakpoint utilities
 */
export const breakpoints = {
  sm: DesignSystem.Breakpoints.sm,
  md: DesignSystem.Breakpoints.md,
  lg: DesignSystem.Breakpoints.lg,
  xl: DesignSystem.Breakpoints.xl,
};

export const useBreakpoint = () => {
  const { width } = getScreenDimensions().window;
  
  return {
    isSm: width >= breakpoints.sm,
    isMd: width >= breakpoints.md,
    isLg: width >= breakpoints.lg,
    isXl: width >= breakpoints.xl,
    current: width >= breakpoints.xl ? 'xl' 
           : width >= breakpoints.lg ? 'lg'
           : width >= breakpoints.md ? 'md'
           : width >= breakpoints.sm ? 'sm'
           : 'xs',
  };
};

/**
 * Responsive spacing utilities
 */
export const getResponsiveSpacing = (
  base: number,
  scale: { sm?: number; md?: number; lg?: number; xl?: number } = {}
) => {
  const { current } = useBreakpoint();
  
  switch (current) {
    case 'xl':
      return scale.xl || scale.lg || scale.md || scale.sm || base;
    case 'lg':
      return scale.lg || scale.md || scale.sm || base;
    case 'md':
      return scale.md || scale.sm || base;
    case 'sm':
      return scale.sm || base;
    default:
      return base;
  }
};

/**
 * Responsive font size utilities
 */
export const getResponsiveFontSize = (
  base: number,
  scale: { sm?: number; md?: number; lg?: number; xl?: number } = {}
) => {
  const deviceType = getDeviceType();
  const { current } = useBreakpoint();
  
  // Base scaling for device type
  let scaleFactor = 1;
  if (deviceType.isTablet) {
    scaleFactor = 1.2;
  } else if (deviceType.isSmallPhone) {
    scaleFactor = 0.9;
  }
  
  // Breakpoint-specific scaling
  const breakpointSize = getResponsiveSpacing(base, scale);
  
  return Math.round(breakpointSize * scaleFactor);
};

/**
 * Safe area utilities
 */
export const getSafeAreaInsets = () => {
  const { height, width } = getScreenDimensions().window;
  const deviceType = getDeviceType();
  
  // Simplified safe area calculation
  // In production, use react-native-safe-area-context
  let top = 0;
  let bottom = 0;
  
  if (Platform.OS === 'ios') {
    // iPhone X and newer have notches
    if (deviceType.isLargePhone && height >= 812) {
      top = 44;
      bottom = 34;
    } else {
      top = 20;
      bottom = 0;
    }
  } else {
    // Android
    top = StatusBar.currentHeight || 0;
    bottom = 0;
  }
  
  return { top, bottom, left: 0, right: 0 };
};

/**
 * Container width utilities
 */
export const getContainerWidth = (maxWidth?: keyof typeof DesignSystem.Layout.container) => {
  const { width } = getScreenDimensions().window;
  
  if (!maxWidth) {
    return width - (DesignSystem.Layout.screenPadding.horizontal * 2);
  }
  
  const containerMaxWidth = DesignSystem.Layout.container[maxWidth];
  return Math.min(width - (DesignSystem.Layout.screenPadding.horizontal * 2), containerMaxWidth);
};

/**
 * Grid utilities
 */
export const getGridColumns = (
  itemWidth: number,
  gap: number = DesignSystem.Spacing.md,
  minColumns: number = 1,
  maxColumns: number = 4
) => {
  const containerWidth = getContainerWidth();
  const availableWidth = containerWidth - (gap * (maxColumns - 1));
  const possibleColumns = Math.floor(availableWidth / itemWidth);
  
  return Math.max(minColumns, Math.min(maxColumns, possibleColumns));
};

/**
 * Responsive layout utilities
 */
export const getResponsiveLayout = () => {
  const deviceType = getDeviceType();
  const { isLandscape } = getScreenDimensions();
  const safeArea = getSafeAreaInsets();
  
  return {
    // Header heights
    headerHeight: deviceType.isTablet ? DesignSystem.Layout.header.large : DesignSystem.Layout.header.default,
    
    // Content padding
    contentPadding: {
      horizontal: deviceType.isTablet ? DesignSystem.Spacing['4xl'] : DesignSystem.Layout.screenPadding.horizontal,
      vertical: DesignSystem.Layout.screenPadding.vertical,
    },
    
    // Modal sizing
    modalWidth: deviceType.isTablet ? DesignSystem.Components.modal.maxWidth : '90%',
    
    // Form layout
    formMaxWidth: deviceType.isTablet ? 400 : '100%',
    
    // Button sizing
    buttonHeight: deviceType.isSmallPhone 
      ? DesignSystem.Components.button.height.sm 
      : DesignSystem.Components.button.height.md,
    
    // Input sizing
    inputHeight: deviceType.isSmallPhone ? 40 : DesignSystem.Components.input.height,
    
    // Safe areas
    safeArea,
    
    // Layout direction
    isLandscape,
    shouldUseHorizontalLayout: deviceType.isTablet && isLandscape,
  };
};

/**
 * Platform-specific utilities
 */
export const getPlatformStyles = () => {
  const isIOS = Platform.OS === 'ios';
  const isAndroid = Platform.OS === 'android';
  
  return {
    // Shadow styles
    shadow: isIOS ? DesignSystem.Shadows.md : { elevation: DesignSystem.Shadows.md.elevation },
    
    // Border radius adjustments
    borderRadius: isAndroid ? DesignSystem.BorderRadius.md - 2 : DesignSystem.BorderRadius.md,
    
    // Font weight adjustments
    fontWeight: {
      normal: isIOS ? '400' : '400',
      medium: isIOS ? '500' : '500',
      semibold: isIOS ? '600' : '700', // Android needs heavier weight
      bold: isIOS ? '700' : '700',
    },
    
    // Button styles
    button: {
      minHeight: isAndroid ? 48 : 44, // Android Material Design guidelines
    },
    
    // Input styles
    input: {
      fontSize: isAndroid ? 16 : 16, // Prevent zoom on iOS
    },
  };
};

/**
 * Responsive image utilities
 */
export const getResponsiveImageSize = (
  baseWidth: number,
  baseHeight: number,
  maxWidth?: number
) => {
  const containerWidth = getContainerWidth();
  const deviceType = getDeviceType();
  
  let targetWidth = baseWidth;
  let targetHeight = baseHeight;
  
  // Scale for device type
  if (deviceType.isTablet) {
    targetWidth *= 1.5;
    targetHeight *= 1.5;
  } else if (deviceType.isSmallPhone) {
    targetWidth *= 0.8;
    targetHeight *= 0.8;
  }
  
  // Respect max width
  const finalMaxWidth = maxWidth || containerWidth;
  if (targetWidth > finalMaxWidth) {
    const scale = finalMaxWidth / targetWidth;
    targetWidth = finalMaxWidth;
    targetHeight *= scale;
  }
  
  return {
    width: Math.round(targetWidth),
    height: Math.round(targetHeight),
  };
};

/**
 * Responsive text utilities
 */
export const getResponsiveTextProps = (variant: keyof typeof DesignSystem.Typography.fontSize) => {
  const baseFontSize = DesignSystem.Typography.fontSize[variant];
  const responsiveFontSize = getResponsiveFontSize(baseFontSize);
  const deviceType = getDeviceType();
  
  return {
    fontSize: responsiveFontSize,
    lineHeight: responsiveFontSize * DesignSystem.Typography.lineHeight.normal,
    // Adjust letter spacing for larger screens
    letterSpacing: deviceType.isTablet ? 0.5 : 0,
  };
};

/**
 * Hook for responsive values
 */
export const useResponsiveValue = <T>(values: {
  xs?: T;
  sm?: T;
  md?: T;
  lg?: T;
  xl?: T;
  default: T;
}): T => {
  const { current } = useBreakpoint();
  
  return values[current] || values.default;
};

/**
 * Orientation utilities
 */
export const useOrientation = () => {
  const { isLandscape, isPortrait } = getScreenDimensions();
  
  return {
    isLandscape,
    isPortrait,
    // Get orientation-specific values
    getValue: <T>(portrait: T, landscape: T): T => isLandscape ? landscape : portrait,
  };
};
