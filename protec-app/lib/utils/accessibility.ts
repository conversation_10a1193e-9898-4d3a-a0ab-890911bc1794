/**
 * Accessibility Utilities
 * 
 * Comprehensive accessibility utilities for screen readers, keyboard navigation,
 * and inclusive design patterns.
 */

import { AccessibilityInfo, Platform } from 'react-native';
import { DesignSystem } from '@/constants/DesignSystem';

/**
 * Accessibility roles for different UI elements
 */
export const AccessibilityRoles = {
  button: 'button',
  link: 'link',
  text: 'text',
  heading: 'header',
  image: 'image',
  textInput: 'textbox',
  search: 'search',
  list: 'list',
  listItem: 'listitem',
  tab: 'tab',
  tabList: 'tablist',
  menu: 'menu',
  menuItem: 'menuitem',
  alert: 'alert',
  dialog: 'dialog',
  progressBar: 'progressbar',
  switch: 'switch',
  checkbox: 'checkbox',
  radio: 'radio',
} as const;

/**
 * Accessibility states for dynamic content
 */
export const AccessibilityStates = {
  disabled: { disabled: true },
  selected: { selected: true },
  checked: { checked: true },
  expanded: { expanded: true },
  busy: { busy: true },
} as const;

/**
 * Common accessibility props for different component types
 */
export const getAccessibilityProps = {
  button: (label: string, hint?: string, disabled?: boolean) => ({
    accessible: true,
    accessibilityRole: AccessibilityRoles.button,
    accessibilityLabel: label,
    accessibilityHint: hint,
    accessibilityState: disabled ? AccessibilityStates.disabled : undefined,
  }),

  textInput: (label: string, value?: string, error?: string, required?: boolean) => ({
    accessible: true,
    accessibilityRole: AccessibilityRoles.textInput,
    accessibilityLabel: label + (required ? ' (required)' : ''),
    accessibilityValue: value ? { text: value } : undefined,
    accessibilityHint: error || undefined,
    accessibilityState: error ? { invalid: true } : undefined,
  }),

  heading: (text: string, level: 1 | 2 | 3 | 4 | 5 | 6 = 1) => ({
    accessible: true,
    accessibilityRole: AccessibilityRoles.heading,
    accessibilityLabel: text,
    ...(Platform.OS === 'ios' && { accessibilityTraits: ['header'] }),
    ...(Platform.OS === 'android' && { accessibilityLevel: level }),
  }),

  image: (description: string, decorative?: boolean) => ({
    accessible: !decorative,
    accessibilityRole: decorative ? undefined : AccessibilityRoles.image,
    accessibilityLabel: decorative ? undefined : description,
  }),

  link: (label: string, hint?: string) => ({
    accessible: true,
    accessibilityRole: AccessibilityRoles.link,
    accessibilityLabel: label,
    accessibilityHint: hint || 'Double tap to open',
  }),

  list: (itemCount?: number) => ({
    accessible: true,
    accessibilityRole: AccessibilityRoles.list,
    accessibilityHint: itemCount ? `List with ${itemCount} items` : undefined,
  }),

  listItem: (label: string, position?: { index: number; total: number }) => ({
    accessible: true,
    accessibilityRole: AccessibilityRoles.listItem,
    accessibilityLabel: position 
      ? `${label}, ${position.index + 1} of ${position.total}`
      : label,
  }),

  tab: (label: string, selected?: boolean, position?: { index: number; total: number }) => ({
    accessible: true,
    accessibilityRole: AccessibilityRoles.tab,
    accessibilityLabel: label,
    accessibilityState: selected ? AccessibilityStates.selected : undefined,
    accessibilityHint: position 
      ? `Tab ${position.index + 1} of ${position.total}`
      : undefined,
  }),

  alert: (message: string, type: 'error' | 'warning' | 'info' | 'success' = 'info') => ({
    accessible: true,
    accessibilityRole: AccessibilityRoles.alert,
    accessibilityLabel: `${type}: ${message}`,
    accessibilityLiveRegion: 'assertive' as const,
  }),

  progressBar: (value: number, max: number = 100, label?: string) => ({
    accessible: true,
    accessibilityRole: AccessibilityRoles.progressBar,
    accessibilityLabel: label || 'Progress',
    accessibilityValue: {
      min: 0,
      max,
      now: value,
      text: `${Math.round((value / max) * 100)}%`,
    },
  }),
} as const;

/**
 * Check if screen reader is enabled
 */
export const isScreenReaderEnabled = async (): Promise<boolean> => {
  try {
    return await AccessibilityInfo.isScreenReaderEnabled();
  } catch (error) {
    console.warn('Failed to check screen reader status:', error);
    return false;
  }
};

/**
 * Check if reduce motion is enabled
 */
export const isReduceMotionEnabled = async (): Promise<boolean> => {
  try {
    return await AccessibilityInfo.isReduceMotionEnabled();
  } catch (error) {
    console.warn('Failed to check reduce motion status:', error);
    return false;
  }
};

/**
 * Announce message to screen reader
 */
export const announceForAccessibility = (message: string) => {
  AccessibilityInfo.announceForAccessibility(message);
};

/**
 * Focus management utilities
 */
export const focusManager = {
  /**
   * Set focus to an element (useful for form validation)
   */
  setFocus: (ref: React.RefObject<any>) => {
    if (ref.current?.focus) {
      ref.current.focus();
    }
  },

  /**
   * Move focus to next element in tab order
   */
  focusNext: () => {
    AccessibilityInfo.setAccessibilityFocus(0); // Platform-specific implementation needed
  },

  /**
   * Move focus to previous element in tab order
   */
  focusPrevious: () => {
    // Platform-specific implementation needed
  },
};

/**
 * Color contrast utilities
 */
export const colorContrast = {
  /**
   * Check if color combination meets WCAG AA standards
   */
  meetsWCAGAA: (foreground: string, background: string): boolean => {
    // This is a simplified check - in production, use a proper contrast calculation library
    const ratio = calculateContrastRatio(foreground, background);
    return ratio >= DesignSystem.Accessibility.contrast.normal;
  },

  /**
   * Check if color combination meets WCAG AAA standards
   */
  meetsWCAGAAA: (foreground: string, background: string): boolean => {
    const ratio = calculateContrastRatio(foreground, background);
    return ratio >= 7; // WCAG AAA standard
  },

  /**
   * Get accessible color variant
   */
  getAccessibleColor: (color: string, background: string): string => {
    if (colorContrast.meetsWCAGAA(color, background)) {
      return color;
    }
    // Return a more contrasted version - simplified implementation
    return color === '#FFFFFF' ? '#000000' : '#FFFFFF';
  },
};

/**
 * Simplified contrast ratio calculation
 * Note: In production, use a proper color contrast library like 'color-contrast'
 */
function calculateContrastRatio(foreground: string, background: string): number {
  // This is a very simplified implementation
  // In production, use a proper library for accurate calculations
  const fgLuminance = getRelativeLuminance(foreground);
  const bgLuminance = getRelativeLuminance(background);
  
  const lighter = Math.max(fgLuminance, bgLuminance);
  const darker = Math.min(fgLuminance, bgLuminance);
  
  return (lighter + 0.05) / (darker + 0.05);
}

/**
 * Simplified relative luminance calculation
 */
function getRelativeLuminance(color: string): number {
  // Very simplified - in production use proper color parsing
  const hex = color.replace('#', '');
  const r = parseInt(hex.substr(0, 2), 16) / 255;
  const g = parseInt(hex.substr(2, 2), 16) / 255;
  const b = parseInt(hex.substr(4, 2), 16) / 255;
  
  return 0.2126 * r + 0.7152 * g + 0.0722 * b;
}

/**
 * Touch target utilities
 */
export const touchTarget = {
  /**
   * Ensure minimum touch target size
   */
  ensureMinimumSize: (width: number, height: number) => ({
    minWidth: Math.max(width, DesignSystem.Accessibility.minTouchTarget.width),
    minHeight: Math.max(height, DesignSystem.Accessibility.minTouchTarget.height),
  }),

  /**
   * Add padding to reach minimum touch target
   */
  addPaddingForMinimumSize: (width: number, height: number) => {
    const minWidth = DesignSystem.Accessibility.minTouchTarget.width;
    const minHeight = DesignSystem.Accessibility.minTouchTarget.height;
    
    return {
      paddingHorizontal: Math.max(0, (minWidth - width) / 2),
      paddingVertical: Math.max(0, (minHeight - height) / 2),
    };
  },
};

/**
 * Form accessibility utilities
 */
export const formAccessibility = {
  /**
   * Get accessibility props for form field with validation
   */
  getFieldProps: (
    label: string,
    value: string,
    error?: string,
    required?: boolean,
    description?: string
  ) => ({
    ...getAccessibilityProps.textInput(label, value, error, required),
    accessibilityHint: error || description,
  }),

  /**
   * Get accessibility props for form submit button
   */
  getSubmitButtonProps: (isLoading?: boolean, hasErrors?: boolean) => ({
    ...getAccessibilityProps.button(
      isLoading ? 'Submitting...' : 'Submit',
      hasErrors ? 'Please fix form errors before submitting' : undefined,
      isLoading
    ),
    accessibilityState: {
      busy: isLoading,
      disabled: isLoading,
    },
  }),
};

/**
 * Navigation accessibility utilities
 */
export const navigationAccessibility = {
  /**
   * Get accessibility props for navigation items
   */
  getNavItemProps: (label: string, isActive?: boolean, badge?: number) => ({
    ...getAccessibilityProps.tab(label, isActive),
    accessibilityLabel: badge ? `${label}, ${badge} notifications` : label,
    accessibilityState: isActive ? AccessibilityStates.selected : undefined,
  }),

  /**
   * Get accessibility props for back button
   */
  getBackButtonProps: (destination?: string) => ({
    ...getAccessibilityProps.button(
      'Go back',
      destination ? `Go back to ${destination}` : 'Go back to previous screen'
    ),
  }),
};
