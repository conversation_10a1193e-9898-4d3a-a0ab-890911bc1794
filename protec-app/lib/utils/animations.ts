/**
 * Animation Utilities
 * 
 * Comprehensive animation utilities for micro-interactions and smooth transitions
 * using react-native-reanimated.
 */

import {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSpring,
  withSequence,
  withDelay,
  withRepeat,
  interpolate,
  runOnJS,
  Easing,
} from 'react-native-reanimated';
import { DesignSystem } from '@/constants/DesignSystem';

/**
 * Common animation configurations
 */
export const AnimationConfigs = {
  // Timing configurations
  timing: {
    fast: { duration: DesignSystem.Animations.duration.fast },
    normal: { duration: DesignSystem.Animations.duration.normal },
    slow: { duration: DesignSystem.Animations.duration.slow },
    slower: { duration: DesignSystem.Animations.duration.slower },
  },
  
  // Spring configurations
  spring: {
    gentle: DesignSystem.Animations.spring.gentle,
    bouncy: DesignSystem.Animations.spring.bouncy,
    snappy: DesignSystem.Animations.spring.snappy,
  },
  
  // Easing functions
  easing: {
    easeInOut: Easing.inOut(Easing.ease),
    easeOut: Easing.out(Easing.ease),
    easeIn: Easing.in(Easing.ease),
    bounce: Easing.bounce,
    elastic: Easing.elastic(1.5),
  },
} as const;

/**
 * Button press animation hook
 */
export const useButtonPressAnimation = (
  pressScale: number = 0.95,
  pressOpacity: number = 0.8
) => {
  const scale = useSharedValue(1);
  const opacity = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value,
  }));

  const onPressIn = () => {
    scale.value = withSpring(pressScale, AnimationConfigs.spring.snappy);
    opacity.value = withTiming(pressOpacity, AnimationConfigs.timing.fast);
  };

  const onPressOut = () => {
    scale.value = withSpring(1, AnimationConfigs.spring.gentle);
    opacity.value = withTiming(1, AnimationConfigs.timing.fast);
  };

  return {
    animatedStyle,
    onPressIn,
    onPressOut,
  };
};

/**
 * Fade in animation hook
 */
export const useFadeInAnimation = (
  delay: number = 0,
  duration: number = DesignSystem.Animations.duration.normal
) => {
  const opacity = useSharedValue(0);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
  }));

  const fadeIn = () => {
    opacity.value = withDelay(
      delay,
      withTiming(1, { duration, easing: AnimationConfigs.easing.easeOut })
    );
  };

  const fadeOut = () => {
    opacity.value = withTiming(0, { duration, easing: AnimationConfigs.easing.easeIn });
  };

  return {
    animatedStyle,
    fadeIn,
    fadeOut,
    opacity,
  };
};

/**
 * Slide animation hook
 */
export const useSlideAnimation = (
  direction: 'up' | 'down' | 'left' | 'right' = 'up',
  distance: number = 50,
  delay: number = 0
) => {
  const translateX = useSharedValue(direction === 'left' ? -distance : direction === 'right' ? distance : 0);
  const translateY = useSharedValue(direction === 'up' ? distance : direction === 'down' ? -distance : 0);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { translateX: translateX.value },
      { translateY: translateY.value },
    ],
  }));

  const slideIn = () => {
    translateX.value = withDelay(delay, withSpring(0, AnimationConfigs.spring.gentle));
    translateY.value = withDelay(delay, withSpring(0, AnimationConfigs.spring.gentle));
  };

  const slideOut = () => {
    translateX.value = withTiming(
      direction === 'left' ? -distance : direction === 'right' ? distance : 0,
      AnimationConfigs.timing.normal
    );
    translateY.value = withTiming(
      direction === 'up' ? distance : direction === 'down' ? -distance : 0,
      AnimationConfigs.timing.normal
    );
  };

  return {
    animatedStyle,
    slideIn,
    slideOut,
    translateX,
    translateY,
  };
};

/**
 * Scale animation hook
 */
export const useScaleAnimation = (
  fromScale: number = 0,
  toScale: number = 1,
  delay: number = 0
) => {
  const scale = useSharedValue(fromScale);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const scaleIn = () => {
    scale.value = withDelay(delay, withSpring(toScale, AnimationConfigs.spring.bouncy));
  };

  const scaleOut = () => {
    scale.value = withTiming(fromScale, AnimationConfigs.timing.normal);
  };

  return {
    animatedStyle,
    scaleIn,
    scaleOut,
    scale,
  };
};

/**
 * Rotation animation hook
 */
export const useRotationAnimation = (
  fromRotation: number = 0,
  toRotation: number = 360,
  duration: number = DesignSystem.Animations.duration.slower
) => {
  const rotation = useSharedValue(fromRotation);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ rotate: `${rotation.value}deg` }],
  }));

  const rotate = () => {
    rotation.value = withTiming(toRotation, { duration });
  };

  const rotateRepeat = () => {
    rotation.value = withRepeat(
      withTiming(toRotation, { duration }),
      -1,
      false
    );
  };

  const stopRotation = () => {
    rotation.value = withTiming(fromRotation, AnimationConfigs.timing.normal);
  };

  return {
    animatedStyle,
    rotate,
    rotateRepeat,
    stopRotation,
    rotation,
  };
};

/**
 * Shake animation hook
 */
export const useShakeAnimation = (intensity: number = 10) => {
  const translateX = useSharedValue(0);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateX: translateX.value }],
  }));

  const shake = () => {
    translateX.value = withSequence(
      withTiming(intensity, { duration: 50 }),
      withTiming(-intensity, { duration: 50 }),
      withTiming(intensity, { duration: 50 }),
      withTiming(-intensity, { duration: 50 }),
      withTiming(0, { duration: 50 })
    );
  };

  return {
    animatedStyle,
    shake,
  };
};

/**
 * Pulse animation hook
 */
export const usePulseAnimation = (
  minScale: number = 1,
  maxScale: number = 1.1,
  duration: number = 1000
) => {
  const scale = useSharedValue(minScale);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const startPulse = () => {
    scale.value = withRepeat(
      withSequence(
        withTiming(maxScale, { duration: duration / 2 }),
        withTiming(minScale, { duration: duration / 2 })
      ),
      -1,
      true
    );
  };

  const stopPulse = () => {
    scale.value = withTiming(minScale, AnimationConfigs.timing.normal);
  };

  return {
    animatedStyle,
    startPulse,
    stopPulse,
  };
};

/**
 * Entrance animation sequence
 */
export const useEntranceAnimation = (
  elements: number = 1,
  staggerDelay: number = 100
) => {
  const animations = Array.from({ length: elements }, (_, index) => {
    const opacity = useSharedValue(0);
    const translateY = useSharedValue(30);
    const scale = useSharedValue(0.9);

    const animatedStyle = useAnimatedStyle(() => ({
      opacity: opacity.value,
      transform: [
        { translateY: translateY.value },
        { scale: scale.value },
      ],
    }));

    const animate = () => {
      const delay = index * staggerDelay;
      
      opacity.value = withDelay(delay, withTiming(1, AnimationConfigs.timing.normal));
      translateY.value = withDelay(delay, withSpring(0, AnimationConfigs.spring.gentle));
      scale.value = withDelay(delay, withSpring(1, AnimationConfigs.spring.bouncy));
    };

    return { animatedStyle, animate };
  });

  const startAnimation = () => {
    animations.forEach(({ animate }) => animate());
  };

  return {
    animations,
    startAnimation,
  };
};

/**
 * Loading dots animation
 */
export const useLoadingDotsAnimation = (dotCount: number = 3) => {
  const dots = Array.from({ length: dotCount }, () => useSharedValue(0.3));

  const animatedStyles = dots.map(dot => 
    useAnimatedStyle(() => ({
      opacity: dot.value,
    }))
  );

  const startAnimation = () => {
    dots.forEach((dot, index) => {
      const delay = index * 200;
      dot.value = withDelay(
        delay,
        withRepeat(
          withSequence(
            withTiming(1, { duration: 600 }),
            withTiming(0.3, { duration: 600 })
          ),
          -1,
          false
        )
      );
    });
  };

  const stopAnimation = () => {
    dots.forEach(dot => {
      dot.value = withTiming(0.3, AnimationConfigs.timing.fast);
    });
  };

  return {
    animatedStyles,
    startAnimation,
    stopAnimation,
  };
};

/**
 * Progress bar animation
 */
export const useProgressAnimation = (duration: number = 2000) => {
  const progress = useSharedValue(0);

  const animatedStyle = useAnimatedStyle(() => ({
    width: `${progress.value}%`,
  }));

  const startProgress = (onComplete?: () => void) => {
    progress.value = withTiming(
      100,
      { duration },
      (finished) => {
        if (finished && onComplete) {
          runOnJS(onComplete)();
        }
      }
    );
  };

  const resetProgress = () => {
    progress.value = 0;
  };

  return {
    animatedStyle,
    startProgress,
    resetProgress,
    progress,
  };
};
