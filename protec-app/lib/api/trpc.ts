import { createTRPCClient, httpBatchLink } from '@trpc/client';
import { createTRPCReact } from '@trpc/react-query';
import { getAuthToken } from '../auth/storage';
import { config } from '../config/env';
import { AppRouter } from '../types/api';
import { transformer } from './transformer';

export const api = createTRPCReact<AppRouter>();

export const trpcClient = createTRPCClient<AppRouter>({
  links: [
    httpBatchLink({
      url: `${config.API_URL}/api/trpc`,
      transformer,
      async headers() {
        const token = await getAuthToken();
        return {
          authorization: token ? `Bearer ${token}` : '',
          'Content-Type': 'application/json',
          'x-client-type': 'mobile',
        };
      },
      fetch: async (url, options) => {
        if (config.DEV_MODE) {
          console.log('tRPC Request:', { url, options });
        }

        try {
          const response = await fetch(url, options);

          if (config.DEV_MODE) {
            console.log('tRPC Response:', {
              status: response.status,
              statusText: response.statusText
            });
          }

          return response;
        } catch (error) {
          if (config.DEV_MODE) {
            console.error('tRPC Error:', error);
          }
          throw error;
        }
      },
    }),
  ],
});
