import { createTRPCClient, httpBatchLink } from '@trpc/client';
import { createTRPCReact } from '@trpc/react-query';
import { getAuthToken } from '../auth/storage';
import { config } from '../config/env';
// Import the AppRouter type from the web app's tRPC setup
import { AppRouter } from '../../../web/lib/trpc/root';
import { transformer } from './transformer';

// Use the imported AppRouter type to correctly type the tRPC client and API instance
export const api = createTRPCReact<AppRouter>();

export const trpcClient = createTRPCClient<AppRouter>({
  links: [
    httpBatchLink({
      url: `${config.API_URL}/api/trpc`,
      // transformer is correctly imported and used here
      transformer,
      async headers() {
        const token = await getAuthToken();
        return {
          authorization: token ? `Bearer ${token}` : '',
          'Content-Type': 'application/json',
          'x-client-type': 'mobile',
        };
      },
      fetch: async (url, options) => {
        if (config.DEV_MODE) {
          console.log('tRPC Request:', { url, options });
        }

        try {
          const response = await fetch(url, options);

          if (config.DEV_MODE) {
            console.log('tRPC Response:', {
              status: response.status,
              statusText: response.statusText
            });
          }

          return response;
        } catch (error) {
          if (config.DEV_MODE) {
            console.error('tRPC Error:', error);
          }
          throw error;
        }
      },
    }),
  ],
});
