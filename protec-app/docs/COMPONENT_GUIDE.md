# PROTEC Alumni App - Component Usage Guide

## Overview

This guide provides detailed information on how to use the enhanced UI components in the PROTEC Alumni mobile application. All components are designed with accessibility, responsiveness, and smooth animations in mind.

## Core Components

### Button Component

The Button component provides a consistent, accessible, and animated button interface.

#### Basic Usage
```tsx
import { Button } from '@/components/ui/Button';

<Button
  title="Click Me"
  onPress={() => console.log('Button pressed')}
/>
```

#### Props
| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `title` | `string` | - | Button text (required) |
| `onPress` | `() => void` | - | Press handler (required) |
| `variant` | `'primary' \| 'secondary' \| 'outline' \| 'ghost' \| 'destructive'` | `'primary'` | Visual style variant |
| `size` | `'sm' \| 'md' \| 'lg'` | `'md'` | Button size |
| `disabled` | `boolean` | `false` | Disabled state |
| `loading` | `boolean` | `false` | Loading state with spinner |
| `leftIcon` | `React.ReactNode` | - | Icon on the left side |
| `rightIcon` | `React.ReactNode` | - | Icon on the right side |
| `fullWidth` | `boolean` | `false` | Full width button |
| `accessibilityLabel` | `string` | - | Custom accessibility label |
| `accessibilityHint` | `string` | - | Accessibility hint |

#### Examples

**Primary Action Button**
```tsx
<Button
  title="Sign In"
  onPress={handleSignIn}
  variant="primary"
  size="lg"
  loading={isLoading}
  accessibilityLabel="Sign in to your account"
/>
```

**Secondary Button with Icon**
```tsx
<Button
  title="Cancel"
  onPress={handleCancel}
  variant="secondary"
  leftIcon={<CancelIcon />}
/>
```

**Destructive Action**
```tsx
<Button
  title="Delete Account"
  onPress={handleDelete}
  variant="destructive"
  accessibilityHint="This action cannot be undone"
/>
```

### Input Component

The Input component provides form inputs with validation, animations, and accessibility features.

#### Basic Usage
```tsx
import { Input } from '@/components/ui/Input';

<Input
  label="Email"
  value={email}
  onChangeText={setEmail}
  placeholder="Enter your email"
/>
```

#### Props
| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `label` | `string` | - | Input label |
| `value` | `string` | - | Input value |
| `onChangeText` | `(text: string) => void` | - | Change handler |
| `error` | `string` | - | Error message |
| `helperText` | `string` | - | Helper text |
| `variant` | `'default' \| 'outlined' \| 'filled'` | `'outlined'` | Visual style |
| `size` | `'sm' \| 'md' \| 'lg'` | `'md'` | Input size |
| `leftIcon` | `React.ReactNode` | - | Left side icon |
| `rightIcon` | `React.ReactNode` | - | Right side icon |

#### Examples

**Email Input with Validation**
```tsx
<Input
  label="Email Address"
  value={email}
  onChangeText={handleEmailChange}
  error={emailError}
  placeholder="Enter your email"
  keyboardType="email-address"
  autoCapitalize="none"
  variant="outlined"
  size="lg"
/>
```

**Password Input with Icon**
```tsx
<Input
  label="Password"
  value={password}
  onChangeText={setPassword}
  secureTextEntry={!showPassword}
  rightIcon={
    <TouchableOpacity onPress={() => setShowPassword(!showPassword)}>
      <EyeIcon />
    </TouchableOpacity>
  }
/>
```

### LoadingSpinner Component

Provides various loading animations with accessibility support.

#### Basic Usage
```tsx
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

<LoadingSpinner
  variant="dots"
  size="large"
  text="Loading..."
/>
```

#### Props
| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `variant` | `'spinner' \| 'dots' \| 'pulse'` | `'spinner'` | Animation type |
| `size` | `'small' \| 'medium' \| 'large'` | `'large'` | Spinner size |
| `color` | `string` | - | Custom color |
| `text` | `string` | - | Loading text |
| `overlay` | `boolean` | `false` | Full screen overlay |

#### Examples

**Page Loading**
```tsx
<LoadingSpinner
  variant="pulse"
  size="large"
  text="Loading your data..."
  overlay
/>
```

**Button Loading State**
```tsx
<LoadingSpinner
  variant="dots"
  size="small"
/>
```

## Animated Components

### AnimatedView

Provides entrance animations for any content.

#### Basic Usage
```tsx
import { AnimatedView } from '@/components/animated/AnimatedComponents';

<AnimatedView animation="fadeIn" delay={200}>
  <Text>This content will fade in</Text>
</AnimatedView>
```

#### Props
| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `animation` | `'fadeIn' \| 'slideUp' \| 'slideDown' \| 'slideLeft' \| 'slideRight' \| 'scaleIn'` | `'fadeIn'` | Animation type |
| `delay` | `number` | `0` | Animation delay (ms) |
| `duration` | `number` | `300` | Animation duration (ms) |
| `autoStart` | `boolean` | `true` | Auto-start animation |

### StaggeredList

Animates list items with staggered timing.

#### Basic Usage
```tsx
import { StaggeredList } from '@/components/animated/AnimatedComponents';

<StaggeredList staggerDelay={100}>
  {items.map(item => (
    <ListItem key={item.id} {...item} />
  ))}
</StaggeredList>
```

### Transition Components

#### ScaleTransition
```tsx
<ScaleTransition visible={showContent}>
  <View>Content that scales in/out</View>
</ScaleTransition>
```

#### FadeTransition
```tsx
<FadeTransition visible={showContent}>
  <View>Content that fades in/out</View>
</FadeTransition>
```

#### SlideTransition
```tsx
<SlideTransition visible={showContent} direction="up">
  <View>Content that slides in/out</View>
</SlideTransition>
```

## Utility Functions

### Validation Utilities

```tsx
import { validateEmail, validatePassword } from '@/lib/utils/validation';

// Email validation
const emailResult = validateEmail(email);
if (!emailResult.isValid) {
  setEmailError(emailResult.error);
}

// Password validation
const passwordResult = validatePassword(password);
if (!passwordResult.isValid) {
  setPasswordError(passwordResult.error);
}
```

### Animation Utilities

```tsx
import { useButtonPressAnimation, useFadeInAnimation } from '@/lib/utils/animations';

// Button press animation
const { animatedStyle, onPressIn, onPressOut } = useButtonPressAnimation();

// Fade in animation
const { animatedStyle, fadeIn, fadeOut } = useFadeInAnimation(200);
```

### Accessibility Utilities

```tsx
import { getAccessibilityProps, announceForAccessibility } from '@/lib/utils/accessibility';

// Button accessibility
const buttonProps = getAccessibilityProps.button(
  'Submit Form',
  'Submits the form data',
  isDisabled
);

// Announce to screen reader
announceForAccessibility('Form submitted successfully');
```

### Responsive Utilities

```tsx
import { getResponsiveLayout, useBreakpoint } from '@/lib/utils/responsive';

// Get responsive layout values
const layout = getResponsiveLayout();

// Use breakpoint hook
const { isMd, isLg, current } = useBreakpoint();
```

## Form Patterns

### Login Form Example
```tsx
function LoginForm() {
  const [email, setEmail] = useState('');
  const [emailError, setEmailError] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async () => {
    const validation = validateEmail(email);
    if (!validation.isValid) {
      setEmailError(validation.error);
      return;
    }

    setIsLoading(true);
    try {
      await submitForm(email);
      announceForAccessibility('Login successful');
    } catch (error) {
      const appError = handleAuthError(error);
      announceForAccessibility(`Error: ${appError.userMessage}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AnimatedView animation="slideUp" delay={200}>
      <ShakeView trigger={!!emailError}>
        <Input
          label="Email Address"
          value={email}
          onChangeText={setEmail}
          error={emailError}
          placeholder="Enter your email"
          keyboardType="email-address"
          autoCapitalize="none"
        />
      </ShakeView>

      <Button
        title="Sign In"
        onPress={handleSubmit}
        loading={isLoading}
        disabled={!email.trim()}
        variant="primary"
        size="lg"
        fullWidth
      />
    </AnimatedView>
  );
}
```

## Best Practices

### Component Usage
1. **Always use design system components** instead of creating custom ones
2. **Provide accessibility labels** for all interactive elements
3. **Use appropriate animations** to enhance user experience
4. **Test on multiple screen sizes** to ensure responsiveness
5. **Follow platform conventions** for iOS and Android

### Performance
1. **Use React.memo** for components that don't need frequent re-renders
2. **Optimize animations** by using native driver when possible
3. **Lazy load** heavy components when appropriate
4. **Monitor performance** with React DevTools

### Accessibility
1. **Test with screen readers** regularly
2. **Provide meaningful labels** and hints
3. **Ensure proper focus order** in forms
4. **Use semantic markup** with proper roles
5. **Test keyboard navigation** on all interactive elements

### Error Handling
1. **Use consistent error patterns** across the app
2. **Provide helpful error messages** to users
3. **Implement retry mechanisms** for network errors
4. **Log errors appropriately** for debugging
5. **Announce errors** to screen readers

## Common Patterns

### Loading States
```tsx
// Page loading
if (isLoading) {
  return (
    <LoadingSpinner
      variant="pulse"
      size="large"
      text="Loading..."
      overlay
    />
  );
}

// Button loading
<Button
  title="Submit"
  onPress={handleSubmit}
  loading={isSubmitting}
  disabled={isSubmitting}
/>
```

### Error States
```tsx
// Form validation error
<ShakeView trigger={!!error}>
  <Input
    value={value}
    onChangeText={setValue}
    error={error}
  />
</ShakeView>

// Error announcement
useEffect(() => {
  if (error) {
    announceForAccessibility(`Error: ${error}`);
  }
}, [error]);
```

### Success States
```tsx
// Success message
<ScaleTransition visible={showSuccess}>
  <View {...getAccessibilityProps.alert('Success message', 'success')}>
    <Text>✅ Operation completed successfully!</Text>
  </View>
</ScaleTransition>

// Success announcement
useEffect(() => {
  if (success) {
    announceForAccessibility('Operation completed successfully');
  }
}, [success]);
```
