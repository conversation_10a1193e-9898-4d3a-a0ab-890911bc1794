# PROTEC Alumni App - Design System Documentation

## Overview

This document outlines the comprehensive design system implemented for the PROTEC Alumni mobile application. The design system ensures consistency, accessibility, and a premium user experience across all platforms.

## Design Principles

### 1. Professional Excellence
- Clean, modern interface that reflects PROTEC's commitment to excellence
- Consistent use of brand colors and typography
- Premium feel with subtle animations and micro-interactions

### 2. Accessibility First
- WCAG AA compliance for color contrast
- Screen reader support with proper semantic markup
- Minimum touch target sizes (44x44pt)
- Support for reduced motion preferences

### 3. Responsive Design
- Seamless experience across different screen sizes
- Platform-specific design patterns (iOS HIG & Material Design)
- Adaptive layouts for tablets and phones

### 4. Performance Optimized
- Smooth 60fps animations using react-native-reanimated
- Efficient rendering with proper component optimization
- Minimal bundle size impact

## Brand Colors

### Primary Palette
```typescript
// PROTEC Brand Colors
navy: '#012A5B'        // Primary brand color
red: '#E31E24'         // Accent color
navyLight: '#1E3A6F'   // Hover states
navyDark: '#001A3D'    // Pressed states
```

### Semantic Colors
```typescript
// Status Colors
success: '#10B981'     // Success states
warning: '#F59E0B'     // Warning states
error: '#EF4444'       // Error states
info: '#3B82F6'        // Information states
```

### Neutral Palette
```typescript
// Grayscale
gray50: '#F9FAFB'      // Lightest background
gray100: '#F3F4F6'     // Light background
gray200: '#E5E7EB'     // Borders
gray300: '#D1D5DB'     // Disabled states
gray400: '#9CA3AF'     // Muted text
gray500: '#6B7280'     // Secondary text
gray600: '#4B5563'     // Primary text (light mode)
gray700: '#374151'     // Dark backgrounds
gray800: '#1F2937'     // Darker backgrounds
gray900: '#111827'     // Darkest text
```

## Typography

### Font Scale
```typescript
fontSize: {
  xs: 12,     // Helper text, captions
  sm: 14,     // Small text, labels
  base: 16,   // Body text, inputs
  lg: 18,     // Large body text
  xl: 20,     // Small headings
  '2xl': 24,  // Medium headings
  '3xl': 30,  // Large headings
  '4xl': 36,  // Extra large headings
  '5xl': 48,  // Display text
}
```

### Font Weights
```typescript
fontWeight: {
  normal: '400',    // Regular text
  medium: '500',    // Emphasized text
  semibold: '600',  // Buttons, labels
  bold: '700',      // Headings
  extrabold: '800', // Display text
}
```

## Spacing System

Based on a 4px grid system for consistent spacing:

```typescript
spacing: {
  xs: 4,      // Tight spacing
  sm: 8,      // Small spacing
  md: 12,     // Medium spacing
  lg: 16,     // Large spacing
  xl: 20,     // Extra large spacing
  '2xl': 24,  // 2x extra large
  '3xl': 32,  // 3x extra large
  '4xl': 40,  // 4x extra large
  '5xl': 48,  // 5x extra large
  '6xl': 64,  // 6x extra large
}
```

## Component Library

### Button Component

#### Variants
- **Primary**: Main call-to-action buttons
- **Secondary**: Secondary actions
- **Outline**: Tertiary actions
- **Ghost**: Subtle actions
- **Destructive**: Dangerous actions

#### Sizes
- **Small (sm)**: 36px height, compact spacing
- **Medium (md)**: 44px height, standard spacing
- **Large (lg)**: 52px height, generous spacing

#### Features
- Smooth press animations with scale and opacity changes
- Loading states with activity indicators
- Icon support (left and right)
- Full-width option
- Accessibility compliant with proper ARIA labels

### Input Component

#### Variants
- **Default**: Simple underline style
- **Outlined**: Border around the input
- **Filled**: Background filled style

#### Features
- Animated focus states with color transitions
- Error states with shake animation
- Helper text and validation messages
- Icon support (left and right)
- Responsive sizing
- Screen reader support

### Loading Components

#### Variants
- **Spinner**: Traditional activity indicator
- **Dots**: Animated dots sequence
- **Pulse**: Pulsing circle animation

#### Features
- Customizable colors and sizes
- Overlay support for full-screen loading
- Accessibility announcements

## Animation System

### Timing Functions
```typescript
duration: {
  fast: 150,      // Quick interactions
  normal: 250,    // Standard transitions
  slow: 350,      // Deliberate animations
  slower: 500,    // Entrance animations
}
```

### Spring Configurations
```typescript
spring: {
  gentle: { damping: 20, stiffness: 300 },   // Smooth, natural
  bouncy: { damping: 15, stiffness: 400 },   // Playful bounce
  snappy: { damping: 25, stiffness: 500 },   // Quick, responsive
}
```

### Common Animations
- **Button Press**: Scale to 0.95 with opacity fade
- **Form Validation**: Shake animation for errors
- **Page Transitions**: Slide and fade combinations
- **Loading States**: Smooth progress indicators

## Accessibility Guidelines

### Color Contrast
- All text meets WCAG AA standards (4.5:1 ratio)
- Large text meets WCAG AA standards (3:1 ratio)
- Interactive elements have sufficient contrast

### Touch Targets
- Minimum 44x44pt touch targets
- Adequate spacing between interactive elements
- Visual feedback for all interactions

### Screen Reader Support
- Proper semantic markup with accessibility roles
- Descriptive labels and hints
- Live region announcements for dynamic content
- Logical focus order

### Keyboard Navigation
- All interactive elements are keyboard accessible
- Visible focus indicators
- Logical tab order

## Responsive Design

### Breakpoints
```typescript
breakpoints: {
  sm: 640,    // Small tablets
  md: 768,    // Medium tablets
  lg: 1024,   // Large tablets
  xl: 1280,   // Desktop
}
```

### Device Categories
- **Small Phone**: < 667px height
- **Regular Phone**: 667-812px height
- **Large Phone**: > 812px height
- **Tablet**: > 600px width

### Adaptive Features
- Responsive font sizes based on device type
- Adaptive spacing and component sizing
- Platform-specific design patterns
- Orientation-aware layouts

## Platform Considerations

### iOS Specific
- Native iOS shadow styles
- iOS Human Interface Guidelines compliance
- Proper safe area handling
- iOS-specific font weights

### Android Specific
- Material Design elevation system
- Android-specific minimum touch targets (48dp)
- Proper status bar handling
- Material Design color system

## Usage Examples

### Basic Button Usage
```tsx
<Button
  title="Sign In"
  onPress={handleSignIn}
  variant="primary"
  size="lg"
  loading={isLoading}
  accessibilityLabel="Sign in to your account"
/>
```

### Form Input Usage
```tsx
<Input
  label="Email Address"
  value={email}
  onChangeText={setEmail}
  error={emailError}
  variant="outlined"
  size="lg"
  placeholder="Enter your email"
  keyboardType="email-address"
/>
```

### Animated Components
```tsx
<AnimatedView animation="slideUp" delay={200}>
  <ThemedText type="title">Welcome Back</ThemedText>
</AnimatedView>

<StaggeredList staggerDelay={100}>
  {items.map(item => <ListItem key={item.id} {...item} />)}
</StaggeredList>
```

## Best Practices

### Component Development
1. Always use the design system tokens
2. Implement proper accessibility features
3. Add smooth animations for interactions
4. Test on multiple screen sizes
5. Follow platform-specific patterns

### Color Usage
1. Use semantic colors for status indicators
2. Ensure proper contrast ratios
3. Test in both light and dark modes
4. Consider color blindness accessibility

### Typography
1. Use the established font scale
2. Maintain proper line heights
3. Consider reading accessibility
4. Test with dynamic font sizes

### Spacing
1. Use the 4px grid system
2. Maintain consistent spacing patterns
3. Consider touch target requirements
4. Test on different screen densities

## Testing Guidelines

### Visual Testing
- Test on multiple device sizes
- Verify color contrast ratios
- Check animation performance
- Validate responsive behavior

### Accessibility Testing
- Use screen reader testing
- Verify keyboard navigation
- Test with accessibility tools
- Validate ARIA implementations

### Performance Testing
- Monitor animation frame rates
- Check component render performance
- Validate memory usage
- Test on lower-end devices

## Maintenance

### Regular Updates
- Review and update color contrast ratios
- Update accessibility features as standards evolve
- Optimize animations for performance
- Keep platform guidelines current

### Documentation
- Keep this documentation updated
- Document new components and patterns
- Maintain usage examples
- Update best practices based on learnings
