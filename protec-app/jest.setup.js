import 'react-native-gesture-handler/jestSetup';
import mockAsyncStorage from '@react-native-async-storage/async-storage/jest/async-storage-mock';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => mockAsyncStorage);

// Mock react-native-reanimated
jest.mock('react-native-reanimated', () => {
  const Reanimated = require('react-native-reanimated/mock');
  Reanimated.default.call = () => {};
  return Reanimated;
});

// Mock Expo modules
jest.mock('expo-constants', () => ({
  expoConfig: {
    extra: {
      apiUrl: 'http://localhost:3000',
    },
  },
  executionEnvironment: 'standalone',
}));

jest.mock('expo-router', () => ({
  router: {
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
    canGoBack: jest.fn(() => true),
  },
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
    canGoBack: jest.fn(() => true),
  }),
  useLocalSearchParams: () => ({}),
  useGlobalSearchParams: () => ({}),
  usePathname: () => '/',
  useSegments: () => [],
}));

jest.mock('expo-font', () => ({
  loadAsync: jest.fn(),
  isLoaded: jest.fn(() => true),
}));

jest.mock('expo-splash-screen', () => ({
  hideAsync: jest.fn(),
  preventAutoHideAsync: jest.fn(),
}));

jest.mock('expo-secure-store', () => ({
  setItemAsync: jest.fn(),
  getItemAsync: jest.fn(),
  deleteItemAsync: jest.fn(),
}));

jest.mock('expo-auth-session', () => ({
  makeRedirectUri: jest.fn(() => 'http://localhost:19006'),
  useAuthRequest: jest.fn(() => [
    null,
    { type: 'success', params: { code: 'mock-code' } },
    jest.fn(),
  ]),
  AuthRequest: {
    promptAsync: jest.fn(),
  },
}));

jest.mock('expo-contacts', () => ({
  requestPermissionsAsync: jest.fn(() => ({ status: 'granted' })),
  getPermissionsAsync: jest.fn(() => ({ status: 'granted' })),
  getContactsAsync: jest.fn(() => ({ data: [] })),
  addContactAsync: jest.fn(() => 'mock-contact-id'),
  Fields: {
    Name: 'name',
    PhoneNumbers: 'phoneNumbers',
    Emails: 'emails',
    Company: 'company',
    JobTitle: 'jobTitle',
    Note: 'note',
    Image: 'image',
  },
}));

jest.mock('expo-location', () => ({
  requestForegroundPermissionsAsync: jest.fn(() => ({ status: 'granted' })),
  requestBackgroundPermissionsAsync: jest.fn(() => ({ status: 'granted' })),
  getForegroundPermissionsAsync: jest.fn(() => ({ status: 'granted' })),
  getBackgroundPermissionsAsync: jest.fn(() => ({ status: 'granted' })),
  getCurrentPositionAsync: jest.fn(() => ({
    coords: {
      latitude: -26.2041,
      longitude: 28.0473,
      accuracy: 5,
    },
  })),
  reverseGeocodeAsync: jest.fn(() => [
    {
      city: 'Johannesburg',
      region: 'Gauteng',
      country: 'South Africa',
    },
  ]),
  geocodeAsync: jest.fn(() => [
    {
      latitude: -26.2041,
      longitude: 28.0473,
    },
  ]),
}));

jest.mock('expo-calendar', () => ({
  requestCalendarPermissionsAsync: jest.fn(() => ({ status: 'granted' })),
  getCalendarPermissionsAsync: jest.fn(() => ({ status: 'granted' })),
  getCalendarsAsync: jest.fn(() => []),
  createCalendarAsync: jest.fn(() => 'mock-calendar-id'),
  createEventAsync: jest.fn(() => 'mock-event-id'),
}));

jest.mock('expo-sharing', () => ({
  isAvailableAsync: jest.fn(() => true),
  shareAsync: jest.fn(),
}));

// Mock React Query
jest.mock('@tanstack/react-query', () => ({
  useQuery: jest.fn(() => ({
    data: null,
    isLoading: false,
    error: null,
    refetch: jest.fn(),
  })),
  useMutation: jest.fn(() => ({
    mutate: jest.fn(),
    mutateAsync: jest.fn(),
    isLoading: false,
    error: null,
  })),
  useQueryClient: jest.fn(() => ({
    invalidateQueries: jest.fn(),
    setQueryData: jest.fn(),
    getQueryData: jest.fn(),
  })),
  QueryClient: jest.fn(() => ({
    invalidateQueries: jest.fn(),
    setQueryData: jest.fn(),
    getQueryData: jest.fn(),
  })),
}));

// Mock tRPC
jest.mock('@/lib/api/trpc', () => ({
  api: {
    alumni: {
      getAll: {
        query: jest.fn(),
      },
      getById: {
        query: jest.fn(),
      },
    },
    events: {
      getAll: {
        query: jest.fn(),
      },
      getById: {
        query: jest.fn(),
      },
      rsvp: {
        mutate: jest.fn(),
      },
    },
    posts: {
      getAll: {
        query: jest.fn(),
      },
      create: {
        mutate: jest.fn(),
      },
      toggleLike: {
        mutate: jest.fn(),
      },
    },
  },
}));

// Mock Zustand stores
jest.mock('@/lib/stores/authStore', () => ({
  useAuthStore: jest.fn(() => ({
    user: null,
    isAuthenticated: false,
    login: jest.fn(),
    logout: jest.fn(),
    setUser: jest.fn(),
  })),
}));

// Mock theme colors
jest.mock('@/hooks/useThemeColor', () => ({
  useThemeColor: jest.fn((props, colorName) => {
    const colors = {
      primary: '#012A5B',
      text: '#000000',
      background: '#FFFFFF',
      surface: '#F5F5F5',
      border: '#E0E0E0',
      error: '#DC2626',
    };
    return colors[colorName] || '#000000';
  }),
}));

// Mock Ionicons
jest.mock('@expo/vector-icons', () => ({
  Ionicons: 'Ionicons',
}));

// Silence the warning: Animated: `useNativeDriver` is not supported
jest.mock('react-native/Libraries/Animated/NativeAnimatedHelper');

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Setup global test environment
global.__DEV__ = true;

// Mock fetch for API calls
global.fetch = jest.fn(() =>
  Promise.resolve({
    ok: true,
    status: 200,
    json: () => Promise.resolve({}),
    text: () => Promise.resolve(''),
  })
);

// Mock timers
jest.useFakeTimers();

// Setup test timeout
jest.setTimeout(10000);
