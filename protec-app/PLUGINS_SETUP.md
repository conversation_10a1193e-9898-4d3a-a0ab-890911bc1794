# PROTEC Alumni App - High Priority Plugins Setup

This document outlines the setup and usage of the high priority Expo SDK 53 plugins integrated into the PROTEC Alumni app.

## 🚀 Installed Plugins

### 1. expo-contacts
**Purpose**: Alumni directory integration and contact management
**Permissions**: Read/Write contacts
**Use Cases**: 
- Find fellow PROTEC alumni in contacts
- Add new alumni connections
- Search and filter contacts by company/email domain

### 2. expo-location
**Purpose**: Location-based features for events and meetups
**Permissions**: Location access (when in use)
**Use Cases**:
- Find nearby alumni events
- Get directions to meetups
- Location-based event recommendations

### 3. expo-calendar
**Purpose**: Event scheduling and reminders
**Permissions**: Calendar read/write access
**Use Cases**:
- Save alumni events to calendar
- Set event reminders
- Create recurring alumni meetups

### 4. expo-auth-session
**Purpose**: Social login integration
**Permissions**: None (uses OAuth)
**Use Cases**:
- Google Sign-In for professional profiles
- LinkedIn integration for networking
- Microsoft account for enterprise users

### 5. expo-sharing
**Purpose**: Content sharing capabilities
**Permissions**: None (uses native sharing)
**Use Cases**:
- Share alumni achievements
- Invite friends to events
- Share app with other alumni

## 📱 Configuration

### App.json Configuration
The following permissions and plugins have been added to `app.json`:

```json
{
  "expo": {
    "plugins": [
      ["expo-contacts", {
        "contactsPermission": "Allow PROTEC Alumni to access your contacts to help you connect with fellow alumni."
      }],
      ["expo-location", {
        "locationAlwaysAndWhenInUsePermission": "Allow PROTEC Alumni to use your location to show nearby alumni events and meetups.",
        "isIosBackgroundLocationEnabled": false,
        "isAndroidBackgroundLocationEnabled": false
      }],
      ["expo-calendar", {
        "calendarPermission": "Allow PROTEC Alumni to access your calendar to help you save alumni events and reminders."
      }]
    ]
  }
}
```

### Environment Variables
Create a `.env` file in your project root with the following OAuth credentials:

```env
EXPO_PUBLIC_GOOGLE_CLIENT_ID=your-google-client-id
EXPO_PUBLIC_LINKEDIN_CLIENT_ID=your-linkedin-client-id
EXPO_PUBLIC_MICROSOFT_CLIENT_ID=your-microsoft-client-id
```

## 🛠 Usage Examples

### Contacts Service
```typescript
import { ContactsService } from '../services';

// Get all contacts
const contacts = await ContactsService.getAllContacts();

// Find potential alumni
const alumni = await ContactsService.findPotentialAlumni(
  ['protec.edu'], // Alumni email domains
  ['PROTEC', 'Boeing'] // Alumni companies
);

// Add new contact
await ContactsService.addContact({
  firstName: 'John',
  lastName: 'Doe',
  emails: ['<EMAIL>'],
  company: 'PROTEC Alumni Network'
});
```

### Location Service
```typescript
import { LocationService } from '../services';

// Get current location
const location = await LocationService.getCurrentLocation();

// Find nearby events
const nearbyEvents = await LocationService.findNearbyEvents(
  location,
  allEvents,
  50 // 50km radius
);

// Get address from coordinates
const address = await LocationService.getAddressFromCoordinates(location);
```

### Calendar Service
```typescript
import { CalendarService } from '../services';

// Add event to calendar
const eventId = await CalendarService.addEvent({
  title: 'PROTEC Alumni Networking',
  startDate: new Date('2024-08-15T18:00:00'),
  endDate: new Date('2024-08-15T20:00:00'),
  location: 'Seattle Center',
  notes: 'Annual networking event'
});

// Get upcoming alumni events
const upcomingEvents = await CalendarService.getUpcomingAlumniEvents(30);
```

### Auth Service
```typescript
import { AuthService } from '../services';

// Sign in with Google
const user = await AuthService.signInWithGoogle();

// Check if authenticated
const isAuth = await AuthService.isAuthenticated();

// Get stored user
const currentUser = await AuthService.getStoredUser();

// Sign out
await AuthService.signOut();
```

### Sharing Service
```typescript
import { SharingService } from '../services';

// Share achievement
await SharingService.shareAchievement({
  title: 'New Job at Microsoft',
  description: 'Excited to start my new role!',
  alumniName: 'John Doe',
  category: 'job',
  date: new Date()
});

// Share event
await SharingService.shareEvent({
  title: 'Alumni Mixer',
  description: 'Join us for networking',
  date: new Date(),
  location: 'Seattle Center'
});

// Share app invitation
await SharingService.shareAppInvitation('John Doe');
```

## 🔧 Development Setup

### 1. Install Dependencies
```bash
npx expo install expo-contacts expo-location expo-calendar expo-auth-session expo-sharing
```

### 2. Configure OAuth Providers

#### Google OAuth Setup
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add your bundle ID and redirect URI

#### LinkedIn OAuth Setup
1. Go to [LinkedIn Developer Portal](https://developer.linkedin.com/)
2. Create a new app
3. Add OAuth 2.0 redirect URLs
4. Get client ID and secret

#### Microsoft OAuth Setup
1. Go to [Azure Portal](https://portal.azure.com/)
2. Register a new application
3. Configure redirect URIs
4. Get application ID

### 3. Build and Test
```bash
# Create development build
npx expo run:ios
npx expo run:android

# Or use EAS Build
eas build --platform all --profile development
```

## 📋 Testing Checklist

- [ ] Contacts permission requested and granted
- [ ] Location permission requested and granted
- [ ] Calendar permission requested and granted
- [ ] OAuth flows work for all providers
- [ ] Sharing works on both iOS and Android
- [ ] All services handle errors gracefully
- [ ] Permissions are properly explained to users

## 🚨 Important Notes

### iOS Considerations
- Location permission requires usage descriptions in Info.plist
- Calendar access requires NSCalendarsUsageDescription
- Contacts require NSContactsUsageDescription

### Android Considerations
- Location requires FINE_LOCATION and COARSE_LOCATION permissions
- Calendar requires READ_CALENDAR and WRITE_CALENDAR permissions
- Contacts require READ_CONTACTS and WRITE_CONTACTS permissions

### Privacy & Security
- All sensitive data is stored securely using expo-secure-store
- OAuth tokens are handled securely
- Location data is only used when necessary
- Users can revoke permissions at any time

## 🔄 Next Steps

### Medium Priority Plugins to Add Next
1. **expo-camera** - Enhanced photo features for events
2. **expo-video** - Alumni testimonials and event videos
3. **expo-local-authentication** - Biometric app security
4. **expo-background-task** - Background sync for updates

### Integration Opportunities
- Combine location + calendar for smart event suggestions
- Use contacts + auth for automatic alumni discovery
- Integrate sharing + calendar for event invitations
- Connect location + sharing for check-ins at events

## 📞 Support

For issues with plugin setup or integration:
1. Check the [Expo Documentation](https://docs.expo.dev/)
2. Review the service files in `/services/`
3. Test with the demo components in `/components/examples/`
4. Check permissions in device settings

## 📚 Additional Resources

- [Expo SDK 53 Documentation](https://docs.expo.dev/versions/v53.0.0/)
- [React Native Permissions Guide](https://docs.expo.dev/guides/permissions/)
- [OAuth Implementation Guide](https://docs.expo.dev/guides/authentication/)
- [Native Module Configuration](https://docs.expo.dev/workflow/configuration/)
