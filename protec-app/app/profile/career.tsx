import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Button, Card, Input, Modal } from '@/components/ui';
import { router } from 'expo-router';
import React, { useState } from 'react';
import {
    Alert,
    ScrollView,
    StyleSheet,
    TouchableOpacity,
    View,
} from 'react-native';

interface CareerEntry {
  id: string;
  title: string;
  company: string;
  startDate: string;
  endDate?: string;
  current: boolean;
  description: string;
  location: string;
}

export default function CareerHistoryScreen() {
  const [careerHistory, setCareerHistory] = useState<CareerEntry[]>([
    {
      id: '1',
      title: 'Software Engineer',
      company: 'Tech Corp',
      startDate: '2022-01',
      current: true,
      description: 'Developing web applications using React and Node.js',
      location: 'Johannesburg, South Africa',
    },
  ]);

  const [showAddModal, setShowAddModal] = useState(false);
  const [editingEntry, setEditingEntry] = useState<CareerEntry | null>(null);
  const [formData, setFormData] = useState({
    title: '',
    company: '',
    startDate: '',
    endDate: '',
    current: false,
    description: '',
    location: '',
  });

  const handleAddEntry = () => {
    setFormData({
      title: '',
      company: '',
      startDate: '',
      endDate: '',
      current: false,
      description: '',
      location: '',
    });
    setEditingEntry(null);
    setShowAddModal(true);
  };

  const handleEditEntry = (entry: CareerEntry) => {
    setFormData({
      title: entry.title,
      company: entry.company,
      startDate: entry.startDate,
      endDate: entry.endDate || '',
      current: entry.current,
      description: entry.description,
      location: entry.location,
    });
    setEditingEntry(entry);
    setShowAddModal(true);
  };

  const handleDeleteEntry = (id: string) => {
    Alert.alert(
      'Delete Entry',
      'Are you sure you want to delete this career entry?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            setCareerHistory(prev => prev.filter(entry => entry.id !== id));
          },
        },
      ]
    );
  };

  const handleSaveEntry = () => {
    if (!formData.title.trim() || !formData.company.trim()) {
      Alert.alert('Error', 'Title and company are required');
      return;
    }

    const newEntry: CareerEntry = {
      id: editingEntry?.id || Date.now().toString(),
      title: formData.title.trim(),
      company: formData.company.trim(),
      startDate: formData.startDate,
      endDate: formData.current ? undefined : formData.endDate,
      current: formData.current,
      description: formData.description.trim(),
      location: formData.location.trim(),
    };

    if (editingEntry) {
      setCareerHistory(prev =>
        prev.map(entry => (entry.id === editingEntry.id ? newEntry : entry))
      );
    } else {
      setCareerHistory(prev => [newEntry, ...prev]);
    }

    setShowAddModal(false);
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    const [year, month] = dateString.split('-');
    const monthNames = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return `${monthNames[parseInt(month) - 1]} ${year}`;
  };

  return (
    <ThemedView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <ThemedText style={styles.backButton}>‹ Back</ThemedText>
        </TouchableOpacity>
        <ThemedText type="subtitle">Career History</ThemedText>
        <TouchableOpacity onPress={handleAddEntry}>
          <ThemedText style={styles.addButton}>Add</ThemedText>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {careerHistory.length === 0 ? (
          <Card variant="outlined" style={styles.emptyState}>
            <ThemedText style={styles.emptyText}>
              No career history added yet
            </ThemedText>
            <Button
              title="Add Your First Entry"
              onPress={handleAddEntry}
              style={styles.emptyButton}
            />
          </Card>
        ) : (
          careerHistory.map((entry, index) => (
            <Card key={entry.id} variant="elevated" style={styles.entryCard}>
              <View style={styles.entryHeader}>
                <View style={styles.entryInfo}>
                  <ThemedText type="defaultSemiBold" style={styles.entryTitle}>
                    {entry.title}
                  </ThemedText>
                  <ThemedText style={styles.entryCompany}>
                    {entry.company}
                  </ThemedText>
                  <ThemedText style={styles.entryDate}>
                    {formatDate(entry.startDate)} - {entry.current ? 'Present' : formatDate(entry.endDate || '')}
                  </ThemedText>
                  {entry.location && (
                    <ThemedText style={styles.entryLocation}>
                      📍 {entry.location}
                    </ThemedText>
                  )}
                </View>
                
                <View style={styles.entryActions}>
                  <TouchableOpacity
                    onPress={() => handleEditEntry(entry)}
                    style={styles.actionButton}
                  >
                    <ThemedText style={styles.actionText}>Edit</ThemedText>
                  </TouchableOpacity>
                  <TouchableOpacity
                    onPress={() => handleDeleteEntry(entry.id)}
                    style={styles.actionButton}
                  >
                    <ThemedText style={[styles.actionText, styles.deleteText]}>
                      Delete
                    </ThemedText>
                  </TouchableOpacity>
                </View>
              </View>
              
              {entry.description && (
                <ThemedText style={styles.entryDescription}>
                  {entry.description}
                </ThemedText>
              )}
            </Card>
          ))
        )}
      </ScrollView>

      {/* Add/Edit Modal */}
      <Modal
        visible={showAddModal}
        onClose={() => setShowAddModal(false)}
        title={editingEntry ? 'Edit Career Entry' : 'Add Career Entry'}
        size="lg"
      >
        <ScrollView showsVerticalScrollIndicator={false}>
          <Input
            label="Job Title *"
            value={formData.title}
            onChangeText={(value) => setFormData(prev => ({ ...prev, title: value }))}
            placeholder="e.g. Software Engineer"
          />
          
          <Input
            label="Company *"
            value={formData.company}
            onChangeText={(value) => setFormData(prev => ({ ...prev, company: value }))}
            placeholder="e.g. Google"
          />
          
          <Input
            label="Location"
            value={formData.location}
            onChangeText={(value) => setFormData(prev => ({ ...prev, location: value }))}
            placeholder="e.g. Johannesburg, South Africa"
          />
          
          <Input
            label="Start Date"
            value={formData.startDate}
            onChangeText={(value) => setFormData(prev => ({ ...prev, startDate: value }))}
            placeholder="YYYY-MM"
          />
          
          {!formData.current && (
            <Input
              label="End Date"
              value={formData.endDate}
              onChangeText={(value) => setFormData(prev => ({ ...prev, endDate: value }))}
              placeholder="YYYY-MM"
            />
          )}
          
          <TouchableOpacity
            style={styles.checkboxContainer}
            onPress={() => setFormData(prev => ({ ...prev, current: !prev.current }))}
          >
            <View style={[styles.checkbox, formData.current && styles.checkboxChecked]}>
              {formData.current && <ThemedText style={styles.checkmark}>✓</ThemedText>}
            </View>
            <ThemedText style={styles.checkboxLabel}>
              I currently work here
            </ThemedText>
          </TouchableOpacity>
          
          <Input
            label="Description"
            value={formData.description}
            onChangeText={(value) => setFormData(prev => ({ ...prev, description: value }))}
            placeholder="Describe your role and responsibilities..."
            multiline
            numberOfLines={4}
            style={styles.textArea}
          />
        </ScrollView>
        
        <View style={styles.modalActions}>
          <Button
            title="Cancel"
            onPress={() => setShowAddModal(false)}
            variant="outline"
            style={styles.modalButton}
          />
          <Button
            title="Save"
            onPress={handleSaveEntry}
            style={styles.modalButton}
          />
        </View>
      </Modal>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  backButton: {
    color: '#012A5B',
    fontSize: 18,
    fontWeight: '600',
  },
  addButton: {
    color: '#012A5B',
    fontSize: 16,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  emptyState: {
    alignItems: 'center',
    padding: 32,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    marginBottom: 16,
    textAlign: 'center',
  },
  emptyButton: {
    marginTop: 8,
  },
  entryCard: {
    marginBottom: 16,
  },
  entryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  entryInfo: {
    flex: 1,
  },
  entryTitle: {
    fontSize: 18,
    marginBottom: 4,
  },
  entryCompany: {
    fontSize: 16,
    color: '#012A5B',
    fontWeight: '500',
    marginBottom: 4,
  },
  entryDate: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  entryLocation: {
    fontSize: 12,
    color: '#888',
  },
  entryActions: {
    gap: 8,
  },
  actionButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
  },
  actionText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#012A5B',
  },
  deleteText: {
    color: '#dc2626',
  },
  entryDescription: {
    fontSize: 14,
    lineHeight: 20,
    color: '#666',
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderColor: '#012A5B',
    borderRadius: 4,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxChecked: {
    backgroundColor: '#012A5B',
  },
  checkmark: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  checkboxLabel: {
    fontSize: 16,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 12,
    marginTop: 16,
  },
  modalButton: {
    flex: 1,
  },
});
