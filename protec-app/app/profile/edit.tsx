import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Card, Input, LoadingSpinner } from '@/components/ui';
import { useAlumniProfile, useUpdateAlumniProfile } from '@/lib/hooks/useAlumni';
import { useAuthStore } from '@/lib/stores/authStore';
import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
    Alert,
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    StyleSheet,
    TouchableOpacity,
    View,
} from 'react-native';

interface ProfileFormData {
  name: string;
  bio: string;
  currentRole: string;
  company: string;
  industry: string;
  city: string;
  province: string;
  skills: string;
  interests: string;
  graduationYear: string;
  programmes: string;
}

export default function EditProfileScreen() {
  const { user } = useAuthStore();
  const { data: profile, isLoading } = useAlumniProfile(user?.id || '');
  const updateProfile = useUpdateAlumniProfile();
  
  const [formData, setFormData] = useState<ProfileFormData>({
    name: '',
    bio: '',
    currentRole: '',
    company: '',
    industry: '',
    city: '',
    province: '',
    skills: '',
    interests: '',
    graduationYear: '',
    programmes: '',
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (profile) {
      setFormData({
        name: profile.name || '',
        bio: profile.bio || '',
        currentRole: profile.currentRole || '',
        company: profile.company || '',
        industry: profile.industry || '',
        city: profile.city || '',
        province: profile.province || '',
        skills: profile.skills?.join(', ') || '',
        interests: profile.interests?.join(', ') || '',
        graduationYear: profile.graduationYear?.toString() || '',
        programmes: profile.programmes?.join(', ') || '',
      });
    }
  }, [profile]);

  const handleInputChange = (field: keyof ProfileFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSave = async () => {
    if (!formData.name.trim()) {
      Alert.alert('Error', 'Name is required');
      return;
    }

    setIsSubmitting(true);
    try {
      const updateData = {
        id: user?.id,
        name: formData.name.trim(),
        bio: formData.bio.trim(),
        currentRole: formData.currentRole.trim(),
        company: formData.company.trim(),
        industry: formData.industry.trim(),
        city: formData.city.trim(),
        province: formData.province.trim(),
        skills: formData.skills
          .split(',')
          .map(s => s.trim())
          .filter(s => s.length > 0),
        interests: formData.interests
          .split(',')
          .map(s => s.trim())
          .filter(s => s.length > 0),
        graduationYear: formData.graduationYear ? parseInt(formData.graduationYear) : undefined,
        programmes: formData.programmes
          .split(',')
          .map(s => s.trim())
          .filter(s => s.length > 0),
      };

      await updateProfile.mutateAsync(updateData);
      Alert.alert('Success', 'Profile updated successfully', [
        { text: 'OK', onPress: () => router.back() }
      ]);
    } catch (error) {
      Alert.alert('Error', 'Failed to update profile. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    router.back();
  };

  if (isLoading) {
    return (
      <ThemedView style={styles.container}>
        <LoadingSpinner text="Loading profile..." />
      </ThemedView>
    );
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ThemedView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={handleCancel}>
            <ThemedText style={styles.cancelButton}>Cancel</ThemedText>
          </TouchableOpacity>
          <ThemedText type="subtitle">Edit Profile</ThemedText>
          <TouchableOpacity onPress={handleSave} disabled={isSubmitting}>
            <ThemedText style={[styles.saveButton, isSubmitting && styles.disabledButton]}>
              {isSubmitting ? 'Saving...' : 'Save'}
            </ThemedText>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Basic Information */}
          <Card variant="outlined" style={styles.section}>
            <ThemedText type="subtitle" style={styles.sectionTitle}>
              Basic Information
            </ThemedText>
            
            <Input
              label="Full Name *"
              value={formData.name}
              onChangeText={(value) => handleInputChange('name', value)}
              placeholder="Enter your full name"
            />
            
            <Input
              label="Bio"
              value={formData.bio}
              onChangeText={(value) => handleInputChange('bio', value)}
              placeholder="Tell us about yourself..."
              multiline
              numberOfLines={4}
              style={styles.textArea}
            />
          </Card>

          {/* Professional Information */}
          <Card variant="outlined" style={styles.section}>
            <ThemedText type="subtitle" style={styles.sectionTitle}>
              Professional Information
            </ThemedText>
            
            <Input
              label="Current Role"
              value={formData.currentRole}
              onChangeText={(value) => handleInputChange('currentRole', value)}
              placeholder="e.g. Software Engineer"
            />
            
            <Input
              label="Company"
              value={formData.company}
              onChangeText={(value) => handleInputChange('company', value)}
              placeholder="e.g. Google"
            />
            
            <Input
              label="Industry"
              value={formData.industry}
              onChangeText={(value) => handleInputChange('industry', value)}
              placeholder="e.g. Technology"
            />
          </Card>

          {/* Education */}
          <Card variant="outlined" style={styles.section}>
            <ThemedText type="subtitle" style={styles.sectionTitle}>
              Education
            </ThemedText>
            
            <Input
              label="Graduation Year"
              value={formData.graduationYear}
              onChangeText={(value) => handleInputChange('graduationYear', value)}
              placeholder="e.g. 2020"
              keyboardType="numeric"
            />
            
            <Input
              label="Programmes"
              value={formData.programmes}
              onChangeText={(value) => handleInputChange('programmes', value)}
              placeholder="e.g. Computer Science, Mathematics"
              helperText="Separate multiple programmes with commas"
            />
          </Card>

          {/* Location */}
          <Card variant="outlined" style={styles.section}>
            <ThemedText type="subtitle" style={styles.sectionTitle}>
              Location
            </ThemedText>
            
            <Input
              label="City"
              value={formData.city}
              onChangeText={(value) => handleInputChange('city', value)}
              placeholder="e.g. Johannesburg"
            />
            
            <Input
              label="Province"
              value={formData.province}
              onChangeText={(value) => handleInputChange('province', value)}
              placeholder="e.g. Gauteng"
            />
          </Card>

          {/* Skills & Interests */}
          <Card variant="outlined" style={styles.section}>
            <ThemedText type="subtitle" style={styles.sectionTitle}>
              Skills & Interests
            </ThemedText>
            
            <Input
              label="Skills"
              value={formData.skills}
              onChangeText={(value) => handleInputChange('skills', value)}
              placeholder="e.g. JavaScript, React, Python"
              helperText="Separate multiple skills with commas"
            />
            
            <Input
              label="Interests"
              value={formData.interests}
              onChangeText={(value) => handleInputChange('interests', value)}
              placeholder="e.g. AI, Web Development, Data Science"
              helperText="Separate multiple interests with commas"
            />
          </Card>

          <View style={styles.bottomPadding} />
        </ScrollView>
      </ThemedView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  cancelButton: {
    color: '#666',
    fontSize: 16,
  },
  saveButton: {
    color: '#012A5B',
    fontSize: 16,
    fontWeight: '600',
  },
  disabledButton: {
    opacity: 0.5,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 16,
  },
  sectionTitle: {
    marginBottom: 16,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  bottomPadding: {
    height: 32,
  },
});
