import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  FlatList,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Button, Card, Input, LoadingSpinner } from '@/components/ui';
import { ContactsService } from '@/lib/services/contactsService';
import { useAlumni } from '@/lib/hooks/useAlumni';
import { useAuth } from '@/lib/providers/AuthProvider';
import { DesignSystem } from '@/constants/DesignSystem';
import { Ionicons } from '@expo/vector-icons';
import { useThemeColor } from '@/hooks/useThemeColor';
import { router } from 'expo-router';
import * as Contacts from 'expo-contacts';

export default function ContactsScreen() {
  const [activeTab, setActiveTab] = useState<'alumni' | 'device' | 'protec'>('alumni');
  const [searchQuery, setSearchQuery] = useState('');
  const [deviceContacts, setDeviceContacts] = useState<Contacts.Contact[]>([]);
  const [protecContacts, setProtecContacts] = useState<Contacts.Contact[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [hasPermission, setHasPermission] = useState(false);
  const [exportProgress, setExportProgress] = useState<{ current: number; total: number } | null>(null);

  const { user } = useAuth();
  const primaryColor = useThemeColor({}, 'primary');
  const textColor = useThemeColor({}, 'text');

  const { data: alumniData, isLoading: isLoadingAlumni } = useAlumni({
    search: searchQuery,
    limit: 50,
  });

  useEffect(() => {
    checkPermissions();
  }, []);

  const checkPermissions = async () => {
    const permission = await ContactsService.hasPermissions();
    setHasPermission(permission);
    
    if (permission) {
      loadDeviceContacts();
      loadProtecContacts();
    }
  };

  const requestPermissions = async () => {
    const granted = await ContactsService.requestPermissions();
    setHasPermission(granted);
    
    if (granted) {
      loadDeviceContacts();
      loadProtecContacts();
    } else {
      Alert.alert(
        'Permission Required',
        'Contacts permission is required to manage alumni contacts.',
        [{ text: 'OK' }]
      );
    }
  };

  const loadDeviceContacts = async () => {
    try {
      setIsLoading(true);
      const contacts = await ContactsService.getAllContacts();
      setDeviceContacts(contacts);
    } catch (error) {
      console.error('Error loading device contacts:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadProtecContacts = async () => {
    try {
      const contacts = await ContactsService.findProtecAlumniInContacts();
      setProtecContacts(contacts);
    } catch (error) {
      console.error('Error loading PROTEC contacts:', error);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      if (hasPermission) {
        await Promise.all([
          loadDeviceContacts(),
          loadProtecContacts(),
        ]);
      }
    } finally {
      setRefreshing(false);
    }
  };

  const handleAddToContacts = async (alumni: any) => {
    if (!hasPermission) {
      await requestPermissions();
      return;
    }

    await ContactsService.showAddToContactsDialog(alumni);
    // Refresh contacts after adding
    await loadProtecContacts();
  };

  const handleBulkExport = async () => {
    if (!hasPermission) {
      await requestPermissions();
      return;
    }

    if (!alumniData?.alumni || alumniData.alumni.length === 0) {
      Alert.alert('No Alumni', 'No alumni data available to export.');
      return;
    }

    setExportProgress({ current: 0, total: alumniData.alumni.length });

    const success = await ContactsService.showBulkExportDialog(
      alumniData.alumni,
      (current, total) => {
        setExportProgress({ current, total });
      }
    );

    setExportProgress(null);

    if (success) {
      await loadProtecContacts();
    }
  };

  const renderTabBar = () => (
    <View style={styles.tabBar}>
      {[
        { key: 'alumni', label: 'Alumni Directory', icon: 'school' },
        { key: 'device', label: 'Device Contacts', icon: 'phone-portrait' },
        { key: 'protec', label: 'PROTEC Contacts', icon: 'people' },
      ].map(tab => (
        <TouchableOpacity
          key={tab.key}
          style={[
            styles.tab,
            activeTab === tab.key && { backgroundColor: primaryColor + '20', borderColor: primaryColor }
          ]}
          onPress={() => setActiveTab(tab.key as any)}
        >
          <Ionicons
            name={tab.icon as any}
            size={16}
            color={activeTab === tab.key ? primaryColor : textColor}
          />
          <ThemedText
            style={[
              styles.tabText,
              activeTab === tab.key && { color: primaryColor, fontWeight: '600' }
            ]}
          >
            {tab.label}
          </ThemedText>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderAlumniItem = ({ item }: { item: any }) => (
    <Card style={styles.contactCard}>
      <View style={styles.contactHeader}>
        <View style={styles.contactInfo}>
          <View style={styles.avatar}>
            <ThemedText style={styles.avatarText}>
              {item.name.charAt(0).toUpperCase()}
            </ThemedText>
          </View>
          <View style={styles.contactDetails}>
            <ThemedText style={styles.contactName}>{item.name}</ThemedText>
            <ThemedText style={styles.contactMeta}>
              {item.currentRole && `${item.currentRole} • `}
              Class of {item.graduationYear}
            </ThemedText>
            {item.company && (
              <ThemedText style={styles.contactCompany}>{item.company}</ThemedText>
            )}
          </View>
        </View>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => handleAddToContacts(item)}
        >
          <Ionicons name="person-add" size={20} color={primaryColor} />
        </TouchableOpacity>
      </View>
    </Card>
  );

  const renderContactItem = ({ item }: { item: Contacts.Contact }) => (
    <Card style={styles.contactCard}>
      <View style={styles.contactHeader}>
        <View style={styles.contactInfo}>
          <View style={styles.avatar}>
            <ThemedText style={styles.avatarText}>
              {item.name?.charAt(0).toUpperCase() || '?'}
            </ThemedText>
          </View>
          <View style={styles.contactDetails}>
            <ThemedText style={styles.contactName}>{item.name || 'Unknown'}</ThemedText>
            {item.emails && item.emails.length > 0 && (
              <ThemedText style={styles.contactMeta}>{item.emails[0].email}</ThemedText>
            )}
            {item.company && (
              <ThemedText style={styles.contactCompany}>{item.company}</ThemedText>
            )}
          </View>
        </View>
      </View>
    </Card>
  );

  const renderEmptyState = () => (
    <Card variant="outlined" style={styles.emptyState}>
      <ThemedText style={styles.emptyTitle}>
        {activeTab === 'alumni' && 'No alumni found'}
        {activeTab === 'device' && 'No contacts found'}
        {activeTab === 'protec' && 'No PROTEC contacts found'}
      </ThemedText>
      <ThemedText style={styles.emptyDescription}>
        {activeTab === 'alumni' && 'Try adjusting your search or check your connection.'}
        {activeTab === 'device' && hasPermission ? 'No contacts found on your device.' : 'Grant contacts permission to view your contacts.'}
        {activeTab === 'protec' && 'Add alumni to your contacts to see them here.'}
      </ThemedText>
      {!hasPermission && (
        <Button
          title="Grant Permission"
          onPress={requestPermissions}
          style={styles.emptyButton}
        />
      )}
    </Card>
  );

  const getDisplayedData = () => {
    switch (activeTab) {
      case 'device':
        return deviceContacts.filter(contact =>
          !searchQuery || 
          contact.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          contact.emails?.some(email => email.email?.toLowerCase().includes(searchQuery.toLowerCase()))
        );
      case 'protec':
        return protecContacts.filter(contact =>
          !searchQuery || 
          contact.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          contact.emails?.some(email => email.email?.toLowerCase().includes(searchQuery.toLowerCase()))
        );
      default:
        return alumniData?.alumni || [];
    }
  };

  const displayedData = getDisplayedData();
  const isCurrentlyLoading = isLoadingAlumni || isLoading;

  return (
    <ThemedView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <ThemedText type="title">Contacts</ThemedText>
          <ThemedText style={styles.subtitle}>
            Manage your alumni connections
          </ThemedText>
        </View>
        
        {activeTab === 'alumni' && (
          <Button
            title="Export All"
            onPress={handleBulkExport}
            size="sm"
            disabled={!hasPermission || !alumniData?.alumni?.length}
          />
        )}
      </View>

      {/* Tab Bar */}
      {renderTabBar()}

      {/* Search */}
      <View style={styles.searchContainer}>
        <Input
          placeholder={`Search ${activeTab === 'alumni' ? 'alumni' : 'contacts'}...`}
          value={searchQuery}
          onChangeText={setSearchQuery}
          leftIcon="search"
          style={styles.searchInput}
        />
      </View>

      {/* Export Progress */}
      {exportProgress && (
        <Card style={styles.progressCard}>
          <ThemedText style={styles.progressText}>
            Exporting contacts... {exportProgress.current}/{exportProgress.total}
          </ThemedText>
          <View style={styles.progressBar}>
            <View 
              style={[
                styles.progressFill,
                { width: `${(exportProgress.current / exportProgress.total) * 100}%` }
              ]} 
            />
          </View>
        </Card>
      )}

      {/* Content */}
      {isCurrentlyLoading ? (
        <LoadingSpinner text="Loading contacts..." />
      ) : (
        <FlatList
          data={displayedData}
          renderItem={activeTab === 'alumni' ? renderAlumniItem : renderContactItem}
          keyExtractor={(item, index) => 
            activeTab === 'alumni' ? item.id : (item.id || index.toString())
          }
          contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
          }
          ListEmptyComponent={renderEmptyState}
        />
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: DesignSystem.Colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    padding: DesignSystem.Spacing.lg,
    paddingBottom: DesignSystem.Spacing.sm,
    backgroundColor: DesignSystem.Colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: DesignSystem.Colors.border,
  },
  headerContent: {
    flex: 1,
  },
  subtitle: {
    fontSize: DesignSystem.Typography.caption.fontSize,
    color: DesignSystem.Colors.textSecondary,
    marginTop: DesignSystem.Spacing.xs,
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: DesignSystem.Colors.surface,
    paddingHorizontal: DesignSystem.Spacing.lg,
    paddingVertical: DesignSystem.Spacing.md,
    gap: DesignSystem.Spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: DesignSystem.Colors.border,
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: DesignSystem.Spacing.sm,
    paddingVertical: DesignSystem.Spacing.sm,
    borderRadius: DesignSystem.BorderRadius.md,
    borderWidth: 1,
    borderColor: DesignSystem.Colors.border,
    backgroundColor: DesignSystem.Colors.background,
    gap: DesignSystem.Spacing.xs,
    flex: 1,
  },
  tabText: {
    fontSize: DesignSystem.Typography.caption.fontSize,
    textAlign: 'center',
    flex: 1,
  },
  searchContainer: {
    padding: DesignSystem.Spacing.lg,
    backgroundColor: DesignSystem.Colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: DesignSystem.Colors.border,
  },
  searchInput: {
    marginBottom: 0,
  },
  progressCard: {
    margin: DesignSystem.Spacing.lg,
    padding: DesignSystem.Spacing.md,
  },
  progressText: {
    fontSize: DesignSystem.Typography.caption.fontSize,
    marginBottom: DesignSystem.Spacing.sm,
    textAlign: 'center',
  },
  progressBar: {
    height: 4,
    backgroundColor: DesignSystem.Colors.border,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: DesignSystem.Colors.primary,
  },
  listContent: {
    padding: DesignSystem.Spacing.lg,
    paddingTop: DesignSystem.Spacing.sm,
  },
  contactCard: {
    marginBottom: DesignSystem.Spacing.md,
  },
  contactHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  contactInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: DesignSystem.Colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: DesignSystem.Spacing.md,
  },
  avatarText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  contactDetails: {
    flex: 1,
  },
  contactName: {
    fontSize: DesignSystem.Typography.body.fontSize,
    fontWeight: '600',
    marginBottom: 2,
  },
  contactMeta: {
    fontSize: DesignSystem.Typography.caption.fontSize,
    color: DesignSystem.Colors.textSecondary,
    marginBottom: 2,
  },
  contactCompany: {
    fontSize: DesignSystem.Typography.caption.fontSize,
    color: DesignSystem.Colors.textSecondary,
  },
  addButton: {
    padding: DesignSystem.Spacing.sm,
    borderRadius: DesignSystem.BorderRadius.md,
    backgroundColor: DesignSystem.Colors.primary + '20',
  },
  emptyState: {
    alignItems: 'center',
    padding: DesignSystem.Spacing.xl,
  },
  emptyTitle: {
    fontSize: DesignSystem.Typography.h3.fontSize,
    fontWeight: '600',
    marginBottom: DesignSystem.Spacing.sm,
    textAlign: 'center',
  },
  emptyDescription: {
    fontSize: DesignSystem.Typography.body.fontSize,
    color: DesignSystem.Colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: DesignSystem.Spacing.lg,
  },
  emptyButton: {
    marginTop: DesignSystem.Spacing.sm,
  },
});
