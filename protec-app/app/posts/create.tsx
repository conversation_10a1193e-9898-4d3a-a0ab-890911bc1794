import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Card, Input } from '@/components/ui';
import { useAuthStore } from '@/lib/stores/authStore';
import * as ImagePicker from 'expo-image-picker';
import { router } from 'expo-router';
import React, { useState } from 'react';
import {
    Alert,
    Image,
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    StyleSheet,
    TouchableOpacity,
    View,
} from 'react-native';

interface PostFormData {
  content: string;
  tags: string;
  isPublic: boolean;
  mediaUrls: string[];
}

export default function CreatePostScreen() {
  const { user } = useAuthStore();
  
  const [formData, setFormData] = useState<PostFormData>({
    content: '',
    tags: '',
    isPublic: true,
    mediaUrls: [],
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedImages, setSelectedImages] = useState<string[]>([]);

  const handleInputChange = (field: keyof PostFormData, value: string | boolean | string[]) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const validateForm = (): boolean => {
    if (!formData.content.trim()) {
      Alert.alert('Error', 'Post content is required');
      return false;
    }
    
    if (formData.content.length > 1000) {
      Alert.alert('Error', 'Post content must be less than 1000 characters');
      return false;
    }

    return true;
  };

  const handleImagePicker = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Please grant camera roll permissions to upload images.');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsMultipleSelection: true,
        quality: 0.8,
        aspect: [4, 3],
      });

      if (!result.canceled && result.assets) {
        const newImages = result.assets.map(asset => asset.uri);
        setSelectedImages(prev => [...prev, ...newImages]);
        handleInputChange('mediaUrls', [...selectedImages, ...newImages]);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to pick images. Please try again.');
    }
  };

  const handleRemoveImage = (index: number) => {
    const newImages = selectedImages.filter((_, i) => i !== index);
    setSelectedImages(newImages);
    handleInputChange('mediaUrls', newImages);
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      const postData = {
        content: formData.content.trim(),
        tags: formData.tags
          .split(',')
          .map(tag => tag.trim())
          .filter(tag => tag.length > 0),
        isPublic: formData.isPublic,
        mediaUrls: formData.mediaUrls,
        authorId: user?.id,
      };

      // TODO: Implement API call to create post
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      Alert.alert(
        'Success',
        'Post created successfully!',
        [{ text: 'OK', onPress: () => router.back() }]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to create post. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    if (formData.content.trim() || selectedImages.length > 0) {
      Alert.alert(
        'Cancel Post',
        'Are you sure you want to cancel? All changes will be lost.',
        [
          { text: 'Continue Editing', style: 'cancel' },
          { text: 'Cancel', style: 'destructive', onPress: () => router.back() },
        ]
      );
    } else {
      router.back();
    }
  };

  const characterCount = formData.content.length;
  const isOverLimit = characterCount > 1000;

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ThemedView style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={handleCancel}>
            <ThemedText style={styles.cancelButton}>Cancel</ThemedText>
          </TouchableOpacity>
          <ThemedText type="subtitle">Create Post</ThemedText>
          <TouchableOpacity onPress={handleSubmit} disabled={isSubmitting || isOverLimit}>
            <ThemedText style={[
              styles.postButton,
              (isSubmitting || isOverLimit) && styles.disabledButton
            ]}>
              {isSubmitting ? 'Posting...' : 'Post'}
            </ThemedText>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Author Info */}
          <Card variant="outlined" style={styles.authorCard}>
            <View style={styles.authorInfo}>
              <View style={styles.authorAvatar}>
                <ThemedText style={styles.avatarText}>
                  {user?.name?.charAt(0).toUpperCase()}
                </ThemedText>
              </View>
              
              <View style={styles.authorDetails}>
                <ThemedText type="defaultSemiBold" style={styles.authorName}>
                  {user?.name}
                </ThemedText>
                <ThemedText style={styles.authorMeta}>
                  Posting to PROTEC Alumni Community
                </ThemedText>
              </View>
            </View>
          </Card>

          {/* Post Content */}
          <Card variant="outlined" style={styles.contentCard}>
            <Input
              value={formData.content}
              onChangeText={(value) => handleInputChange('content', value)}
              placeholder="What's on your mind? Share your achievements, insights, or connect with fellow alumni..."
              multiline
              numberOfLines={8}
              style={styles.contentInput}
              containerStyle={styles.contentInputContainer}
            />
            
            <View style={styles.characterCount}>
              <ThemedText style={[
                styles.characterCountText,
                isOverLimit && styles.characterCountError
              ]}>
                {characterCount}/1000
              </ThemedText>
            </View>
          </Card>

          {/* Media Upload */}
          <Card variant="outlined" style={styles.mediaCard}>
            <ThemedText type="subtitle" style={styles.sectionTitle}>
              Add Media
            </ThemedText>
            
            <TouchableOpacity
              style={styles.mediaUploadButton}
              onPress={handleImagePicker}
            >
              <ThemedText style={styles.mediaUploadIcon}>📷</ThemedText>
              <ThemedText style={styles.mediaUploadText}>
                Add Photos
              </ThemedText>
            </TouchableOpacity>

            {/* Selected Images */}
            {selectedImages.length > 0 && (
              <View style={styles.selectedImages}>
                <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                  {selectedImages.map((uri, index) => (
                    <View key={index} style={styles.imageContainer}>
                      <Image source={{ uri }} style={styles.selectedImage} />
                      <TouchableOpacity
                        style={styles.removeImageButton}
                        onPress={() => handleRemoveImage(index)}
                      >
                        <ThemedText style={styles.removeImageText}>×</ThemedText>
                      </TouchableOpacity>
                    </View>
                  ))}
                </ScrollView>
              </View>
            )}
          </Card>

          {/* Tags */}
          <Card variant="outlined" style={styles.tagsCard}>
            <ThemedText type="subtitle" style={styles.sectionTitle}>
              Tags
            </ThemedText>
            
            <Input
              value={formData.tags}
              onChangeText={(value) => handleInputChange('tags', value)}
              placeholder="career, achievement, networking, tech"
              helperText="Separate multiple tags with commas"
              containerStyle={styles.tagsInputContainer}
            />
          </Card>

          {/* Privacy Settings */}
          <Card variant="outlined" style={styles.privacyCard}>
            <ThemedText type="subtitle" style={styles.sectionTitle}>
              Privacy
            </ThemedText>
            
            <View style={styles.privacyOption}>
              <View style={styles.privacyInfo}>
                <ThemedText style={styles.privacyLabel}>
                  {formData.isPublic ? 'Public Post' : 'Alumni Only'}
                </ThemedText>
                <ThemedText style={styles.privacyDescription}>
                  {formData.isPublic 
                    ? 'Visible to all alumni and visitors'
                    : 'Only visible to PROTEC alumni'
                  }
                </ThemedText>
              </View>
              
              <TouchableOpacity
                style={[styles.toggle, formData.isPublic && styles.toggleActive]}
                onPress={() => handleInputChange('isPublic', !formData.isPublic)}
              >
                <View style={[styles.toggleThumb, formData.isPublic && styles.toggleThumbActive]} />
              </TouchableOpacity>
            </View>
          </Card>

          <View style={styles.bottomPadding} />
        </ScrollView>
      </ThemedView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  cancelButton: {
    color: '#666',
    fontSize: 16,
  },
  postButton: {
    color: '#012A5B',
    fontSize: 16,
    fontWeight: '600',
  },
  disabledButton: {
    opacity: 0.5,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  authorCard: {
    marginBottom: 16,
  },
  authorInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  authorAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#012A5B',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  avatarText: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  authorDetails: {
    flex: 1,
  },
  authorName: {
    fontSize: 16,
    marginBottom: 2,
  },
  authorMeta: {
    fontSize: 14,
    color: '#666',
  },
  contentCard: {
    marginBottom: 16,
  },
  contentInputContainer: {
    marginBottom: 0,
  },
  contentInput: {
    minHeight: 120,
    textAlignVertical: 'top',
    fontSize: 16,
    lineHeight: 22,
  },
  characterCount: {
    alignItems: 'flex-end',
    marginTop: 8,
  },
  characterCountText: {
    fontSize: 12,
    color: '#666',
  },
  characterCountError: {
    color: '#dc2626',
  },
  mediaCard: {
    marginBottom: 16,
  },
  sectionTitle: {
    marginBottom: 12,
  },
  mediaUploadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#ddd',
    borderStyle: 'dashed',
    backgroundColor: 'rgba(0, 0, 0, 0.02)',
  },
  mediaUploadIcon: {
    fontSize: 24,
    marginRight: 8,
  },
  mediaUploadText: {
    fontSize: 16,
    color: '#666',
  },
  selectedImages: {
    marginTop: 12,
  },
  imageContainer: {
    position: 'relative',
    marginRight: 8,
  },
  selectedImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
  },
  removeImageButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#dc2626',
    justifyContent: 'center',
    alignItems: 'center',
  },
  removeImageText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  tagsCard: {
    marginBottom: 16,
  },
  tagsInputContainer: {
    marginBottom: 0,
  },
  privacyCard: {
    marginBottom: 16,
  },
  privacyOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  privacyInfo: {
    flex: 1,
  },
  privacyLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  privacyDescription: {
    fontSize: 14,
    color: '#666',
  },
  toggle: {
    width: 50,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#ddd',
    justifyContent: 'center',
    paddingHorizontal: 2,
  },
  toggleActive: {
    backgroundColor: '#012A5B',
  },
  toggleThumb: {
    width: 26,
    height: 26,
    borderRadius: 13,
    backgroundColor: '#fff',
    alignSelf: 'flex-start',
  },
  toggleThumbActive: {
    alignSelf: 'flex-end',
  },
  bottomPadding: {
    height: 32,
  },
});
