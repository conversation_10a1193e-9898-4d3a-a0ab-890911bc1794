import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { <PERSON><PERSON>, Card } from '@/components/ui';
import { useAuthStore } from '@/lib/stores/authStore';
import { Comment, Post } from '@/lib/types';
import { format } from 'date-fns';
import { router, useLocalSearchParams } from 'expo-router';
import React, { useState } from 'react';
import {
    Alert,
    Image,
    ScrollView,
    Share,
    StyleSheet,
    TextInput,
    TouchableOpacity,
    View,
} from 'react-native';

export default function PostDetailsScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { user, isAuthenticated } = useAuthStore();
  
  // Mock data - replace with actual API call
  const [post] = useState<Post>({
    id: id || '1',
    author: {
      id: '1',
      email: '<EMAIL>',
      name: '<PERSON>',
      graduationYear: 2020,
      programmes: ['Computer Science'],
      currentRole: 'Software Engineer',
      company: 'Tech Corp',
      industry: 'Technology',
      skills: ['JavaScript', 'React'],
      interests: ['AI'],
      province: 'Gauteng',
      city: 'Johannesburg',
      country: 'South Africa',
      role: 'ALUMNI' as const,
      isActive: true,
    },
    authorId: '1',
    content: 'Excited to share that I just got promoted to Senior Software Engineer! 🎉 Thank you PROTEC for giving me the foundation to succeed in tech. The journey from student to senior engineer has been incredible, and I\'m grateful for all the support from the PROTEC community.',
    mediaUrls: [],
    tags: ['career', 'promotion', 'tech'],
    likes: ['2', '3'],
    comments: [
      {
        id: '1',
        author: {
          id: '2',
          name: 'Jane Smith',
          email: '<EMAIL>',
          role: 'ALUMNI' as const,
          isActive: true,
          province: 'Western Cape',
          city: 'Cape Town',
          country: 'South Africa',
        },
        authorId: '2',
        postId: id || '1',
        text: 'Congratulations! Well deserved! 🎉',
        createdAt: new Date('2024-01-15T11:00:00'),
        updatedAt: new Date('2024-01-15T11:00:00'),
      },
    ],
    isModerated: true,
    isPublic: true,
    isPinned: false,
    createdAt: new Date('2024-01-15T10:30:00'),
    updatedAt: new Date('2024-01-15T10:30:00'),
  });

  const [isLiked, setIsLiked] = useState(post.likes.includes(user?.id || ''));
  const [likesCount, setLikesCount] = useState(post.likes.length);
  const [comments, setComments] = useState(post.comments);
  const [newComment, setNewComment] = useState('');
  const [isSubmittingComment, setIsSubmittingComment] = useState(false);

  const handleLike = () => {
    if (!isAuthenticated) {
      Alert.alert('Login Required', 'Please log in to like posts.');
      return;
    }

    // TODO: Implement API call to like/unlike post
    if (isLiked) {
      setLikesCount(prev => prev - 1);
      setIsLiked(false);
    } else {
      setLikesCount(prev => prev + 1);
      setIsLiked(true);
    }
  };

  const handleShare = async () => {
    try {
      await Share.share({
        message: `Check out this post from ${post.author.name}: ${post.content}`,
        title: 'PROTEC Alumni Post',
      });
    } catch (error) {
      console.error('Error sharing post:', error);
    }
  };

  const handleSubmitComment = async () => {
    if (!isAuthenticated) {
      Alert.alert('Login Required', 'Please log in to comment on posts.');
      return;
    }

    if (!newComment.trim()) {
      Alert.alert('Error', 'Please enter a comment.');
      return;
    }

    setIsSubmittingComment(true);
    try {
      // TODO: Implement API call to create comment
      const comment: Comment = {
        id: Date.now().toString(),
        author: {
          id: user!.id,
          name: user!.name,
          email: user!.email,
          role: user!.role,
          isActive: user!.isActive,
          province: 'Gauteng',
          city: 'Johannesburg',
          country: 'South Africa',
        },
        authorId: user!.id,
        postId: post.id,
        text: newComment.trim(),
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      setComments(prev => [...prev, comment]);
      setNewComment('');
    } catch (error) {
      Alert.alert('Error', 'Failed to post comment. Please try again.');
    } finally {
      setIsSubmittingComment(false);
    }
  };

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`;
    return format(date, 'MMM dd, yyyy');
  };

  return (
    <ThemedView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <ThemedText style={styles.backButton}>‹ Back</ThemedText>
        </TouchableOpacity>
        <ThemedText type="subtitle">Post</ThemedText>
        <TouchableOpacity onPress={handleShare}>
          <ThemedText style={styles.shareButton}>Share</ThemedText>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Post */}
        <Card variant="elevated" style={styles.postCard}>
          {/* Post Header */}
          <View style={styles.postHeader}>
            <TouchableOpacity
              style={styles.authorInfo}
              onPress={() => router.push(`/alumni/${post.author.id}`)}
            >
              <View style={styles.authorAvatar}>
                <ThemedText style={styles.avatarText}>
                  {post.author.name.charAt(0).toUpperCase()}
                </ThemedText>
              </View>
              
              <View style={styles.authorDetails}>
                <ThemedText type="defaultSemiBold" style={styles.authorName}>
                  {post.author.name}
                </ThemedText>
                <ThemedText style={styles.authorMeta}>
                  {post.author.currentRole} • {formatTimeAgo(post.createdAt)}
                </ThemedText>
              </View>
            </TouchableOpacity>
          </View>

          {/* Post Content */}
          <ThemedText style={styles.postContent}>
            {post.content}
          </ThemedText>

          {/* Post Media */}
          {post.mediaUrls && post.mediaUrls.length > 0 && (
            <View style={styles.mediaContainer}>
              {post.mediaUrls.map((url, index) => (
                <Image key={index} source={{ uri: url }} style={styles.mediaImage} />
              ))}
            </View>
          )}

          {/* Post Tags */}
          {post.tags && post.tags.length > 0 && (
            <View style={styles.tagsContainer}>
              {post.tags.map((tag, index) => (
                <View key={index} style={styles.tag}>
                  <ThemedText style={styles.tagText}>#{tag}</ThemedText>
                </View>
              ))}
            </View>
          )}

          {/* Post Actions */}
          <View style={styles.postActions}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={handleLike}
            >
              <ThemedText style={[styles.actionIcon, isLiked && styles.likedIcon]}>
                {isLiked ? '❤️' : '🤍'}
              </ThemedText>
              <ThemedText style={[styles.actionText, isLiked && styles.likedText]}>
                {likesCount} {likesCount === 1 ? 'like' : 'likes'}
              </ThemedText>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.actionButton}>
              <ThemedText style={styles.actionIcon}>💬</ThemedText>
              <ThemedText style={styles.actionText}>
                {comments.length} {comments.length === 1 ? 'comment' : 'comments'}
              </ThemedText>
            </TouchableOpacity>
          </View>
        </Card>

        {/* Comments Section */}
        <Card variant="elevated" style={styles.commentsCard}>
          <ThemedText type="subtitle" style={styles.commentsTitle}>
            Comments ({comments.length})
          </ThemedText>

          {/* Add Comment */}
          {isAuthenticated && (
            <View style={styles.addCommentContainer}>
              <View style={styles.commentInputContainer}>
                <TextInput
                  style={styles.commentInput}
                  value={newComment}
                  onChangeText={setNewComment}
                  placeholder="Write a comment..."
                  multiline
                  maxLength={500}
                />
              </View>
              <Button
                title={isSubmittingComment ? 'Posting...' : 'Post'}
                onPress={handleSubmitComment}
                disabled={isSubmittingComment || !newComment.trim()}
                size="sm"
                style={styles.commentButton}
              />
            </View>
          )}

          {/* Comments List */}
          <View style={styles.commentsList}>
            {comments.length === 0 ? (
              <ThemedText style={styles.noComments}>
                No comments yet. Be the first to comment!
              </ThemedText>
            ) : (
              comments.map((comment) => (
                <View key={comment.id} style={styles.comment}>
                  <TouchableOpacity
                    onPress={() => router.push(`/alumni/${comment.author.id}`)}
                  >
                    <View style={styles.commentHeader}>
                      <View style={styles.commentAvatar}>
                        <ThemedText style={styles.commentAvatarText}>
                          {comment.author.name.charAt(0).toUpperCase()}
                        </ThemedText>
                      </View>
                      <View style={styles.commentInfo}>
                        <ThemedText type="defaultSemiBold" style={styles.commentAuthor}>
                          {comment.author.name}
                        </ThemedText>
                        <ThemedText style={styles.commentTime}>
                          {formatTimeAgo(comment.createdAt)}
                        </ThemedText>
                      </View>
                    </View>
                  </TouchableOpacity>
                  
                  <ThemedText style={styles.commentText}>
                    {comment.text}
                  </ThemedText>
                </View>
              ))
            )}
          </View>
        </Card>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  backButton: {
    color: '#012A5B',
    fontSize: 18,
    fontWeight: '600',
  },
  shareButton: {
    color: '#012A5B',
    fontSize: 16,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  postCard: {
    marginBottom: 16,
  },
  postHeader: {
    marginBottom: 12,
  },
  authorInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  authorAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#012A5B',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  avatarText: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  authorDetails: {
    flex: 1,
  },
  authorName: {
    fontSize: 16,
    marginBottom: 2,
  },
  authorMeta: {
    fontSize: 12,
    color: '#666',
  },
  postContent: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 16,
  },
  mediaContainer: {
    marginBottom: 16,
  },
  mediaImage: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    marginBottom: 8,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6,
    marginBottom: 16,
  },
  tag: {
    backgroundColor: 'rgba(1, 42, 91, 0.1)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  tagText: {
    fontSize: 12,
    color: '#012A5B',
    fontWeight: '500',
  },
  postActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  actionIcon: {
    fontSize: 18,
  },
  actionText: {
    fontSize: 14,
    color: '#666',
  },
  likedIcon: {
    color: '#e74c3c',
  },
  likedText: {
    color: '#e74c3c',
    fontWeight: '500',
  },
  commentsCard: {
    marginBottom: 16,
  },
  commentsTitle: {
    marginBottom: 16,
  },
  addCommentContainer: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  commentInputContainer: {
    flex: 1,
  },
  commentInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    maxHeight: 100,
    textAlignVertical: 'top',
  },
  commentButton: {
    alignSelf: 'flex-start',
  },
  commentsList: {
    gap: 16,
  },
  noComments: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    fontStyle: 'italic',
    paddingVertical: 24,
  },
  comment: {
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.05)',
  },
  commentHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  commentAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#012A5B',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  commentAvatarText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  commentInfo: {
    flex: 1,
  },
  commentAuthor: {
    fontSize: 14,
    marginBottom: 2,
  },
  commentTime: {
    fontSize: 12,
    color: '#888',
  },
  commentText: {
    fontSize: 14,
    lineHeight: 20,
    marginLeft: 40,
  },
});
