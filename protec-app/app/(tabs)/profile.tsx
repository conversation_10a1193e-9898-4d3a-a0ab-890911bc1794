import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Button, Card, LoadingSpinner } from '@/components/ui';
import { useAlumniProfile } from '@/lib/hooks/useAlumni';
import { useAuthStore } from '@/lib/stores/authStore';
import { router } from 'expo-router';
import React from 'react';
import { Image, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';

export default function ProfileScreen() {
  const { user, isAuthenticated, logout } = useAuthStore();
  const { data: profile, isLoading } = useAlumniProfile(user?.id || '');

  const handleEditProfile = () => {
    router.push('/profile/edit');
  };

  const handleLogout = async () => {
    await logout();
    router.replace('/auth/login');
  };

  if (!isAuthenticated || !user) {
    return (
      <ThemedView style={styles.container}>
        <View style={styles.emptyState}>
          <ThemedText type="subtitle">Please log in to view your profile</ThemedText>
          <Button
            title="Go to Login"
            onPress={() => router.replace('/auth/login')}
            style={styles.loginButton}
          />
        </View>
      </ThemedView>
    );
  }

  if (isLoading) {
    return (
      <ThemedView style={styles.container}>
        <LoadingSpinner text="Loading profile..." />
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Profile Header */}
        <Card variant="elevated" style={styles.headerCard}>
          <View style={styles.profileHeader}>
            <View style={styles.avatarContainer}>
              {profile?.photoUrl ? (
                <Image source={{ uri: profile.photoUrl }} style={styles.avatar} />
              ) : (
                <View style={styles.avatarPlaceholder}>
                  <ThemedText style={styles.avatarText}>
                    {user.name.charAt(0).toUpperCase()}
                  </ThemedText>
                </View>
              )}
            </View>

            <View style={styles.profileInfo}>
              <ThemedText type="title" style={styles.name}>
                {user.name}
              </ThemedText>

              {profile?.currentRole && (
                <ThemedText style={styles.role}>
                  {profile.currentRole}
                  {profile.company && ` at ${profile.company}`}
                </ThemedText>
              )}

              <ThemedText style={styles.email}>{user.email}</ThemedText>

              {profile?.graduationYear && (
                <ThemedText style={styles.graduationYear}>
                  Class of {profile.graduationYear}
                </ThemedText>
              )}
            </View>
          </View>

          {profile?.bio && (
            <ThemedText style={styles.bio}>{profile.bio}</ThemedText>
          )}

          <View style={styles.headerActions}>
            <Button
              title="Edit Profile"
              onPress={handleEditProfile}
              variant="outline"
              size="sm"
            />
          </View>
        </Card>

        {/* Quick Stats */}
        <Card variant="elevated" style={styles.statsCard}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>
            Quick Stats
          </ThemedText>

          <View style={styles.statsGrid}>
            <View style={styles.statItem}>
              <ThemedText style={styles.statNumber}>0</ThemedText>
              <ThemedText style={styles.statLabel}>Connections</ThemedText>
            </View>
            <View style={styles.statItem}>
              <ThemedText style={styles.statNumber}>0</ThemedText>
              <ThemedText style={styles.statLabel}>Posts</ThemedText>
            </View>
            <View style={styles.statItem}>
              <ThemedText style={styles.statNumber}>0</ThemedText>
              <ThemedText style={styles.statLabel}>Events</ThemedText>
            </View>
            <View style={styles.statItem}>
              <ThemedText style={styles.statNumber}>0</ThemedText>
              <ThemedText style={styles.statLabel}>Donations</ThemedText>
            </View>
          </View>
        </Card>

        {/* Skills & Interests */}
        {(profile?.skills?.length || profile?.interests?.length) && (
          <Card variant="elevated" style={styles.skillsCard}>
            <ThemedText type="subtitle" style={styles.sectionTitle}>
              Skills & Interests
            </ThemedText>

            {profile.skills && profile.skills.length > 0 && (
              <View style={styles.skillsSection}>
                <ThemedText style={styles.subsectionTitle}>Skills</ThemedText>
                <View style={styles.tagsContainer}>
                  {profile.skills.map((skill, index) => (
                    <View key={index} style={styles.skillTag}>
                      <ThemedText style={styles.skillText}>{skill}</ThemedText>
                    </View>
                  ))}
                </View>
              </View>
            )}

            {profile.interests && profile.interests.length > 0 && (
              <View style={styles.skillsSection}>
                <ThemedText style={styles.subsectionTitle}>Interests</ThemedText>
                <View style={styles.tagsContainer}>
                  {profile.interests.map((interest, index) => (
                    <View key={index} style={styles.interestTag}>
                      <ThemedText style={styles.interestText}>{interest}</ThemedText>
                    </View>
                  ))}
                </View>
              </View>
            )}
          </Card>
        )}

        {/* Location */}
        {(profile?.city || profile?.province) && (
          <Card variant="elevated" style={styles.locationCard}>
            <ThemedText type="subtitle" style={styles.sectionTitle}>
              Location
            </ThemedText>
            <ThemedText style={styles.locationText}>
              📍 {profile.city}, {profile.province}, {profile.country || 'South Africa'}
            </ThemedText>
          </Card>
        )}

        {/* Account Settings */}
        <Card variant="elevated" style={styles.settingsCard}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>
            Account
          </ThemedText>

          <TouchableOpacity
            style={styles.settingItem}
            onPress={() => router.push('/profile/career')}
          >
            <ThemedText style={styles.settingText}>Career History</ThemedText>
            <ThemedText style={styles.settingArrow}>›</ThemedText>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.settingItem}
            onPress={() => router.push('/settings')}
          >
            <ThemedText style={styles.settingText}>Settings</ThemedText>
            <ThemedText style={styles.settingArrow}>›</ThemedText>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.settingItem}
            onPress={() => router.push('/donations/history')}
          >
            <ThemedText style={styles.settingText}>Donation History</ThemedText>
            <ThemedText style={styles.settingArrow}>›</ThemedText>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.settingItem}
            onPress={() => router.push('/privacy')}
          >
            <ThemedText style={styles.settingText}>Privacy Settings</ThemedText>
            <ThemedText style={styles.settingArrow}>›</ThemedText>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.settingItem, styles.logoutItem]}
            onPress={handleLogout}
          >
            <ThemedText style={styles.logoutText}>Logout</ThemedText>
          </TouchableOpacity>
        </Card>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  loginButton: {
    marginTop: 16,
  },
  headerCard: {
    marginBottom: 16,
  },
  profileHeader: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  avatarContainer: {
    marginRight: 16,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  avatarPlaceholder: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#012A5B',
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    color: '#fff',
    fontSize: 32,
    fontWeight: 'bold',
  },
  profileInfo: {
    flex: 1,
  },
  name: {
    fontSize: 24,
    marginBottom: 4,
  },
  role: {
    fontSize: 16,
    color: '#666',
    marginBottom: 4,
  },
  email: {
    fontSize: 14,
    color: '#888',
    marginBottom: 4,
  },
  graduationYear: {
    fontSize: 14,
    color: '#012A5B',
    fontWeight: '500',
  },
  bio: {
    fontSize: 14,
    lineHeight: 20,
    color: '#666',
    marginBottom: 16,
  },
  headerActions: {
    alignItems: 'flex-end',
  },
  statsCard: {
    marginBottom: 16,
  },
  sectionTitle: {
    marginBottom: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#012A5B',
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  skillsCard: {
    marginBottom: 16,
  },
  skillsSection: {
    marginBottom: 16,
  },
  subsectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  skillTag: {
    backgroundColor: 'rgba(1, 42, 91, 0.1)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  skillText: {
    fontSize: 12,
    color: '#012A5B',
    fontWeight: '500',
  },
  interestTag: {
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  interestText: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
  },
  locationCard: {
    marginBottom: 16,
  },
  locationText: {
    fontSize: 14,
    color: '#666',
  },
  settingsCard: {
    marginBottom: 32,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  settingText: {
    fontSize: 16,
  },
  settingArrow: {
    fontSize: 18,
    color: '#666',
  },
  logoutItem: {
    borderBottomWidth: 0,
  },
  logoutText: {
    fontSize: 16,
    color: '#dc2626',
    fontWeight: '500',
  },
});
