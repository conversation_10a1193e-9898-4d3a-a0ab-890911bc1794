import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Button, Card, EventCard, Input, LoadingSpinner, Modal } from '@/components/ui';
import { useEvents, useMyEvents, useMyRSVPs } from '@/lib/hooks/useEvents';
import { useAuth } from '@/lib/providers/AuthProvider';
import { useEventsStore } from '@/lib/stores/eventsStore';
import { Event } from '@/lib/types';
import { DesignSystem } from '@/constants/DesignSystem';
import { Ionicons } from '@expo/vector-icons';
import { useThemeColor } from '@/hooks/useThemeColor';
import { router } from 'expo-router';
import React, { useEffect, useState, useMemo } from 'react';
import {
    FlatList,
    RefreshControl,
    ScrollView,
    StyleSheet,
    TouchableOpacity,
    View,
} from 'react-native';

export default function EventsScreen() {
  const [activeTab, setActiveTab] = useState<'all' | 'rsvp' | 'organized' | 'past'>('all');
  const [showFilters, setShowFilters] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  const { user } = useAuth();
  const {
    events,
    myEvents,
    filters,
    isLoading,
    setFilters,
    clearFilters,
  } = useEventsStore();

  const primaryColor = useThemeColor({}, 'primary');
  const textColor = useThemeColor({}, 'text');

  const { data: eventsData, refetch, isLoading: isLoadingEvents } = useEvents(filters);
  const { data: myRSVPsData, isLoading: isLoadingRSVPs } = useMyRSVPs();
  const { data: organizedEventsData, isLoading: isLoadingOrganized } = useMyEvents();

  const canCreateEvents = user?.role === 'EVENT_ORGANIZER' || user?.role === 'ADMIN';

  useEffect(() => {
    // Update store when data changes
    if (eventsData) {
      // This would be handled by the store in the actual implementation
    }
  }, [eventsData]);

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await refetch();
    } finally {
      setRefreshing(false);
    }
  };

  const handleFilterChange = (key: string, value: any) => {
    setFilters({ [key]: value });
  };

  const handleClearFilters = () => {
    clearFilters();
  };

  const handleCreateEvent = () => {
    router.push('/events/create');
  };

  const displayedEvents = useMemo(() => {
    const now = new Date();

    switch (activeTab) {
      case 'rsvp':
        return myRSVPsData?.map(rsvp => rsvp.event).filter(event => new Date(event.endTime) >= now) || [];
      case 'organized':
        return organizedEventsData?.filter(event => new Date(event.endTime) >= now) || [];
      case 'past':
        const allEvents = [
          ...(eventsData?.events || []),
          ...(myRSVPsData?.map(rsvp => rsvp.event) || []),
          ...(organizedEventsData || [])
        ];
        // Remove duplicates and filter past events
        const uniquePastEvents = allEvents
          .filter((event, index, self) =>
            index === self.findIndex(e => e.id === event.id) &&
            new Date(event.endTime) < now
          );
        return uniquePastEvents;
      default:
        return eventsData?.events?.filter(event => new Date(event.endTime) >= now) || [];
    }
  }, [activeTab, eventsData, myRSVPsData, organizedEventsData]);

  const isCurrentlyLoading = isLoadingEvents ||
    (activeTab === 'rsvp' && isLoadingRSVPs) ||
    (activeTab === 'organized' && isLoadingOrganized);

  const renderEventItem = ({ item }: { item: Event }) => (
    <EventCard event={item} />
  );

  const renderTabBar = () => (
    <View style={styles.tabBar}>
      {[
        { key: 'all', label: 'All Events', icon: 'calendar' },
        { key: 'rsvp', label: 'My RSVPs', icon: 'checkmark-circle' },
        { key: 'organized', label: 'Organized', icon: 'create' },
        { key: 'past', label: 'Past', icon: 'time' },
      ].map(tab => (
        <TouchableOpacity
          key={tab.key}
          style={[
            styles.tab,
            activeTab === tab.key && { backgroundColor: primaryColor + '20', borderColor: primaryColor }
          ]}
          onPress={() => setActiveTab(tab.key as any)}
        >
          <Ionicons
            name={tab.icon as any}
            size={16}
            color={activeTab === tab.key ? primaryColor : textColor}
          />
          <ThemedText
            style={[
              styles.tabText,
              activeTab === tab.key && { color: primaryColor, fontWeight: '600' }
            ]}
          >
            {tab.label}
          </ThemedText>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderEmptyState = () => (
    <Card variant="outlined" style={styles.emptyState}>
      <ThemedText style={styles.emptyTitle}>
        {activeTab === 'all' && 'No upcoming events'}
        {activeTab === 'rsvp' && 'No RSVP events'}
        {activeTab === 'organized' && 'No organized events'}
        {activeTab === 'past' && 'No past events'}
      </ThemedText>
      <ThemedText style={styles.emptyDescription}>
        {activeTab === 'all' && 'Check back later for new events or create your own!'}
        {activeTab === 'rsvp' && 'RSVP to events to see them here.'}
        {activeTab === 'organized' && 'Events you organize will appear here.'}
        {activeTab === 'past' && 'Past events you attended will appear here.'}
      </ThemedText>
      {(activeTab === 'all' || activeTab === 'organized') && canCreateEvents && (
        <Button
          title="Create Event"
          onPress={handleCreateEvent}
          style={styles.emptyButton}
        />
      )}
      {activeTab === 'rsvp' && (
        <Button
          title="Browse Events"
          onPress={() => setActiveTab('all')}
          variant="outline"
          style={styles.emptyButton}
        />
      )}
    </Card>
  );

  return (
    <ThemedView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <ThemedText type="title">Events</ThemedText>
          <ThemedText style={styles.subtitle}>
            Discover and join PROTEC community events
          </ThemedText>
        </View>

        {canCreateEvents && (
          <TouchableOpacity
            style={styles.createButton}
            onPress={handleCreateEvent}
          >
            <ThemedText style={styles.createButtonText}>+ Create</ThemedText>
          </TouchableOpacity>
        )}
      </View>

      {/* Tab Bar */}
      {renderTabBar()}

      {/* Old Tabs - Remove this section */}
      <View style={styles.tabs} />
        <TouchableOpacity
          style={[styles.tab, activeTab === 'all' && styles.activeTab]}
          onPress={() => setActiveTab('all')}
        >
          <ThemedText style={[
            styles.tabText,
            activeTab === 'all' && styles.activeTabText
          ]}>
            All Events
          </ThemedText>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tab, activeTab === 'my' && styles.activeTab]}
          onPress={() => setActiveTab('my')}
        >
          <ThemedText style={[
            styles.tabText,
            activeTab === 'my' && styles.activeTabText
          ]}>
            My Events ({myEvents.length})
          </ThemedText>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tab, activeTab === 'past' && styles.activeTab]}
          onPress={() => setActiveTab('past')}
        >
          <ThemedText style={[
            styles.tabText,
            activeTab === 'past' && styles.activeTabText
          ]}>
            Past Events
          </ThemedText>
        </TouchableOpacity>
      </View>

      {/* Filters for All Events tab */}
      {activeTab === 'all' && (
        <View style={styles.filtersContainer}>
          <TouchableOpacity
            style={styles.filterButton}
            onPress={() => setShowFilters(true)}
          >
            <ThemedText style={styles.filterButtonText}>
              Filters {Object.keys(filters).length > 0 && `(${Object.keys(filters).length})`}
            </ThemedText>
          </TouchableOpacity>

          {Object.keys(filters).length > 0 && (
            <TouchableOpacity
              style={styles.clearFiltersButton}
              onPress={handleClearFilters}
            >
              <ThemedText style={styles.clearFiltersText}>Clear All</ThemedText>
            </TouchableOpacity>
          )}
        </View>
      )}

      {/* Events List */}
      {isCurrentlyLoading ? (
        <LoadingSpinner text="Loading events..." />
      ) : (
        <FlatList
          data={displayedEvents}
          renderItem={renderEventItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
          }
          ListEmptyComponent={renderEmptyState}
        />
      )}

      {/* Filters Modal */}
      <EventFiltersModal
        visible={showFilters}
        onClose={() => setShowFilters(false)}
        filters={filters}
        onFilterChange={handleFilterChange}
        onClearFilters={handleClearFilters}
      />
    </ThemedView>
  );
}

interface EventFiltersModalProps {
  visible: boolean;
  onClose: () => void;
  filters: any;
  onFilterChange: (key: string, value: any) => void;
  onClearFilters: () => void;
}

function EventFiltersModal({
  visible,
  onClose,
  filters,
  onFilterChange,
  onClearFilters,
}: EventFiltersModalProps) {
  const [localFilters, setLocalFilters] = useState(filters);

  useEffect(() => {
    setLocalFilters(filters);
  }, [filters]);

  const handleApplyFilters = () => {
    Object.entries(localFilters).forEach(([key, value]) => {
      onFilterChange(key, value);
    });
    onClose();
  };

  const handleLocalFilterChange = (key: string, value: any) => {
    setLocalFilters((prev: any) => ({ ...prev, [key]: value }));
  };

  const categories = [
    'Networking',
    'Workshop',
    'Conference',
    'Social',
    'Professional Development',
    'Alumni Gathering',
    'Fundraising',
    'Other',
  ];

  const locations = [
    'Johannesburg',
    'Cape Town',
    'Durban',
    'Pretoria',
    'Port Elizabeth',
    'Bloemfontein',
    'Online',
    'Other',
  ];

  return (
    <Modal
      visible={visible}
      onClose={onClose}
      title="Filter Events"
      size="lg"
    >
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Category */}
        <View style={styles.filterSection}>
          <ThemedText style={styles.filterLabel}>Category</ThemedText>
          <View style={styles.filterGrid}>
            {categories.map((category) => (
              <TouchableOpacity
                key={category}
                style={[
                  styles.filterOption,
                  localFilters.category === category && styles.filterOptionSelected,
                ]}
                onPress={() =>
                  handleLocalFilterChange(
                    'category',
                    localFilters.category === category ? undefined : category
                  )
                }
              >
                <ThemedText
                  style={[
                    styles.filterOptionText,
                    localFilters.category === category && styles.filterOptionTextSelected,
                  ]}
                >
                  {category}
                </ThemedText>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Location */}
        <View style={styles.filterSection}>
          <ThemedText style={styles.filterLabel}>Location</ThemedText>
          <View style={styles.filterGrid}>
            {locations.map((location) => (
              <TouchableOpacity
                key={location}
                style={[
                  styles.filterOption,
                  localFilters.location === location && styles.filterOptionSelected,
                ]}
                onPress={() =>
                  handleLocalFilterChange(
                    'location',
                    localFilters.location === location ? undefined : location
                  )
                }
              >
                <ThemedText
                  style={[
                    styles.filterOptionText,
                    localFilters.location === location && styles.filterOptionTextSelected,
                  ]}
                >
                  {location}
                </ThemedText>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Date Range */}
        <View style={styles.filterSection}>
          <ThemedText style={styles.filterLabel}>Date Range</ThemedText>
          <View style={styles.dateFilters}>
            <Input
              label="Start Date"
              value={localFilters.startDate || ''}
              onChangeText={(value) => handleLocalFilterChange('startDate', value)}
              placeholder="YYYY-MM-DD"
              containerStyle={styles.dateInput}
            />
            <Input
              label="End Date"
              value={localFilters.endDate || ''}
              onChangeText={(value) => handleLocalFilterChange('endDate', value)}
              placeholder="YYYY-MM-DD"
              containerStyle={styles.dateInput}
            />
          </View>
        </View>
      </ScrollView>

      {/* Modal Actions */}
      <View style={styles.modalActions}>
        <Button
          title="Clear All"
          onPress={() => {
            setLocalFilters({});
            onClearFilters();
          }}
          variant="outline"
          style={styles.modalButton}
        />
        <Button
          title="Apply Filters"
          onPress={handleApplyFilters}
          style={styles.modalButton}
        />
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    padding: 16,
    paddingBottom: 8,
  },
  headerContent: {
    flex: 1,
  },
  subtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  createButton: {
    backgroundColor: '#012A5B',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  createButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  tabs: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  tab: {
    flex: 1,
    paddingVertical: 16,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#012A5B',
  },
  tabText: {
    fontSize: 14,
    color: '#666',
  },
  activeTabText: {
    color: '#012A5B',
    fontWeight: '600',
  },
  filtersContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 8,
    gap: 12,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#012A5B',
  },
  filterButtonText: {
    color: '#012A5B',
    fontWeight: '500',
    fontSize: 14,
  },
  clearFiltersButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    backgroundColor: 'rgba(220, 38, 38, 0.1)',
  },
  clearFiltersText: {
    color: '#dc2626',
    fontWeight: '500',
    fontSize: 14,
  },
  listContent: {
    padding: 16,
    paddingTop: 8,
  },
  emptyState: {
    alignItems: 'center',
    padding: 32,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyDescription: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 16,
  },
  emptyButton: {
    marginTop: 8,
  },
  filterSection: {
    marginBottom: 24,
  },
  filterLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  filterGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  filterOption: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#ddd',
    marginBottom: 8,
  },
  filterOptionSelected: {
    backgroundColor: '#012A5B',
    borderColor: '#012A5B',
  },
  filterOptionText: {
    fontSize: 14,
    color: '#666',
  },
  filterOptionTextSelected: {
    color: '#fff',
    fontWeight: '500',
  },
  dateFilters: {
    flexDirection: 'row',
    gap: 12,
  },
  dateInput: {
    flex: 1,
  },
  modalActions: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 16,
  },
  modalButton: {
    flex: 1,
  },
  // New tab bar styles
  tabBar: {
    flexDirection: 'row',
    backgroundColor: DesignSystem.Colors.surface,
    paddingHorizontal: DesignSystem.Spacing.lg,
    paddingVertical: DesignSystem.Spacing.md,
    gap: DesignSystem.Spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: DesignSystem.Colors.border,
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: DesignSystem.Spacing.md,
    paddingVertical: DesignSystem.Spacing.sm,
    borderRadius: DesignSystem.BorderRadius.md,
    borderWidth: 1,
    borderColor: DesignSystem.Colors.border,
    backgroundColor: DesignSystem.Colors.background,
    gap: DesignSystem.Spacing.xs,
    flex: 1,
  },
  tabText: {
    fontSize: DesignSystem.Typography.caption.fontSize,
    textAlign: 'center',
  },
});
