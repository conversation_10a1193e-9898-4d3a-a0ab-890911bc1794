import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { <PERSON><PERSON>, Card, LoadingSpinner } from '@/components/ui';
import { PostCard } from '@/components/feed/PostCard';
import { useAuth } from '@/lib/providers/AuthProvider';
import { usePosts, useMyPosts, useTrendingPosts } from '@/lib/hooks/usePosts';
import { Post } from '@/lib/types';
import { DesignSystem } from '@/constants/DesignSystem';
import { Ionicons } from '@expo/vector-icons';
import { useThemeColor } from '@/hooks/useThemeColor';
import { router } from 'expo-router';
import React, { useState, useMemo } from 'react';
import {
    Alert,
    FlatList,
    RefreshControl,
    StyleSheet,
    TouchableOpacity,
    View
} from 'react-native';

export default function FeedScreen() {
  const [activeTab, setActiveTab] = useState<'all' | 'trending' | 'my'>('all');
  const [refreshing, setRefreshing] = useState(false);

  const { user, isAuthenticated } = useAuth();
  const primaryColor = useThemeColor({}, 'primary');
  const textColor = useThemeColor({}, 'text');

  // API hooks
  const { data: allPostsData, refetch: refetchAllPosts, isLoading: isLoadingAllPosts } = usePosts();
  const { data: myPostsData, refetch: refetchMyPosts, isLoading: isLoadingMyPosts } = useMyPosts();
  const { data: trendingPostsData, refetch: refetchTrendingPosts, isLoading: isLoadingTrendingPosts } = useTrendingPosts();

  // Determine displayed posts based on active tab
  const displayedPosts = useMemo(() => {
    switch (activeTab) {
      case 'trending':
        return trendingPostsData?.posts || [];
      case 'my':
        return myPostsData?.posts || [];
      default:
        return allPostsData?.posts || [];
    }
  }, [activeTab, allPostsData, myPostsData, trendingPostsData]);

  const isCurrentlyLoading =
    (activeTab === 'all' && isLoadingAllPosts) ||
    (activeTab === 'my' && isLoadingMyPosts) ||
    (activeTab === 'trending' && isLoadingTrendingPosts);

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      // Refetch based on active tab
      switch (activeTab) {
        case 'trending':
          await refetchTrendingPosts();
          break;
        case 'my':
          await refetchMyPosts();
          break;
        default:
          await refetchAllPosts();
          break;
      }
    } finally {
      setRefreshing(false);
    }
  };

  const handleCreatePost = () => {
    if (!isAuthenticated) {
      Alert.alert(
        'Login Required',
        'Please log in to create posts.',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Login', onPress: () => router.push('/auth/login') },
        ]
      );
      return;
    }

    router.push('/posts/create');
  };

  const renderTabBar = () => (
    <View style={styles.tabBar}>
      {[
        { key: 'all', label: 'All Posts', icon: 'newspaper' },
        { key: 'trending', label: 'Trending', icon: 'trending-up' },
        { key: 'my', label: 'My Posts', icon: 'person' },
      ].map(tab => (
        <TouchableOpacity
          key={tab.key}
          style={[
            styles.tab,
            activeTab === tab.key && { backgroundColor: primaryColor + '20', borderColor: primaryColor }
          ]}
          onPress={() => setActiveTab(tab.key as any)}
        >
          <Ionicons
            name={tab.icon as any}
            size={16}
            color={activeTab === tab.key ? primaryColor : textColor}
          />
          <ThemedText
            style={[
              styles.tabText,
              activeTab === tab.key && { color: primaryColor, fontWeight: '600' }
            ]}
          >
            {tab.label}
          </ThemedText>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderPostItem = ({ item }: { item: Post }) => (
    <PostCard post={item} />
  );

  const renderEmptyState = () => (
    <Card variant="outlined" style={styles.emptyState}>
      <ThemedText style={styles.emptyTitle}>
        {activeTab === 'all' && 'No posts yet'}
        {activeTab === 'trending' && 'No trending posts'}
        {activeTab === 'my' && 'You haven\'t posted anything yet'}
      </ThemedText>
      <ThemedText style={styles.emptyDescription}>
        {activeTab === 'all' && 'Be the first to share something with the community!'}
        {activeTab === 'trending' && 'Check back later for trending posts from the community.'}
        {activeTab === 'my' && 'Share your achievements and updates with fellow alumni.'}
      </ThemedText>
      {(activeTab === 'all' || activeTab === 'my') && (
        <Button
          title="Create Post"
          onPress={handleCreatePost}
          style={styles.emptyButton}
        />
      )}
      {activeTab === 'trending' && (
        <Button
          title="Browse All Posts"
          onPress={() => setActiveTab('all')}
          variant="outline"
          style={styles.emptyButton}
        />
      )}
    </Card>
  );

  return (
    <ThemedView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <ThemedText type="title">Feed</ThemedText>
          <ThemedText style={styles.subtitle}>
            Stay connected with the PROTEC community
          </ThemedText>
        </View>

        <TouchableOpacity
          style={styles.createButton}
          onPress={handleCreatePost}
        >
          <ThemedText style={styles.createButtonText}>+ Post</ThemedText>
        </TouchableOpacity>
      </View>

      {/* Tab Bar */}
      {renderTabBar()}

      {/* Posts List */}
      {isCurrentlyLoading ? (
        <LoadingSpinner text="Loading posts..." />
      ) : (
        <FlatList
          data={displayedPosts}
          renderItem={renderPostItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
          }
          ListEmptyComponent={renderEmptyState}
        />
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: DesignSystem.Colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    padding: DesignSystem.Spacing.lg,
    paddingBottom: DesignSystem.Spacing.sm,
    backgroundColor: DesignSystem.Colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: DesignSystem.Colors.border,
  },
  headerContent: {
    flex: 1,
  },
  subtitle: {
    fontSize: DesignSystem.Typography.caption.fontSize,
    color: DesignSystem.Colors.textSecondary,
    marginTop: DesignSystem.Spacing.xs,
  },
  createButton: {
    backgroundColor: DesignSystem.Colors.primary,
    paddingHorizontal: DesignSystem.Spacing.md,
    paddingVertical: DesignSystem.Spacing.sm,
    borderRadius: DesignSystem.BorderRadius.md,
  },
  createButtonText: {
    color: 'white',
    fontSize: DesignSystem.Typography.caption.fontSize,
    fontWeight: '600',
  },
  // Tab bar styles
  tabBar: {
    flexDirection: 'row',
    backgroundColor: DesignSystem.Colors.surface,
    paddingHorizontal: DesignSystem.Spacing.lg,
    paddingVertical: DesignSystem.Spacing.md,
    gap: DesignSystem.Spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: DesignSystem.Colors.border,
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: DesignSystem.Spacing.md,
    paddingVertical: DesignSystem.Spacing.sm,
    borderRadius: DesignSystem.BorderRadius.md,
    borderWidth: 1,
    borderColor: DesignSystem.Colors.border,
    backgroundColor: DesignSystem.Colors.background,
    gap: DesignSystem.Spacing.xs,
    flex: 1,
  },
  tabText: {
    fontSize: DesignSystem.Typography.caption.fontSize,
    textAlign: 'center',
  },
  listContent: {
    padding: DesignSystem.Spacing.lg,
    paddingTop: DesignSystem.Spacing.sm,
  },
  emptyState: {
    alignItems: 'center',
    padding: DesignSystem.Spacing.xl,
  },
  emptyTitle: {
    fontSize: DesignSystem.Typography.h3.fontSize,
    fontWeight: '600',
    marginBottom: DesignSystem.Spacing.sm,
    textAlign: 'center',
  },
  emptyDescription: {
    fontSize: DesignSystem.Typography.body.fontSize,
    color: DesignSystem.Colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: DesignSystem.Spacing.lg,
  },
  emptyButton: {
    marginTop: DesignSystem.Spacing.sm,
  },
});

