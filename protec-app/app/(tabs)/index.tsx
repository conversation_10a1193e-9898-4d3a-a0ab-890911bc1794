import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useAuthStore } from '@/lib/stores/authStore';
import React from 'react';
import { ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';

export default function DashboardScreen() {
  const { user, isAuthenticated, isLoading } = useAuthStore();

  if (isLoading) {
    return (
      <ThemedView style={styles.container}>
        <ThemedText>Loading...</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <ScrollView style={styles.content}>
        <ThemedView style={styles.header}>
          <ThemedText type="title">PROTEC Alumni</ThemedText>
          {isAuthenticated && user ? (
            <ThemedText type="subtitle">Welcome back, {user.name}!</ThemedText>
          ) : (
            <ThemedText type="subtitle">Connect. Grow. Give Back.</ThemedText>
          )}
        </ThemedView>

        {/* Quick Actions */}
        <ThemedView style={styles.section}>
          <ThemedText type="subtitle">Quick Actions</ThemedText>
          <View style={styles.actionGrid}>
            <TouchableOpacity style={styles.actionCard}>
              <ThemedText type="defaultSemiBold">Find Alumni</ThemedText>
              <ThemedText>Connect with fellow graduates</ThemedText>
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionCard}>
              <ThemedText type="defaultSemiBold">Upcoming Events</ThemedText>
              <ThemedText>See what's happening</ThemedText>
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionCard}>
              <ThemedText type="defaultSemiBold">Latest Posts</ThemedText>
              <ThemedText>Community updates</ThemedText>
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionCard}>
              <ThemedText type="defaultSemiBold">Donate</ThemedText>
              <ThemedText>Support future students</ThemedText>
            </TouchableOpacity>
          </View>
        </ThemedView>

        {/* Recent Activity */}
        <ThemedView style={styles.section}>
          <ThemedText type="subtitle">Recent Activity</ThemedText>
          <ThemedView style={styles.placeholder}>
            <ThemedText>No recent activity to show.</ThemedText>
            <ThemedText>Start connecting with alumni to see updates here!</ThemedText>
          </ThemedView>
        </ThemedView>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  header: {
    marginBottom: 24,
    alignItems: 'center',
  },
  section: {
    marginBottom: 24,
  },
  actionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginTop: 12,
  },
  actionCard: {
    flex: 1,
    minWidth: '45%',
    padding: 16,
    borderRadius: 8,
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  placeholder: {
    padding: 16,
    borderRadius: 8,
    backgroundColor: 'rgba(0,0,0,0.05)',
    marginTop: 12,
  },
});
