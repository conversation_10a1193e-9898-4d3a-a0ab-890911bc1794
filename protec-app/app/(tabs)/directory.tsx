import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { AlumniCard, Button, Card, Input, LoadingSpinner } from '@/components/ui';
import { useAlumni, useSearchAlumni, useAlumniRecommendations, useTrendingAlumni } from '@/lib/hooks/useAlumni';
import { useAlumniStore } from '@/lib/stores/alumniStore';
import { AdvancedSearchModal } from '@/components/alumni/AdvancedSearchModal';
import { Alumni } from '@/lib/types';
import { DesignSystem } from '@/constants/DesignSystem';
import { Ionicons } from '@expo/vector-icons';
import { useThemeColor } from '@/hooks/useThemeColor';
import React, { useEffect, useState, useMemo } from 'react';
import {
    FlatList,
    RefreshControl,
    ScrollView,
    StyleSheet,
    TouchableOpacity,
    View,
} from 'react-native';

export default function DirectoryScreen() {
  const [searchQuery, setSearchQuery] = useState('');
  const [showAdvancedSearch, setShowAdvancedSearch] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState<'all' | 'recommended' | 'trending'>('all');
  const [advancedFilters, setAdvancedFilters] = useState<any>({});

  const {
    alumni,
    filters,
    isLoading,
    setSearchQuery: setStoreSearchQuery,
    setFilters,
    clearFilters,
  } = useAlumniStore();

  const primaryColor = useThemeColor({}, 'primary');
  const textColor = useThemeColor({}, 'text');

  // Combine basic search with advanced filters
  const combinedFilters = useMemo(() => ({
    ...advancedFilters,
    query: searchQuery.length > 2 ? searchQuery : advancedFilters.query,
  }), [searchQuery, advancedFilters]);

  const { data: alumniData, refetch, isLoading: isLoadingAlumni } = useAlumni(combinedFilters);
  const { data: searchResults, isLoading: isSearching } = useSearchAlumni(searchQuery, combinedFilters);
  const { data: recommendations, isLoading: isLoadingRecommendations } = useAlumniRecommendations();
  const { data: trending, isLoading: isLoadingTrending } = useTrendingAlumni();

  // Determine which data to display based on active tab and search
  const displayedData = useMemo(() => {
    if (searchQuery.length > 2) {
      return searchResults?.alumni || [];
    }

    switch (activeTab) {
      case 'recommended':
        return recommendations?.alumni || [];
      case 'trending':
        return trending || [];
      default:
        return alumniData?.alumni || alumni;
    }
  }, [searchQuery, activeTab, searchResults, recommendations, trending, alumniData, alumni]);

  const isCurrentlyLoading = isLoadingAlumni || isSearching ||
    (activeTab === 'recommended' && isLoadingRecommendations) ||
    (activeTab === 'trending' && isLoadingTrending);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setStoreSearchQuery(searchQuery);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchQuery, setStoreSearchQuery]);

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await refetch();
    } finally {
      setRefreshing(false);
    }
  };

  const handleApplyAdvancedFilters = (filters: any) => {
    setAdvancedFilters(filters);
    setFilters(filters);
  };

  const handleClearFilters = () => {
    setAdvancedFilters({});
    clearFilters();
    setSearchQuery('');
  };

  const hasActiveFilters = Object.keys(advancedFilters).length > 0 || searchQuery.length > 0;

  const renderAlumniItem = ({ item }: { item: Alumni }) => (
    <AlumniCard alumni={item} />
  );

  const renderTabBar = () => (
    <View style={styles.tabBar}>
      {[
        { key: 'all', label: 'All Alumni', icon: 'people' },
        { key: 'recommended', label: 'Recommended', icon: 'star' },
        { key: 'trending', label: 'Trending', icon: 'trending-up' },
      ].map(tab => (
        <TouchableOpacity
          key={tab.key}
          style={[
            styles.tab,
            activeTab === tab.key && { backgroundColor: primaryColor + '20', borderColor: primaryColor }
          ]}
          onPress={() => setActiveTab(tab.key as any)}
        >
          <Ionicons
            name={tab.icon as any}
            size={16}
            color={activeTab === tab.key ? primaryColor : textColor}
          />
          <ThemedText
            style={[
              styles.tabText,
              activeTab === tab.key && { color: primaryColor, fontWeight: '600' }
            ]}
          >
            {tab.label}
          </ThemedText>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderEmptyState = () => (
    <Card variant="outlined" style={styles.emptyState}>
      <ThemedText style={styles.emptyTitle}>
        {searchQuery ? 'No alumni found' :
         activeTab === 'recommended' ? 'No recommendations available' :
         activeTab === 'trending' ? 'No trending alumni' : 'No alumni to display'}
      </ThemedText>
      <ThemedText style={styles.emptyDescription}>
        {searchQuery
          ? `No alumni match your search for "${searchQuery}"`
          : activeTab === 'recommended'
          ? 'Complete your profile to get personalized recommendations'
          : activeTab === 'trending'
          ? 'Check back later for trending alumni'
          : 'Try adjusting your filters or check back later'}
      </ThemedText>
      {hasActiveFilters && (
        <Button
          title="Clear Search & Filters"
          onPress={handleClearFilters}
          variant="outline"
          style={styles.emptyButton}
        />
      )}
    </Card>
  );

  return (
    <ThemedView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <ThemedText type="title">Alumni Directory</ThemedText>
        <ThemedText style={styles.subtitle}>
          Connect with {displayedData.length} PROTEC alumni worldwide
        </ThemedText>
      </View>

      {/* Tab Bar */}
      {renderTabBar()}

      {/* Search and Filters */}
      <View style={styles.searchContainer}>
        <View style={styles.searchRow}>
          <Input
            value={searchQuery}
          onChangeText={setSearchQuery}
          placeholder="Search alumni by name, company, or skills..."
          style={styles.searchInput}
          containerStyle={styles.searchInputContainer}
        />

        <TouchableOpacity
          style={[
            styles.filterButton,
            hasActiveFilters && { backgroundColor: primaryColor + '20', borderColor: primaryColor }
          ]}
          onPress={() => setShowAdvancedSearch(true)}
        >
          <Ionicons
            name="options"
            size={16}
            color={hasActiveFilters ? primaryColor : textColor}
          />
          <ThemedText
            style={[
              styles.filterButtonText,
              hasActiveFilters && { color: primaryColor, fontWeight: '600' }
            ]}
          >
            Advanced Search
          </ThemedText>
        </TouchableOpacity>

        {hasActiveFilters && (
          <TouchableOpacity
            style={styles.clearButton}
            onPress={handleClearFilters}
          >
            <Ionicons name="close-circle" size={16} color={DesignSystem.Colors.error} />
            <ThemedText style={styles.clearButtonText}>Clear</ThemedText>
          </TouchableOpacity>
        )}
      </View>

      {/* Active Filters */}
      {Object.keys(filters).length > 0 && (
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.activeFilters}
          contentContainerStyle={styles.activeFiltersContent}
        >
          {Object.entries(filters).map(([key, value]) => (
            <TouchableOpacity
              key={key}
              style={styles.activeFilter}
              onPress={() => handleFilterChange(key, undefined)}
            >
              <ThemedText style={styles.activeFilterText}>
                {key}: {Array.isArray(value) ? value.join(', ') : value} ×
              </ThemedText>
            </TouchableOpacity>
          ))}
          <TouchableOpacity
            style={styles.clearFiltersButton}
            onPress={handleClearFilters}
          >
            <ThemedText style={styles.clearFiltersText}>Clear All</ThemedText>
          </TouchableOpacity>
        </ScrollView>
      )}

      {/* Alumni List */}
      {isCurrentlyLoading ? (
        <LoadingSpinner text="Loading alumni..." />
      ) : (
        <FlatList
          data={displayedData || []}
          renderItem={renderAlumniItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
          }
          ListEmptyComponent={renderEmptyState}
        />
      )}

      {/* Advanced Search Modal */}
      <AdvancedSearchModal
        visible={showAdvancedSearch}
        onClose={() => setShowAdvancedSearch(false)}
        onApplyFilters={handleApplyAdvancedFilters}
        initialFilters={advancedFilters}
      />
    </ThemedView>
  );
}

interface FiltersModalProps {
  visible: boolean;
  onClose: () => void;
  filters: any;
  onFilterChange: (key: string, value: any) => void;
  onClearFilters: () => void;
}

function FiltersModal({
  visible,
  onClose,
  filters,
  onFilterChange,
  onClearFilters,
}: FiltersModalProps) {
  const [localFilters, setLocalFilters] = useState(filters);

  useEffect(() => {
    setLocalFilters(filters);
  }, [filters]);

  const handleApplyFilters = () => {
    Object.entries(localFilters).forEach(([key, value]) => {
      onFilterChange(key, value);
    });
    onClose();
  };

  const handleLocalFilterChange = (key: string, value: any) => {
    setLocalFilters((prev: any) => ({ ...prev, [key]: value }));
  };

  const graduationYears = Array.from({ length: 30 }, (_, i) => 2024 - i);
  const industries = [
    'Technology',
    'Finance',
    'Healthcare',
    'Education',
    'Engineering',
    'Consulting',
    'Government',
    'Non-profit',
    'Other',
  ];
  const provinces = [
    'Gauteng',
    'Western Cape',
    'KwaZulu-Natal',
    'Eastern Cape',
    'Free State',
    'Limpopo',
    'Mpumalanga',
    'North West',
    'Northern Cape',
  ];

  return (
    <Modal
      visible={visible}
      onClose={onClose}
      title="Filter Alumni"
      size="lg"
    >
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Graduation Year */}
        <View style={styles.filterSection}>
          <ThemedText style={styles.filterLabel}>Graduation Year</ThemedText>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.filterOptions}
          >
            {graduationYears.map((year) => (
              <TouchableOpacity
                key={year}
                style={[
                  styles.filterOption,
                  localFilters.graduationYear === year && styles.filterOptionSelected,
                ]}
                onPress={() =>
                  handleLocalFilterChange(
                    'graduationYear',
                    localFilters.graduationYear === year ? undefined : year
                  )
                }
              >
                <ThemedText
                  style={[
                    styles.filterOptionText,
                    localFilters.graduationYear === year && styles.filterOptionTextSelected,
                  ]}
                >
                  {year}
                </ThemedText>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Industry */}
        <View style={styles.filterSection}>
          <ThemedText style={styles.filterLabel}>Industry</ThemedText>
          <View style={styles.filterGrid}>
            {industries.map((industry) => (
              <TouchableOpacity
                key={industry}
                style={[
                  styles.filterOption,
                  localFilters.industry === industry && styles.filterOptionSelected,
                ]}
                onPress={() =>
                  handleLocalFilterChange(
                    'industry',
                    localFilters.industry === industry ? undefined : industry
                  )
                }
              >
                <ThemedText
                  style={[
                    styles.filterOptionText,
                    localFilters.industry === industry && styles.filterOptionTextSelected,
                  ]}
                >
                  {industry}
                </ThemedText>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Location */}
        <View style={styles.filterSection}>
          <ThemedText style={styles.filterLabel}>Province</ThemedText>
          <View style={styles.filterGrid}>
            {provinces.map((province) => (
              <TouchableOpacity
                key={province}
                style={[
                  styles.filterOption,
                  localFilters.location === province && styles.filterOptionSelected,
                ]}
                onPress={() =>
                  handleLocalFilterChange(
                    'location',
                    localFilters.location === province ? undefined : province
                  )
                }
              >
                <ThemedText
                  style={[
                    styles.filterOptionText,
                    localFilters.location === province && styles.filterOptionTextSelected,
                  ]}
                >
                  {province}
                </ThemedText>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </ScrollView>

      {/* Modal Actions */}
      <View style={styles.modalActions}>
        <Button
          title="Clear All"
          onPress={() => {
            setLocalFilters({});
            onClearFilters();
          }}
          variant="outline"
          style={styles.modalButton}
        />
        <Button
          title="Apply Filters"
          onPress={handleApplyFilters}
          style={styles.modalButton}
        />
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 16,
    paddingBottom: 8,
  },
  subtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  searchContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingBottom: 8,
    gap: 12,
  },
  searchInputContainer: {
    flex: 1,
    marginBottom: 0,
  },
  searchInput: {
    marginBottom: 0,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#012A5B',
    justifyContent: 'center',
    minWidth: 80,
  },
  filterButtonText: {
    color: '#012A5B',
    fontWeight: '500',
    textAlign: 'center',
  },
  activeFilters: {
    paddingHorizontal: 16,
    paddingBottom: 8,
  },
  activeFiltersContent: {
    gap: 8,
  },
  activeFilter: {
    backgroundColor: 'rgba(1, 42, 91, 0.1)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  activeFilterText: {
    fontSize: 12,
    color: '#012A5B',
    fontWeight: '500',
  },
  clearFiltersButton: {
    backgroundColor: 'rgba(220, 38, 38, 0.1)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  clearFiltersText: {
    fontSize: 12,
    color: '#dc2626',
    fontWeight: '500',
  },
  listContent: {
    padding: 16,
    paddingTop: 8,
  },
  emptyState: {
    alignItems: 'center',
    padding: 32,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyDescription: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 16,
  },
  emptyButton: {
    marginTop: 8,
  },
  filterSection: {
    marginBottom: 24,
  },
  filterLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  filterOptions: {
    flexDirection: 'row',
  },
  filterGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  filterOption: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#ddd',
    marginRight: 8,
    marginBottom: 8,
  },
  filterOptionSelected: {
    backgroundColor: '#012A5B',
    borderColor: '#012A5B',
  },
  filterOptionText: {
    fontSize: 14,
    color: '#666',
  },
  filterOptionTextSelected: {
    color: '#fff',
    fontWeight: '500',
  },
  modalActions: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 16,
  },
  modalButton: {
    flex: 1,
  },
  // New styles for enhanced directory
  tabBar: {
    flexDirection: 'row',
    backgroundColor: DesignSystem.Colors.surface,
    paddingHorizontal: DesignSystem.Spacing.lg,
    paddingVertical: DesignSystem.Spacing.md,
    gap: DesignSystem.Spacing.sm,
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: DesignSystem.Spacing.md,
    paddingVertical: DesignSystem.Spacing.sm,
    borderRadius: DesignSystem.BorderRadius.md,
    borderWidth: 1,
    borderColor: DesignSystem.Colors.border,
    backgroundColor: DesignSystem.Colors.background,
    gap: DesignSystem.Spacing.xs,
  },
  tabText: {
    fontSize: DesignSystem.Typography.caption.fontSize,
  },
  searchRow: {
    marginBottom: DesignSystem.Spacing.md,
  },
  filterRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: DesignSystem.Spacing.md,
  },
  clearButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: DesignSystem.Spacing.sm,
    paddingVertical: DesignSystem.Spacing.xs,
    gap: DesignSystem.Spacing.xs,
  },
  clearButtonText: {
    fontSize: DesignSystem.Typography.caption.fontSize,
    color: DesignSystem.Colors.error,
  },
});
