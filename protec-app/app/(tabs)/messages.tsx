import React, { useEffect } from 'react';
import { router } from 'expo-router';
import { ThemedView } from '@/components/ThemedView';
import { ThemedText } from '@/components/ThemedText';
import { StyleSheet, ActivityIndicator } from 'react-native';

export default function MessagesTab() {
  useEffect(() => {
    // Redirect to the main messages screen
    router.replace('/messages');
  }, []);

  return (
    <ThemedView style={styles.container}>
      <ActivityIndicator size="large" color="#012A5B" />
      <ThemedText style={styles.text}>Loading Messages...</ThemedText>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  text: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
});
