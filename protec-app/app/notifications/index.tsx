import React, { useEffect } from 'react';
import {
  StyleSheet,
  View,
  TouchableOpacity,
  FlatList,
  RefreshControl,
  Alert,
} from 'react-native';
import { router } from 'expo-router';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { But<PERSON>, Card } from '@/components/ui';
import { useNotificationsStore } from '@/lib/stores/notificationsStore';
import { NotificationData } from '@/lib/services/notificationService';
import { format } from 'date-fns';

interface NotificationItem extends NotificationData {
  read?: boolean;
  timestamp?: Date;
}

export default function NotificationsScreen() {
  const {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    clearNotifications,
    initializeNotifications,
    isInitialized,
  } = useNotificationsStore();

  useEffect(() => {
    if (!isInitialized) {
      initializeNotifications();
    }
  }, [isInitialized, initializeNotifications]);

  const handleNotificationPress = (notification: NotificationItem) => {
    // Mark as read
    if (!notification.read) {
      markAsRead(notification.id);
    }

    // Navigate based on notification type
    switch (notification.type) {
      case 'message':
        if (notification.data?.conversationId) {
          router.push(`/messages/thread/${notification.data.conversationId}`);
        } else {
          router.push('/messages');
        }
        break;
      case 'event':
        if (notification.data?.eventId) {
          router.push(`/events/${notification.data.eventId}`);
        } else {
          router.push('/(tabs)/events');
        }
        break;
      case 'connection':
        router.push('/connections');
        break;
      case 'donation':
        if (notification.data?.campaignId) {
          router.push(`/donations/campaigns/${notification.data.campaignId}`);
        } else {
          router.push('/(tabs)/donations');
        }
        break;
      case 'post':
        if (notification.data?.postId) {
          router.push(`/posts/${notification.data.postId}`);
        } else {
          router.push('/(tabs)/feed');
        }
        break;
      default:
        router.push('/(tabs)/');
        break;
    }
  };

  const handleMarkAllAsRead = () => {
    if (unreadCount === 0) return;
    
    Alert.alert(
      'Mark All as Read',
      `Mark all ${unreadCount} notifications as read?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Mark All', onPress: markAllAsRead },
      ]
    );
  };

  const handleClearAll = () => {
    if (notifications.length === 0) return;
    
    Alert.alert(
      'Clear All Notifications',
      'This will permanently delete all notifications. Are you sure?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear All',
          style: 'destructive',
          onPress: clearNotifications,
        },
      ]
    );
  };

  const getNotificationIcon = (type: NotificationData['type']) => {
    switch (type) {
      case 'message': return '💬';
      case 'event': return '📅';
      case 'connection': return '🤝';
      case 'donation': return '💝';
      case 'post': return '📰';
      case 'general': return '📢';
      default: return '🔔';
    }
  };

  const formatNotificationTime = (timestamp?: Date) => {
    if (!timestamp) return '';
    
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - timestamp.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`;
    return format(timestamp, 'MMM dd, yyyy');
  };

  const renderNotificationItem = ({ item }: { item: NotificationItem }) => (
    <TouchableOpacity
      style={styles.notificationItem}
      onPress={() => handleNotificationPress(item)}
    >
      <Card variant="outlined" style={[
        styles.notificationCard,
        !item.read && styles.unreadCard
      ]}>
        <View style={styles.notificationHeader}>
          <View style={styles.notificationIcon}>
            <ThemedText style={styles.iconText}>
              {getNotificationIcon(item.type)}
            </ThemedText>
          </View>
          
          <View style={styles.notificationContent}>
            <View style={styles.notificationTitleRow}>
              <ThemedText 
                type={!item.read ? 'defaultSemiBold' : 'default'}
                style={styles.notificationTitle}
              >
                {item.title}
              </ThemedText>
              {!item.read && <View style={styles.unreadDot} />}
            </View>
            
            <ThemedText 
              style={[
                styles.notificationBody,
                !item.read && styles.unreadBody
              ]}
              numberOfLines={2}
            >
              {item.body}
            </ThemedText>
            
            <ThemedText style={styles.notificationTime}>
              {formatNotificationTime(item.timestamp)}
            </ThemedText>
          </View>
        </View>
      </Card>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <Card variant="outlined" style={styles.emptyState}>
      <ThemedText style={styles.emptyIcon}>🔔</ThemedText>
      <ThemedText style={styles.emptyTitle}>No notifications yet</ThemedText>
      <ThemedText style={styles.emptyDescription}>
        You'll see notifications for messages, events, and other important updates here.
      </ThemedText>
      <Button
        title="Notification Settings"
        onPress={() => router.push('/settings/notifications')}
        variant="outline"
        style={styles.emptyButton}
      />
    </Card>
  );

  return (
    <ThemedView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <ThemedText style={styles.backButton}>‹ Back</ThemedText>
        </TouchableOpacity>
        
        <View style={styles.headerCenter}>
          <ThemedText type="subtitle">Notifications</ThemedText>
          {unreadCount > 0 && (
            <View style={styles.unreadBadge}>
              <ThemedText style={styles.unreadBadgeText}>
                {unreadCount}
              </ThemedText>
            </View>
          )}
        </View>
        
        <TouchableOpacity onPress={() => router.push('/settings/notifications')}>
          <ThemedText style={styles.settingsButton}>⚙️</ThemedText>
        </TouchableOpacity>
      </View>

      {/* Action Buttons */}
      {notifications.length > 0 && (
        <View style={styles.actionBar}>
          {unreadCount > 0 && (
            <Button
              title={`Mark All Read (${unreadCount})`}
              onPress={handleMarkAllAsRead}
              variant="outline"
              size="sm"
              style={styles.actionButton}
            />
          )}
          
          <Button
            title="Clear All"
            onPress={handleClearAll}
            variant="ghost"
            size="sm"
            style={styles.actionButton}
          />
        </View>
      )}

      {/* Notifications List */}
      <FlatList
        data={notifications}
        renderItem={renderNotificationItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl 
            refreshing={false} 
            onRefresh={() => {
              // Refresh notifications from server
            }} 
          />
        }
        ListEmptyComponent={renderEmptyState}
      />
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  backButton: {
    color: '#012A5B',
    fontSize: 18,
    fontWeight: '600',
  },
  headerCenter: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  unreadBadge: {
    backgroundColor: '#e74c3c',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 6,
  },
  unreadBadgeText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  settingsButton: {
    fontSize: 18,
  },
  actionBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.05)',
  },
  actionButton: {
    flex: 1,
    marginHorizontal: 4,
  },
  listContent: {
    padding: 16,
  },
  notificationItem: {
    marginBottom: 8,
  },
  notificationCard: {
    marginBottom: 0,
  },
  unreadCard: {
    borderLeftWidth: 4,
    borderLeftColor: '#012A5B',
    backgroundColor: 'rgba(1, 42, 91, 0.02)',
  },
  notificationHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  notificationIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(1, 42, 91, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  iconText: {
    fontSize: 18,
  },
  notificationContent: {
    flex: 1,
  },
  notificationTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  notificationTitle: {
    fontSize: 16,
    flex: 1,
  },
  unreadDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#e74c3c',
    marginLeft: 8,
  },
  notificationBody: {
    fontSize: 14,
    color: '#666',
    lineHeight: 18,
    marginBottom: 8,
  },
  unreadBody: {
    color: '#333',
  },
  notificationTime: {
    fontSize: 12,
    color: '#888',
  },
  emptyState: {
    alignItems: 'center',
    padding: 48,
  },
  emptyIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyDescription: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
  },
  emptyButton: {
    marginTop: 8,
  },
});
