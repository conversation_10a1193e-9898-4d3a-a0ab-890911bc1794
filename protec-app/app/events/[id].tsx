import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { <PERSON><PERSON>, Card, LoadingSpinner, Modal } from '@/components/ui';
import { useEvent, useRSVPEvent } from '@/lib/hooks/useEvents';
import { useAuthStore } from '@/lib/stores/authStore';
import { format } from 'date-fns';
import { router, useLocalSearchParams } from 'expo-router';
import React, { useState } from 'react';
import {
    Alert,
    Image,
    Linking,
    ScrollView,
    Share,
    StyleSheet,
    TouchableOpacity,
    View,
} from 'react-native';

export default function EventDetailsScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { user, isAuthenticated } = useAuthStore();
  const { data: event, isLoading, error } = useEvent(id || '');
  const rsvpMutation = useRSVPEvent();
  
  const [showRSVPModal, setShowRSVPModal] = useState(false);
  const [rsvpStatus, setRSVPStatus] = useState<'ATTENDING' | 'NOT_ATTENDING' | null>(null);

  const handleRSVP = (status: 'ATTENDING' | 'NOT_ATTENDING') => {
    if (!isAuthenticated) {
      Alert.alert(
        'Login Required',
        'Please log in to RSVP to events.',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Login', onPress: () => router.push('/auth/login') },
        ]
      );
      return;
    }

    Alert.alert(
      'Confirm RSVP',
      `Are you sure you want to ${status === 'ATTENDING' ? 'attend' : 'decline'} this event?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Confirm',
          onPress: async () => {
            try {
              await rsvpMutation.mutateAsync({ eventId: id!, status });
              setRSVPStatus(status);
              setShowRSVPModal(false);
              Alert.alert(
                'RSVP Confirmed',
                status === 'ATTENDING' 
                  ? 'You have successfully RSVP\'d to this event!'
                  : 'You have declined this event.'
              );
            } catch (error) {
              Alert.alert('Error', 'Failed to update RSVP. Please try again.');
            }
          },
        },
      ]
    );
  };

  const handleShare = async () => {
    if (!event) return;
    
    try {
      await Share.share({
        message: `Check out this event: ${event.title}\n\n${event.description}\n\nDate: ${format(event.startTime, 'PPP')}\nTime: ${format(event.startTime, 'p')}`,
        title: event.title,
      });
    } catch (error) {
      console.error('Error sharing event:', error);
    }
  };

  const handleGetDirections = () => {
    if (!event?.location?.address) {
      Alert.alert('No Address', 'No address available for this event.');
      return;
    }

    const address = encodeURIComponent(event.location.address);
    const url = `https://maps.google.com/?q=${address}`;
    Linking.openURL(url);
  };

  const handleContactOrganizer = () => {
    if (!event?.organizer?.email) {
      Alert.alert('Contact Unavailable', 'Organizer contact information is not available.');
      return;
    }

    const subject = encodeURIComponent(`Question about ${event.title}`);
    const body = encodeURIComponent(`Hi ${event.organizer.name},\n\nI have a question about the event "${event.title}".\n\n`);
    const url = `mailto:${event.organizer.email}?subject=${subject}&body=${body}`;
    Linking.openURL(url);
  };

  if (isLoading) {
    return (
      <ThemedView style={styles.container}>
        <LoadingSpinner text="Loading event details..." />
      </ThemedView>
    );
  }

  if (error || !event) {
    return (
      <ThemedView style={styles.container}>
        <View style={styles.errorState}>
          <ThemedText type="subtitle">Event not found</ThemedText>
          <ThemedText style={styles.errorText}>
            This event could not be loaded.
          </ThemedText>
          <Button
            title="Go Back"
            onPress={() => router.back()}
            style={styles.errorButton}
          />
        </View>
      </ThemedView>
    );
  }

  const isEventPast = new Date(event.endTime) < new Date();
  const canRSVP = !isEventPast && isAuthenticated;

  return (
    <ThemedView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <ThemedText style={styles.backButton}>‹ Back</ThemedText>
        </TouchableOpacity>
        <ThemedText type="subtitle">Event Details</ThemedText>
        <TouchableOpacity onPress={handleShare}>
          <ThemedText style={styles.shareButton}>Share</ThemedText>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Event Image */}
        {event.imageUrl && (
          <Image source={{ uri: event.imageUrl }} style={styles.eventImage} />
        )}

        {/* Event Header */}
        <Card variant="elevated" style={styles.eventCard}>
          <View style={styles.eventHeader}>
            <View style={styles.categoryContainer}>
              <ThemedText style={styles.categoryText}>{event.category}</ThemedText>
            </View>
            
            {isEventPast && (
              <View style={styles.pastEventBadge}>
                <ThemedText style={styles.pastEventText}>Past Event</ThemedText>
              </View>
            )}
          </View>

          <ThemedText type="title" style={styles.eventTitle}>
            {event.title}
          </ThemedText>

          <View style={styles.eventMeta}>
            <View style={styles.metaRow}>
              <ThemedText style={styles.metaIcon}>📅</ThemedText>
              <ThemedText style={styles.metaText}>
                {format(event.startTime, 'EEEE, MMMM do, yyyy')}
              </ThemedText>
            </View>
            
            <View style={styles.metaRow}>
              <ThemedText style={styles.metaIcon}>🕐</ThemedText>
              <ThemedText style={styles.metaText}>
                {format(event.startTime, 'h:mm a')} - {format(event.endTime, 'h:mm a')}
              </ThemedText>
            </View>
            
            {event.location && (
              <TouchableOpacity style={styles.metaRow} onPress={handleGetDirections}>
                <ThemedText style={styles.metaIcon}>📍</ThemedText>
                <ThemedText style={[styles.metaText, styles.locationLink]}>
                  {event.location.address || `${event.location.city}, ${event.location.province}`}
                </ThemedText>
              </TouchableOpacity>
            )}
          </View>

          {/* RSVP Button */}
          {canRSVP && (
            <Button
              title={rsvpStatus === 'ATTENDING' ? 'You\'re Attending' : 'RSVP'}
              onPress={() => setShowRSVPModal(true)}
              variant={rsvpStatus === 'ATTENDING' ? 'secondary' : 'primary'}
              style={styles.rsvpButton}
            />
          )}
        </Card>

        {/* Event Description */}
        <Card variant="elevated" style={styles.section}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>
            About This Event
          </ThemedText>
          <ThemedText style={styles.description}>
            {event.description}
          </ThemedText>
        </Card>

        {/* Organizer */}
        <Card variant="elevated" style={styles.section}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>
            Organizer
          </ThemedText>
          
          <View style={styles.organizerInfo}>
            <View style={styles.organizerDetails}>
              <ThemedText type="defaultSemiBold" style={styles.organizerName}>
                {event.organizer.name}
              </ThemedText>
              {event.organizer.currentRole && (
                <ThemedText style={styles.organizerRole}>
                  {event.organizer.currentRole}
                </ThemedText>
              )}
              {event.organizer.graduationYear && (
                <ThemedText style={styles.organizerYear}>
                  Class of {event.organizer.graduationYear}
                </ThemedText>
              )}
            </View>
            
            <Button
              title="Contact"
              onPress={handleContactOrganizer}
              variant="outline"
              size="sm"
            />
          </View>
        </Card>

        {/* Event Tags */}
        {event.tags && event.tags.length > 0 && (
          <Card variant="elevated" style={styles.section}>
            <ThemedText type="subtitle" style={styles.sectionTitle}>
              Tags
            </ThemedText>
            
            <View style={styles.tagsContainer}>
              {event.tags.map((tag, index) => (
                <View key={index} style={styles.tag}>
                  <ThemedText style={styles.tagText}>#{tag}</ThemedText>
                </View>
              ))}
            </View>
          </Card>
        )}

        {/* Event Stats */}
        <Card variant="elevated" style={styles.section}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>
            Event Information
          </ThemedText>
          
          <View style={styles.statsContainer}>
            {event.maxAttendees && (
              <View style={styles.statItem}>
                <ThemedText style={styles.statLabel}>Max Attendees</ThemedText>
                <ThemedText style={styles.statValue}>{event.maxAttendees}</ThemedText>
              </View>
            )}
            
            <View style={styles.statItem}>
              <ThemedText style={styles.statLabel}>Created</ThemedText>
              <ThemedText style={styles.statValue}>
                {format(event.createdAt, 'MMM dd, yyyy')}
              </ThemedText>
            </View>
          </View>
        </Card>
      </ScrollView>

      {/* RSVP Modal */}
      <Modal
        visible={showRSVPModal}
        onClose={() => setShowRSVPModal(false)}
        title="RSVP to Event"
        size="sm"
      >
        <View style={styles.rsvpModalContent}>
          <ThemedText style={styles.rsvpModalText}>
            Will you be attending "{event.title}"?
          </ThemedText>
          
          <View style={styles.rsvpModalActions}>
            <Button
              title="Yes, I'll Attend"
              onPress={() => handleRSVP('ATTENDING')}
              style={styles.rsvpModalButton}
            />
            <Button
              title="No, I Can't Attend"
              onPress={() => handleRSVP('NOT_ATTENDING')}
              variant="outline"
              style={styles.rsvpModalButton}
            />
          </View>
        </View>
      </Modal>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  backButton: {
    color: '#012A5B',
    fontSize: 18,
    fontWeight: '600',
  },
  shareButton: {
    color: '#012A5B',
    fontSize: 16,
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  errorState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  errorText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginTop: 8,
    marginBottom: 16,
  },
  errorButton: {
    marginTop: 8,
  },
  eventImage: {
    width: '100%',
    height: 200,
    resizeMode: 'cover',
  },
  eventCard: {
    margin: 16,
    marginBottom: 8,
  },
  eventHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  categoryContainer: {
    backgroundColor: 'rgba(1, 42, 91, 0.1)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  categoryText: {
    fontSize: 12,
    color: '#012A5B',
    fontWeight: '500',
  },
  pastEventBadge: {
    backgroundColor: 'rgba(220, 38, 38, 0.1)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  pastEventText: {
    fontSize: 12,
    color: '#dc2626',
    fontWeight: '500',
  },
  eventTitle: {
    fontSize: 24,
    marginBottom: 16,
  },
  eventMeta: {
    gap: 8,
    marginBottom: 16,
  },
  metaRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  metaIcon: {
    fontSize: 16,
    marginRight: 8,
    width: 20,
  },
  metaText: {
    fontSize: 14,
    color: '#666',
    flex: 1,
  },
  locationLink: {
    color: '#012A5B',
    textDecorationLine: 'underline',
  },
  rsvpButton: {
    marginTop: 8,
  },
  section: {
    marginHorizontal: 16,
    marginBottom: 8,
  },
  sectionTitle: {
    marginBottom: 12,
  },
  description: {
    fontSize: 14,
    lineHeight: 20,
    color: '#666',
  },
  organizerInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  organizerDetails: {
    flex: 1,
  },
  organizerName: {
    fontSize: 16,
    marginBottom: 4,
  },
  organizerRole: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  organizerYear: {
    fontSize: 12,
    color: '#888',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  tag: {
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  tagText: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statLabel: {
    fontSize: 12,
    color: '#888',
    marginBottom: 4,
  },
  statValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#012A5B',
  },
  rsvpModalContent: {
    alignItems: 'center',
  },
  rsvpModalText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 22,
  },
  rsvpModalActions: {
    width: '100%',
    gap: 12,
  },
  rsvpModalButton: {
    width: '100%',
  },
});
