import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Card, Input } from '@/components/ui';
import { useCreateEvent } from '@/lib/hooks/useEvents';
import { useAuthStore } from '@/lib/stores/authStore';
import { router } from 'expo-router';
import React, { useState } from 'react';
import {
    Alert,
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    StyleSheet,
    TouchableOpacity,
    View,
} from 'react-native';

interface EventFormData {
  title: string;
  description: string;
  category: string;
  startDate: string;
  startTime: string;
  endDate: string;
  endTime: string;
  address: string;
  city: string;
  province: string;
  maxAttendees: string;
  tags: string;
  isPublic: boolean;
}

export default function CreateEventScreen() {
  const { user } = useAuthStore();
  const createEvent = useCreateEvent();
  
  const [formData, setFormData] = useState<EventFormData>({
    title: '',
    description: '',
    category: '',
    startDate: '',
    startTime: '',
    endDate: '',
    endTime: '',
    address: '',
    city: '',
    province: '',
    maxAttendees: '',
    tags: '',
    isPublic: true,
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = (field: keyof EventFormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const validateForm = (): boolean => {
    if (!formData.title.trim()) {
      Alert.alert('Error', 'Event title is required');
      return false;
    }
    
    if (!formData.description.trim()) {
      Alert.alert('Error', 'Event description is required');
      return false;
    }
    
    if (!formData.category.trim()) {
      Alert.alert('Error', 'Event category is required');
      return false;
    }
    
    if (!formData.startDate || !formData.startTime) {
      Alert.alert('Error', 'Start date and time are required');
      return false;
    }
    
    if (!formData.endDate || !formData.endTime) {
      Alert.alert('Error', 'End date and time are required');
      return false;
    }

    // Validate date/time logic
    const startDateTime = new Date(`${formData.startDate}T${formData.startTime}`);
    const endDateTime = new Date(`${formData.endDate}T${formData.endTime}`);
    
    if (startDateTime >= endDateTime) {
      Alert.alert('Error', 'End date/time must be after start date/time');
      return false;
    }
    
    if (startDateTime <= new Date()) {
      Alert.alert('Error', 'Event must be scheduled for a future date');
      return false;
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      const eventData = {
        title: formData.title.trim(),
        description: formData.description.trim(),
        category: formData.category.trim(),
        startTime: new Date(`${formData.startDate}T${formData.startTime}`),
        endTime: new Date(`${formData.endDate}T${formData.endTime}`),
        location: {
          address: formData.address.trim(),
          city: formData.city.trim(),
          province: formData.province.trim(),
        },
        organizerId: user?.id,
        isPublic: formData.isPublic,
        maxAttendees: formData.maxAttendees ? parseInt(formData.maxAttendees) : undefined,
        tags: formData.tags
          .split(',')
          .map(tag => tag.trim())
          .filter(tag => tag.length > 0),
      };

      await createEvent.mutateAsync(eventData);
      
      Alert.alert(
        'Success',
        'Event created successfully!',
        [{ text: 'OK', onPress: () => router.back() }]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to create event. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    Alert.alert(
      'Cancel Event Creation',
      'Are you sure you want to cancel? All changes will be lost.',
      [
        { text: 'Continue Editing', style: 'cancel' },
        { text: 'Cancel', style: 'destructive', onPress: () => router.back() },
      ]
    );
  };

  const categories = [
    'Networking',
    'Workshop',
    'Conference',
    'Social',
    'Professional Development',
    'Alumni Gathering',
    'Fundraising',
    'Other',
  ];

  const provinces = [
    'Gauteng',
    'Western Cape',
    'KwaZulu-Natal',
    'Eastern Cape',
    'Free State',
    'Limpopo',
    'Mpumalanga',
    'North West',
    'Northern Cape',
  ];

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ThemedView style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={handleCancel}>
            <ThemedText style={styles.cancelButton}>Cancel</ThemedText>
          </TouchableOpacity>
          <ThemedText type="subtitle">Create Event</ThemedText>
          <TouchableOpacity onPress={handleSubmit} disabled={isSubmitting}>
            <ThemedText style={[styles.saveButton, isSubmitting && styles.disabledButton]}>
              {isSubmitting ? 'Creating...' : 'Create'}
            </ThemedText>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Basic Information */}
          <Card variant="outlined" style={styles.section}>
            <ThemedText type="subtitle" style={styles.sectionTitle}>
              Basic Information
            </ThemedText>
            
            <Input
              label="Event Title *"
              value={formData.title}
              onChangeText={(value) => handleInputChange('title', value)}
              placeholder="Enter event title"
            />
            
            <Input
              label="Description *"
              value={formData.description}
              onChangeText={(value) => handleInputChange('description', value)}
              placeholder="Describe your event..."
              multiline
              numberOfLines={4}
              style={styles.textArea}
            />

            {/* Category Selector */}
            <View style={styles.categoryContainer}>
              <ThemedText style={styles.inputLabel}>Category *</ThemedText>
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                style={styles.categoryScroll}
              >
                {categories.map((category) => (
                  <TouchableOpacity
                    key={category}
                    style={[
                      styles.categoryOption,
                      formData.category === category && styles.categoryOptionSelected,
                    ]}
                    onPress={() => handleInputChange('category', category)}
                  >
                    <ThemedText
                      style={[
                        styles.categoryOptionText,
                        formData.category === category && styles.categoryOptionTextSelected,
                      ]}
                    >
                      {category}
                    </ThemedText>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
          </Card>

          {/* Date & Time */}
          <Card variant="outlined" style={styles.section}>
            <ThemedText type="subtitle" style={styles.sectionTitle}>
              Date & Time
            </ThemedText>
            
            <View style={styles.dateTimeRow}>
              <Input
                label="Start Date *"
                value={formData.startDate}
                onChangeText={(value) => handleInputChange('startDate', value)}
                placeholder="YYYY-MM-DD"
                containerStyle={styles.dateTimeInput}
              />
              <Input
                label="Start Time *"
                value={formData.startTime}
                onChangeText={(value) => handleInputChange('startTime', value)}
                placeholder="HH:MM"
                containerStyle={styles.dateTimeInput}
              />
            </View>
            
            <View style={styles.dateTimeRow}>
              <Input
                label="End Date *"
                value={formData.endDate}
                onChangeText={(value) => handleInputChange('endDate', value)}
                placeholder="YYYY-MM-DD"
                containerStyle={styles.dateTimeInput}
              />
              <Input
                label="End Time *"
                value={formData.endTime}
                onChangeText={(value) => handleInputChange('endTime', value)}
                placeholder="HH:MM"
                containerStyle={styles.dateTimeInput}
              />
            </View>
          </Card>

          {/* Location */}
          <Card variant="outlined" style={styles.section}>
            <ThemedText type="subtitle" style={styles.sectionTitle}>
              Location
            </ThemedText>
            
            <Input
              label="Address"
              value={formData.address}
              onChangeText={(value) => handleInputChange('address', value)}
              placeholder="Street address or venue name"
            />
            
            <View style={styles.locationRow}>
              <Input
                label="City"
                value={formData.city}
                onChangeText={(value) => handleInputChange('city', value)}
                placeholder="City"
                containerStyle={styles.locationInput}
              />
              
              {/* Province Selector */}
              <View style={styles.locationInput}>
                <ThemedText style={styles.inputLabel}>Province</ThemedText>
                <ScrollView
                  horizontal
                  showsHorizontalScrollIndicator={false}
                  style={styles.provinceScroll}
                >
                  {provinces.map((province) => (
                    <TouchableOpacity
                      key={province}
                      style={[
                        styles.provinceOption,
                        formData.province === province && styles.provinceOptionSelected,
                      ]}
                      onPress={() => handleInputChange('province', province)}
                    >
                      <ThemedText
                        style={[
                          styles.provinceOptionText,
                          formData.province === province && styles.provinceOptionTextSelected,
                        ]}
                      >
                        {province}
                      </ThemedText>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              </View>
            </View>
          </Card>

          {/* Additional Settings */}
          <Card variant="outlined" style={styles.section}>
            <ThemedText type="subtitle" style={styles.sectionTitle}>
              Additional Settings
            </ThemedText>
            
            <Input
              label="Max Attendees"
              value={formData.maxAttendees}
              onChangeText={(value) => handleInputChange('maxAttendees', value)}
              placeholder="Leave empty for unlimited"
              keyboardType="numeric"
            />
            
            <Input
              label="Tags"
              value={formData.tags}
              onChangeText={(value) => handleInputChange('tags', value)}
              placeholder="networking, professional, workshop"
              helperText="Separate multiple tags with commas"
            />

            {/* Public/Private Toggle */}
            <View style={styles.toggleContainer}>
              <View style={styles.toggleInfo}>
                <ThemedText style={styles.toggleLabel}>Public Event</ThemedText>
                <ThemedText style={styles.toggleDescription}>
                  Public events are visible to all alumni
                </ThemedText>
              </View>
              <TouchableOpacity
                style={[styles.toggle, formData.isPublic && styles.toggleActive]}
                onPress={() => handleInputChange('isPublic', !formData.isPublic)}
              >
                <View style={[styles.toggleThumb, formData.isPublic && styles.toggleThumbActive]} />
              </TouchableOpacity>
            </View>
          </Card>

          <View style={styles.bottomPadding} />
        </ScrollView>
      </ThemedView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  cancelButton: {
    color: '#666',
    fontSize: 16,
  },
  saveButton: {
    color: '#012A5B',
    fontSize: 16,
    fontWeight: '600',
  },
  disabledButton: {
    opacity: 0.5,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 16,
  },
  sectionTitle: {
    marginBottom: 16,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginBottom: 8,
  },
  categoryContainer: {
    marginBottom: 16,
  },
  categoryScroll: {
    flexDirection: 'row',
  },
  categoryOption: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#ddd',
    marginRight: 8,
    backgroundColor: '#fff',
  },
  categoryOptionSelected: {
    backgroundColor: '#012A5B',
    borderColor: '#012A5B',
  },
  categoryOptionText: {
    fontSize: 14,
    color: '#666',
  },
  categoryOptionTextSelected: {
    color: '#fff',
    fontWeight: '500',
  },
  dateTimeRow: {
    flexDirection: 'row',
    gap: 12,
  },
  dateTimeInput: {
    flex: 1,
  },
  locationRow: {
    flexDirection: 'row',
    gap: 12,
  },
  locationInput: {
    flex: 1,
  },
  provinceScroll: {
    flexDirection: 'row',
    maxHeight: 40,
  },
  provinceOption: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#ddd',
    marginRight: 6,
    backgroundColor: '#fff',
  },
  provinceOptionSelected: {
    backgroundColor: '#012A5B',
    borderColor: '#012A5B',
  },
  provinceOptionText: {
    fontSize: 12,
    color: '#666',
  },
  provinceOptionTextSelected: {
    color: '#fff',
    fontWeight: '500',
  },
  toggleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
  },
  toggleInfo: {
    flex: 1,
  },
  toggleLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  toggleDescription: {
    fontSize: 14,
    color: '#666',
  },
  toggle: {
    width: 50,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#ddd',
    justifyContent: 'center',
    paddingHorizontal: 2,
  },
  toggleActive: {
    backgroundColor: '#012A5B',
  },
  toggleThumb: {
    width: 26,
    height: 26,
    borderRadius: 13,
    backgroundColor: '#fff',
    alignSelf: 'flex-start',
  },
  toggleThumbActive: {
    alignSelf: 'flex-end',
  },
  bottomPadding: {
    height: 32,
  },
});
