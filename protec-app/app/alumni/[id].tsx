import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { <PERSON><PERSON>, Card, LoadingSpinner, Modal } from '@/components/ui';
import { useAlumniProfile } from '@/lib/hooks/useAlumni';
import { useAuthStore } from '@/lib/stores/authStore';
import { router, useLocalSearchParams } from 'expo-router';
import React, { useState } from 'react';
import {
    Alert,
    Image,
    Linking,
    ScrollView,
    StyleSheet,
    TouchableOpacity,
    View,
} from 'react-native';

export default function AlumniProfileScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { user } = useAuthStore();
  const { data: profile, isLoading, error } = useAlumniProfile(id || '');
  
  const [showContactModal, setShowContactModal] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionRequested, setConnectionRequested] = useState(false);

  const handleConnect = () => {
    if (isConnected) {
      Alert.alert('Already Connected', 'You are already connected with this alumni.');
      return;
    }

    if (connectionRequested) {
      Alert.alert('Request Pending', 'Your connection request is pending approval.');
      return;
    }

    Alert.alert(
      'Send Connection Request',
      `Send a connection request to ${profile?.name}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Send Request',
          onPress: () => {
            // TODO: Implement connection request API
            setConnectionRequested(true);
            Alert.alert('Request Sent', 'Your connection request has been sent.');
          },
        },
      ]
    );
  };

  const handleMessage = () => {
    if (!isConnected) {
      Alert.alert(
        'Connect First',
        'You need to connect with this alumni before sending a message.',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Send Connection Request', onPress: handleConnect },
        ]
      );
      return;
    }

    // TODO: Navigate to message thread
    router.push(`/messages/thread/${id}`);
  };

  const handleContact = () => {
    setShowContactModal(true);
  };

  const handleEmailContact = () => {
    if (profile?.email) {
      Linking.openURL(`mailto:${profile.email}`);
    }
    setShowContactModal(false);
  };

  const handlePhoneContact = () => {
    // TODO: Implement phone contact if available
    Alert.alert('Phone Contact', 'Phone contact feature coming soon.');
    setShowContactModal(false);
  };

  if (isLoading) {
    return (
      <ThemedView style={styles.container}>
        <LoadingSpinner text="Loading profile..." />
      </ThemedView>
    );
  }

  if (error || !profile) {
    return (
      <ThemedView style={styles.container}>
        <View style={styles.errorState}>
          <ThemedText type="subtitle">Profile not found</ThemedText>
          <ThemedText style={styles.errorText}>
            This alumni profile could not be loaded.
          </ThemedText>
          <Button
            title="Go Back"
            onPress={() => router.back()}
            style={styles.errorButton}
          />
        </View>
      </ThemedView>
    );
  }

  const isOwnProfile = user?.id === profile.id;

  return (
    <ThemedView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <ThemedText style={styles.backButton}>‹ Back</ThemedText>
        </TouchableOpacity>
        <ThemedText type="subtitle">Alumni Profile</ThemedText>
        <View style={styles.headerSpacer} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Profile Header */}
        <Card variant="elevated" style={styles.profileCard}>
          <View style={styles.profileHeader}>
            <View style={styles.avatarContainer}>
              {profile.photoUrl ? (
                <Image source={{ uri: profile.photoUrl }} style={styles.avatar} />
              ) : (
                <View style={styles.avatarPlaceholder}>
                  <ThemedText style={styles.avatarText}>
                    {profile.name.charAt(0).toUpperCase()}
                  </ThemedText>
                </View>
              )}
            </View>
            
            <View style={styles.profileInfo}>
              <ThemedText type="title" style={styles.name}>
                {profile.name}
              </ThemedText>
              
              {profile.currentRole && (
                <ThemedText style={styles.role}>
                  {profile.currentRole}
                  {profile.company && ` at ${profile.company}`}
                </ThemedText>
              )}
              
              <View style={styles.profileDetails}>
                {profile.graduationYear && (
                  <ThemedText style={styles.detail}>
                    Class of {profile.graduationYear}
                  </ThemedText>
                )}
                
                {profile.city && profile.province && (
                  <ThemedText style={styles.detail}>
                    📍 {profile.city}, {profile.province}
                  </ThemedText>
                )}
              </View>
            </View>
          </View>
          
          {profile.bio && (
            <ThemedText style={styles.bio}>{profile.bio}</ThemedText>
          )}

          {/* Action Buttons */}
          {!isOwnProfile && (
            <View style={styles.actionButtons}>
              <Button
                title={
                  isConnected
                    ? 'Connected'
                    : connectionRequested
                    ? 'Request Sent'
                    : 'Connect'
                }
                onPress={handleConnect}
                variant={isConnected ? 'secondary' : 'primary'}
                disabled={isConnected || connectionRequested}
                style={styles.actionButton}
              />
              
              <Button
                title="Message"
                onPress={handleMessage}
                variant="outline"
                style={styles.actionButton}
              />
              
              <Button
                title="Contact"
                onPress={handleContact}
                variant="ghost"
                style={styles.actionButton}
              />
            </View>
          )}
        </Card>

        {/* Professional Information */}
        {(profile.industry || profile.skills?.length) && (
          <Card variant="elevated" style={styles.section}>
            <ThemedText type="subtitle" style={styles.sectionTitle}>
              Professional Information
            </ThemedText>
            
            {profile.industry && (
              <View style={styles.infoRow}>
                <ThemedText style={styles.infoLabel}>Industry:</ThemedText>
                <ThemedText style={styles.infoValue}>{profile.industry}</ThemedText>
              </View>
            )}
            
            {profile.skills && profile.skills.length > 0 && (
              <View style={styles.skillsContainer}>
                <ThemedText style={styles.infoLabel}>Skills:</ThemedText>
                <View style={styles.skillsGrid}>
                  {profile.skills.map((skill, index) => (
                    <View key={index} style={styles.skillTag}>
                      <ThemedText style={styles.skillText}>{skill}</ThemedText>
                    </View>
                  ))}
                </View>
              </View>
            )}
          </Card>
        )}

        {/* Education */}
        {profile.programmes && profile.programmes.length > 0 && (
          <Card variant="elevated" style={styles.section}>
            <ThemedText type="subtitle" style={styles.sectionTitle}>
              Education
            </ThemedText>
            
            <View style={styles.educationContainer}>
              <ThemedText style={styles.institutionName}>
                PROTEC
              </ThemedText>
              <ThemedText style={styles.programmes}>
                {profile.programmes.join(', ')}
              </ThemedText>
              <ThemedText style={styles.graduationYear}>
                Graduated {profile.graduationYear}
              </ThemedText>
            </View>
          </Card>
        )}

        {/* Interests */}
        {profile.interests && profile.interests.length > 0 && (
          <Card variant="elevated" style={styles.section}>
            <ThemedText type="subtitle" style={styles.sectionTitle}>
              Interests
            </ThemedText>
            
            <View style={styles.interestsGrid}>
              {profile.interests.map((interest, index) => (
                <View key={index} style={styles.interestTag}>
                  <ThemedText style={styles.interestText}>{interest}</ThemedText>
                </View>
              ))}
            </View>
          </Card>
        )}
      </ScrollView>

      {/* Contact Modal */}
      <Modal
        visible={showContactModal}
        onClose={() => setShowContactModal(false)}
        title="Contact Options"
        size="sm"
      >
        <View style={styles.contactOptions}>
          <TouchableOpacity
            style={styles.contactOption}
            onPress={handleEmailContact}
          >
            <ThemedText style={styles.contactOptionText}>
              📧 Send Email
            </ThemedText>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.contactOption}
            onPress={handlePhoneContact}
          >
            <ThemedText style={styles.contactOptionText}>
              📞 Phone Call
            </ThemedText>
          </TouchableOpacity>
        </View>
      </Modal>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  backButton: {
    color: '#012A5B',
    fontSize: 18,
    fontWeight: '600',
  },
  headerSpacer: {
    width: 50,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  errorState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  errorText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginTop: 8,
    marginBottom: 16,
  },
  errorButton: {
    marginTop: 8,
  },
  profileCard: {
    marginBottom: 16,
  },
  profileHeader: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  avatarContainer: {
    marginRight: 16,
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  avatarPlaceholder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: '#012A5B',
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    color: '#fff',
    fontSize: 40,
    fontWeight: 'bold',
  },
  profileInfo: {
    flex: 1,
  },
  name: {
    fontSize: 24,
    marginBottom: 4,
  },
  role: {
    fontSize: 16,
    color: '#666',
    marginBottom: 8,
  },
  profileDetails: {
    gap: 4,
  },
  detail: {
    fontSize: 14,
    color: '#888',
  },
  bio: {
    fontSize: 14,
    lineHeight: 20,
    color: '#666',
    marginBottom: 16,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
  section: {
    marginBottom: 16,
  },
  sectionTitle: {
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  infoLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666',
    width: 80,
  },
  infoValue: {
    fontSize: 14,
    flex: 1,
  },
  skillsContainer: {
    marginTop: 8,
  },
  skillsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginTop: 8,
  },
  skillTag: {
    backgroundColor: 'rgba(1, 42, 91, 0.1)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  skillText: {
    fontSize: 12,
    color: '#012A5B',
    fontWeight: '500',
  },
  educationContainer: {
    gap: 4,
  },
  institutionName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#012A5B',
  },
  programmes: {
    fontSize: 14,
    color: '#666',
  },
  graduationYear: {
    fontSize: 12,
    color: '#888',
  },
  interestsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  interestTag: {
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  interestText: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
  },
  contactOptions: {
    gap: 12,
  },
  contactOption: {
    padding: 16,
    borderRadius: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    alignItems: 'center',
  },
  contactOptionText: {
    fontSize: 16,
    fontWeight: '500',
  },
});
