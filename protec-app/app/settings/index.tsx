import React from 'react';
import { StyleSheet, ScrollView, TouchableOpacity, View, Switch } from 'react-native';
import { router } from 'expo-router';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useAuthStore } from '@/lib/stores/authStore';

export default function SettingsScreen() {
  const { user } = useAuthStore();
  const [notificationsEnabled, setNotificationsEnabled] = React.useState(true);
  const [darkModeEnabled, setDarkModeEnabled] = React.useState(false);

  const settingSections = [
    {
      title: 'Account',
      items: [
        { title: 'Edit Profile', onPress: () => router.push('/profile/edit') },
        { title: 'Privacy Settings', onPress: () => {} },
        { title: 'Change Password', onPress: () => {} },
      ],
    },
    {
      title: 'Notifications',
      items: [
        {
          title: 'Push Notifications',
          component: (
            <Switch
              value={notificationsEnabled}
              onValueChange={setNotificationsEnabled}
            />
          ),
        },
        { title: 'Email Preferences', onPress: () => {} },
      ],
    },
    {
      title: 'Appearance',
      items: [
        {
          title: 'Dark Mode',
          component: (
            <Switch
              value={darkModeEnabled}
              onValueChange={setDarkModeEnabled}
            />
          ),
        },
      ],
    },
    {
      title: 'Support',
      items: [
        { title: 'Help Center', onPress: () => {} },
        { title: 'Contact Support', onPress: () => {} },
        { title: 'Report a Bug', onPress: () => {} },
      ],
    },
    {
      title: 'About',
      items: [
        { title: 'Terms of Service', onPress: () => {} },
        { title: 'Privacy Policy', onPress: () => {} },
        { title: 'App Version', subtitle: '1.0.0' },
      ],
    },
  ];

  return (
    <ThemedView style={styles.container}>
      <ScrollView style={styles.content}>
        <ThemedView style={styles.header}>
          <ThemedText type="title">Settings</ThemedText>
          {user && (
            <ThemedText>Manage your account and app preferences</ThemedText>
          )}
        </ThemedView>

        {settingSections.map((section, sectionIndex) => (
          <ThemedView key={sectionIndex} style={styles.section}>
            <ThemedText type="subtitle" style={styles.sectionTitle}>
              {section.title}
            </ThemedText>
            
            <View style={styles.sectionContent}>
              {section.items.map((item, itemIndex) => (
                <TouchableOpacity
                  key={itemIndex}
                  style={styles.settingItem}
                  onPress={item.onPress}
                  disabled={!item.onPress}
                >
                  <View style={styles.settingItemContent}>
                    <View style={styles.settingItemText}>
                      <ThemedText type="defaultSemiBold">{item.title}</ThemedText>
                      {item.subtitle && (
                        <ThemedText style={styles.subtitle}>{item.subtitle}</ThemedText>
                      )}
                    </View>
                    {item.component && item.component}
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          </ThemedView>
        ))}
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  header: {
    marginBottom: 24,
    alignItems: 'center',
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    marginBottom: 12,
  },
  sectionContent: {
    backgroundColor: 'rgba(0,0,0,0.05)',
    borderRadius: 8,
    overflow: 'hidden',
  },
  settingItem: {
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  settingItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  settingItemText: {
    flex: 1,
  },
  subtitle: {
    opacity: 0.7,
    marginTop: 4,
  },
});
