import React, { useEffect, useState } from 'react';
import {
  StyleSheet,
  ScrollView,
  View,
  TouchableOpacity,
  Switch,
  Alert,
} from 'react-native';
import { router } from 'expo-router';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Button, Card } from '@/components/ui';
import { useNotificationsStore } from '@/lib/stores/notificationsStore';
import { NotificationData } from '@/lib/services/notificationService';

export default function NotificationSettingsScreen() {
  const {
    preferences,
    isInitialized,
    pushToken,
    permissionStatus,
    initializeNotifications,
    updatePreferences,
    requestPermissions,
    sendTestNotification,
    clearNotifications,
  } = useNotificationsStore();

  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (!isInitialized) {
      initializeNotifications();
    }
  }, [isInitialized, initializeNotifications]);

  const handleTogglePreference = async (key: keyof typeof preferences, value: boolean) => {
    await updatePreferences({ [key]: value });
  };

  const handleRequestPermissions = async () => {
    setIsLoading(true);
    try {
      const granted = await requestPermissions();
      if (granted) {
        Alert.alert('Success', 'Notification permissions granted!');
      } else {
        Alert.alert(
          'Permissions Required',
          'Please enable notifications in your device settings to receive important updates.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Open Settings', onPress: () => {
              // TODO: Open device settings
            }},
          ]
        );
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to request permissions. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleTestNotification = (type: NotificationData['type']) => {
    Alert.alert(
      'Send Test Notification',
      `Send a test ${type} notification?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Send',
          onPress: async () => {
            try {
              await sendTestNotification(type);
              Alert.alert('Success', 'Test notification sent!');
            } catch (error) {
              Alert.alert('Error', 'Failed to send test notification.');
            }
          },
        },
      ]
    );
  };

  const handleClearNotifications = () => {
    Alert.alert(
      'Clear All Notifications',
      'This will clear all notification history. Are you sure?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear',
          style: 'destructive',
          onPress: () => {
            clearNotifications();
            Alert.alert('Success', 'All notifications cleared.');
          },
        },
      ]
    );
  };

  const notificationTypes = [
    {
      key: 'messages' as const,
      title: 'Messages',
      description: 'New messages from alumni',
      icon: '💬',
    },
    {
      key: 'events' as const,
      title: 'Events',
      description: 'Event reminders and updates',
      icon: '📅',
    },
    {
      key: 'connections' as const,
      title: 'Connections',
      description: 'New connection requests',
      icon: '🤝',
    },
    {
      key: 'donations' as const,
      title: 'Donations',
      description: 'Donation campaigns and updates',
      icon: '💝',
    },
    {
      key: 'posts' as const,
      title: 'Posts',
      description: 'New posts and interactions',
      icon: '📰',
    },
    {
      key: 'general' as const,
      title: 'General',
      description: 'Important app updates and announcements',
      icon: '📢',
    },
  ];

  return (
    <ThemedView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <ThemedText style={styles.backButton}>‹ Back</ThemedText>
        </TouchableOpacity>
        <ThemedText type="subtitle">Notification Settings</ThemedText>
        <View style={styles.headerSpacer} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Permission Status */}
        <Card variant="elevated" style={styles.statusCard}>
          <ThemedText type="subtitle" style={styles.statusTitle}>
            Notification Status
          </ThemedText>
          
          <View style={styles.statusRow}>
            <ThemedText style={styles.statusLabel}>Permission:</ThemedText>
            <View style={[
              styles.statusBadge,
              permissionStatus === 'granted' ? styles.statusGranted : styles.statusDenied
            ]}>
              <ThemedText style={[
                styles.statusBadgeText,
                permissionStatus === 'granted' ? styles.statusGrantedText : styles.statusDeniedText
              ]}>
                {permissionStatus === 'granted' ? 'Granted' : 'Not Granted'}
              </ThemedText>
            </View>
          </View>
          
          <View style={styles.statusRow}>
            <ThemedText style={styles.statusLabel}>Push Token:</ThemedText>
            <ThemedText style={styles.statusValue}>
              {pushToken ? 'Available' : 'Not Available'}
            </ThemedText>
          </View>
          
          {permissionStatus !== 'granted' && (
            <Button
              title="Enable Notifications"
              onPress={handleRequestPermissions}
              disabled={isLoading}
              style={styles.enableButton}
            />
          )}
        </Card>

        {/* Notification Types */}
        <Card variant="elevated" style={styles.section}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>
            Notification Types
          </ThemedText>
          
          {notificationTypes.map((type) => (
            <View key={type.key} style={styles.preferenceRow}>
              <View style={styles.preferenceInfo}>
                <View style={styles.preferenceHeader}>
                  <ThemedText style={styles.preferenceIcon}>{type.icon}</ThemedText>
                  <ThemedText style={styles.preferenceTitle}>{type.title}</ThemedText>
                </View>
                <ThemedText style={styles.preferenceDescription}>
                  {type.description}
                </ThemedText>
              </View>
              
              <View style={styles.preferenceActions}>
                <Switch
                  value={preferences[type.key]}
                  onValueChange={(value) => handleTogglePreference(type.key, value)}
                  trackColor={{ false: '#ddd', true: '#012A5B' }}
                  thumbColor={preferences[type.key] ? '#fff' : '#f4f3f4'}
                />
                
                {preferences[type.key] && (
                  <TouchableOpacity
                    style={styles.testButton}
                    onPress={() => handleTestNotification(type.key)}
                  >
                    <ThemedText style={styles.testButtonText}>Test</ThemedText>
                  </TouchableOpacity>
                )}
              </View>
            </View>
          ))}
        </Card>

        {/* Sound & Vibration */}
        <Card variant="elevated" style={styles.section}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>
            Sound & Vibration
          </ThemedText>
          
          <View style={styles.preferenceRow}>
            <View style={styles.preferenceInfo}>
              <ThemedText style={styles.preferenceTitle}>🔊 Sound</ThemedText>
              <ThemedText style={styles.preferenceDescription}>
                Play sound for notifications
              </ThemedText>
            </View>
            <Switch
              value={preferences.sound}
              onValueChange={(value) => handleTogglePreference('sound', value)}
              trackColor={{ false: '#ddd', true: '#012A5B' }}
              thumbColor={preferences.sound ? '#fff' : '#f4f3f4'}
            />
          </View>
          
          <View style={styles.preferenceRow}>
            <View style={styles.preferenceInfo}>
              <ThemedText style={styles.preferenceTitle}>📳 Vibration</ThemedText>
              <ThemedText style={styles.preferenceDescription}>
                Vibrate for notifications
              </ThemedText>
            </View>
            <Switch
              value={preferences.vibration}
              onValueChange={(value) => handleTogglePreference('vibration', value)}
              trackColor={{ false: '#ddd', true: '#012A5B' }}
              thumbColor={preferences.vibration ? '#fff' : '#f4f3f4'}
            />
          </View>
        </Card>

        {/* Actions */}
        <Card variant="elevated" style={styles.section}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>
            Actions
          </ThemedText>
          
          <Button
            title="Send Test Notification"
            onPress={() => handleTestNotification('general')}
            variant="outline"
            style={styles.actionButton}
          />
          
          <Button
            title="Clear All Notifications"
            onPress={handleClearNotifications}
            variant="outline"
            style={styles.actionButton}
          />
        </Card>

        {/* Debug Info */}
        {__DEV__ && pushToken && (
          <Card variant="outlined" style={styles.debugCard}>
            <ThemedText type="subtitle" style={styles.debugTitle}>
              Debug Info
            </ThemedText>
            <ThemedText style={styles.debugText}>
              Push Token: {pushToken.substring(0, 20)}...
            </ThemedText>
          </Card>
        )}

        <View style={styles.bottomPadding} />
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  backButton: {
    color: '#012A5B',
    fontSize: 18,
    fontWeight: '600',
  },
  headerSpacer: {
    width: 50,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  statusCard: {
    marginBottom: 16,
    backgroundColor: 'rgba(1, 42, 91, 0.05)',
  },
  statusTitle: {
    marginBottom: 16,
    color: '#012A5B',
  },
  statusRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  statusLabel: {
    fontSize: 14,
    color: '#666',
  },
  statusValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusGranted: {
    backgroundColor: 'rgba(34, 197, 94, 0.1)',
  },
  statusDenied: {
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
  },
  statusBadgeText: {
    fontSize: 12,
    fontWeight: '500',
  },
  statusGrantedText: {
    color: '#22c55e',
  },
  statusDeniedText: {
    color: '#ef4444',
  },
  enableButton: {
    marginTop: 12,
  },
  section: {
    marginBottom: 16,
  },
  sectionTitle: {
    marginBottom: 16,
  },
  preferenceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.05)',
  },
  preferenceInfo: {
    flex: 1,
    marginRight: 16,
  },
  preferenceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  preferenceIcon: {
    fontSize: 16,
    marginRight: 8,
  },
  preferenceTitle: {
    fontSize: 16,
    fontWeight: '500',
  },
  preferenceDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 18,
  },
  preferenceActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  testButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    backgroundColor: 'rgba(1, 42, 91, 0.1)',
  },
  testButtonText: {
    fontSize: 12,
    color: '#012A5B',
    fontWeight: '500',
  },
  actionButton: {
    marginBottom: 12,
  },
  debugCard: {
    marginTop: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
  },
  debugTitle: {
    marginBottom: 8,
    fontSize: 14,
  },
  debugText: {
    fontSize: 12,
    fontFamily: 'monospace',
    color: '#666',
  },
  bottomPadding: {
    height: 32,
  },
});
