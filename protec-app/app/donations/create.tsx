import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Button, Card, Input } from '@/components/ui';
import { useAuth } from '@/lib/providers/AuthProvider';
import { PaymentService } from '@/lib/services/paymentService';
import { DonationFrequency } from '@/lib/types';
import { router, useLocalSearchParams } from 'expo-router';
import React, { useState } from 'react';
import {
    Alert,
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    StyleSheet,
    TouchableOpacity,
    View,
} from 'react-native';

interface DonationFormData {
  amount: string;
  frequency: DonationFrequency;
  campaign?: string;
  isAnonymous: boolean;
  paymentMethod: string;
  dedicationMessage?: string;
}

export default function CreateDonationScreen() {
  const { campaign } = useLocalSearchParams<{ campaign?: string }>();
  const { user } = useAuth();
  
  const [formData, setFormData] = useState<DonationFormData>({
    amount: '',
    frequency: DonationFrequency.ONE_TIME,
    campaign: campaign,
    isAnonymous: false,
    paymentMethod: 'PayFast',
    dedicationMessage: '',
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = (field: keyof DonationFormData, value: string | boolean | DonationFrequency) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const validateForm = (): boolean => {
    const amount = parseFloat(formData.amount);
    
    if (!formData.amount || isNaN(amount) || amount <= 0) {
      Alert.alert('Error', 'Please enter a valid donation amount');
      return false;
    }
    
    if (amount < 50) {
      Alert.alert('Error', 'Minimum donation amount is R50');
      return false;
    }
    
    if (amount > 100000) {
      Alert.alert('Error', 'Maximum donation amount is R100,000. For larger donations, please contact us directly.');
      return false;
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm() || !user) return;

    setIsSubmitting(true);
    try {
      const amount = parseFloat(formData.amount);
      const currency = 'ZAR'; // Default to ZAR for South African users

      // Show payment gateway selection
      PaymentService.showPaymentGatewaySelection(
        amount,
        currency,
        async (gateway) => {
          try {
            const paymentRequest = {
              amount,
              currency,
              gateway: gateway as any,
              purpose: formData.campaign || 'General Donation',
              frequency: formData.frequency,
              donorEmail: user.email,
              donorName: user.name,
              isAnonymous: formData.isAnonymous,
              dedicationMessage: formData.dedicationMessage?.trim(),
            };

            let paymentResult;

            switch (gateway) {
              case 'payfast':
                paymentResult = await PaymentService.processPayFastPayment(paymentRequest);
                break;
              case 'snapscan':
                paymentResult = await PaymentService.processSnapScanPayment(paymentRequest);
                break;
              case 'ozow':
                paymentResult = await PaymentService.processOzowPayment(paymentRequest);
                break;
              case 'paypal':
                paymentResult = await PaymentService.processPayPalPayment({
                  ...paymentRequest,
                  currency: 'USD'
                });
                break;
              default:
                throw new Error('Unsupported payment gateway');
            }

            if (paymentResult.success) {
              Alert.alert(
                'Payment Initiated',
                'Your payment has been initiated. You will receive a confirmation email once the payment is processed.',
                [
                  {
                    text: 'OK',
                    onPress: () => router.back()
                  }
                ]
              );
            } else {
              Alert.alert('Payment Error', paymentResult.error || 'Failed to process payment');
            }
          } catch (error) {
            console.error('Payment processing error:', error);
            Alert.alert('Error', 'Failed to process payment. Please try again.');
          } finally {
            setIsSubmitting(false);
          }
        }
      );
    } catch (error) {
      console.error('Payment setup error:', error);
      Alert.alert('Error', 'Failed to setup payment. Please try again.');
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    if (formData.amount || formData.dedicationMessage) {
      Alert.alert(
        'Cancel Donation',
        'Are you sure you want to cancel? All information will be lost.',
        [
          { text: 'Continue', style: 'cancel' },
          { text: 'Cancel', style: 'destructive', onPress: () => router.back() },
        ]
      );
    } else {
      router.back();
    }
  };

  const formatCurrency = (amount: string) => {
    const num = parseFloat(amount);
    if (isNaN(num)) return '';
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
    }).format(num);
  };

  const quickAmounts = [100, 250, 500, 1000, 2500, 5000];
  const frequencies = [
    { value: DonationFrequency.ONE_TIME, label: 'One-time' },
    { value: DonationFrequency.MONTHLY, label: 'Monthly' },
    { value: DonationFrequency.QUARTERLY, label: 'Quarterly' },
    { value: DonationFrequency.ANNUALLY, label: 'Annually' },
  ];

  const paymentMethods = [
    { value: 'PayFast', label: 'PayFast', description: 'Credit/Debit Card, EFT' },
    { value: 'SnapScan', label: 'SnapScan', description: 'QR Code Payment' },
    { value: 'Ozow', label: 'Ozow', description: 'Instant EFT' },
  ];

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ThemedView style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={handleCancel}>
            <ThemedText style={styles.cancelButton}>Cancel</ThemedText>
          </TouchableOpacity>
          <ThemedText type="subtitle">Make a Donation</ThemedText>
          <View style={styles.headerSpacer} />
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Donation Summary */}
          <Card variant="elevated" style={styles.summaryCard}>
            <ThemedText type="subtitle" style={styles.summaryTitle}>
              Donation Summary
            </ThemedText>
            
            <View style={styles.summaryRow}>
              <ThemedText style={styles.summaryLabel}>Amount:</ThemedText>
              <ThemedText style={styles.summaryValue}>
                {formData.amount ? formatCurrency(formData.amount) : 'R0.00'}
              </ThemedText>
            </View>
            
            <View style={styles.summaryRow}>
              <ThemedText style={styles.summaryLabel}>Frequency:</ThemedText>
              <ThemedText style={styles.summaryValue}>
                {frequencies.find(f => f.value === formData.frequency)?.label}
              </ThemedText>
            </View>
            
            {formData.campaign && (
              <View style={styles.summaryRow}>
                <ThemedText style={styles.summaryLabel}>Campaign:</ThemedText>
                <ThemedText style={styles.summaryValue}>{formData.campaign}</ThemedText>
              </View>
            )}
          </Card>

          {/* Amount Selection */}
          <Card variant="outlined" style={styles.section}>
            <ThemedText type="subtitle" style={styles.sectionTitle}>
              Donation Amount
            </ThemedText>
            
            {/* Quick Amount Buttons */}
            <View style={styles.quickAmountsContainer}>
              <ThemedText style={styles.quickAmountsLabel}>Quick amounts:</ThemedText>
              <View style={styles.quickAmountsGrid}>
                {quickAmounts.map((amount) => (
                  <TouchableOpacity
                    key={amount}
                    style={[
                      styles.quickAmountButton,
                      formData.amount === amount.toString() && styles.quickAmountButtonSelected
                    ]}
                    onPress={() => handleInputChange('amount', amount.toString())}
                  >
                    <ThemedText style={[
                      styles.quickAmountText,
                      formData.amount === amount.toString() && styles.quickAmountTextSelected
                    ]}>
                      R{amount.toLocaleString()}
                    </ThemedText>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
            
            {/* Custom Amount Input */}
            <Input
              label="Custom Amount (ZAR)"
              value={formData.amount}
              onChangeText={(value) => handleInputChange('amount', value)}
              placeholder="Enter amount"
              keyboardType="numeric"
              helperText="Minimum: R50, Maximum: R100,000"
            />
          </Card>

          {/* Frequency Selection */}
          <Card variant="outlined" style={styles.section}>
            <ThemedText type="subtitle" style={styles.sectionTitle}>
              Donation Frequency
            </ThemedText>
            
            <View style={styles.frequencyContainer}>
              {frequencies.map((freq) => (
                <TouchableOpacity
                  key={freq.value}
                  style={[
                    styles.frequencyOption,
                    formData.frequency === freq.value && styles.frequencyOptionSelected
                  ]}
                  onPress={() => handleInputChange('frequency', freq.value)}
                >
                  <View style={[
                    styles.frequencyRadio,
                    formData.frequency === freq.value && styles.frequencyRadioSelected
                  ]}>
                    {formData.frequency === freq.value && (
                      <View style={styles.frequencyRadioDot} />
                    )}
                  </View>
                  <ThemedText style={[
                    styles.frequencyLabel,
                    formData.frequency === freq.value && styles.frequencyLabelSelected
                  ]}>
                    {freq.label}
                  </ThemedText>
                </TouchableOpacity>
              ))}
            </View>
          </Card>

          {/* Payment Method */}
          <Card variant="outlined" style={styles.section}>
            <ThemedText type="subtitle" style={styles.sectionTitle}>
              Payment Method
            </ThemedText>
            
            <View style={styles.paymentMethodsContainer}>
              {paymentMethods.map((method) => (
                <TouchableOpacity
                  key={method.value}
                  style={[
                    styles.paymentMethodOption,
                    formData.paymentMethod === method.value && styles.paymentMethodOptionSelected
                  ]}
                  onPress={() => handleInputChange('paymentMethod', method.value)}
                >
                  <View style={styles.paymentMethodInfo}>
                    <ThemedText style={[
                      styles.paymentMethodLabel,
                      formData.paymentMethod === method.value && styles.paymentMethodLabelSelected
                    ]}>
                      {method.label}
                    </ThemedText>
                    <ThemedText style={styles.paymentMethodDescription}>
                      {method.description}
                    </ThemedText>
                  </View>
                  <View style={[
                    styles.paymentMethodRadio,
                    formData.paymentMethod === method.value && styles.paymentMethodRadioSelected
                  ]}>
                    {formData.paymentMethod === method.value && (
                      <View style={styles.paymentMethodRadioDot} />
                    )}
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          </Card>

          {/* Additional Options */}
          <Card variant="outlined" style={styles.section}>
            <ThemedText type="subtitle" style={styles.sectionTitle}>
              Additional Options
            </ThemedText>
            
            {/* Anonymous Donation Toggle */}
            <View style={styles.toggleContainer}>
              <View style={styles.toggleInfo}>
                <ThemedText style={styles.toggleLabel}>Anonymous Donation</ThemedText>
                <ThemedText style={styles.toggleDescription}>
                  Your name will not be displayed publicly
                </ThemedText>
              </View>
              <TouchableOpacity
                style={[styles.toggle, formData.isAnonymous && styles.toggleActive]}
                onPress={() => handleInputChange('isAnonymous', !formData.isAnonymous)}
              >
                <View style={[styles.toggleThumb, formData.isAnonymous && styles.toggleThumbActive]} />
              </TouchableOpacity>
            </View>
            
            {/* Dedication Message */}
            <Input
              label="Dedication Message (Optional)"
              value={formData.dedicationMessage}
              onChangeText={(value) => handleInputChange('dedicationMessage', value)}
              placeholder="In memory of... or In honor of..."
              multiline
              numberOfLines={3}
              style={styles.dedicationInput}
            />
          </Card>

          {/* Submit Button */}
          <Button
            title={isSubmitting ? 'Processing...' : `Donate ${formData.amount ? formatCurrency(formData.amount) : 'Now'}`}
            onPress={handleSubmit}
            disabled={isSubmitting || !formData.amount}
            style={styles.submitButton}
          />

          <View style={styles.bottomPadding} />
        </ScrollView>
      </ThemedView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  cancelButton: {
    color: '#666',
    fontSize: 16,
  },
  headerSpacer: {
    width: 50,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  summaryCard: {
    marginBottom: 16,
    backgroundColor: 'rgba(1, 42, 91, 0.05)',
  },
  summaryTitle: {
    marginBottom: 12,
    color: '#012A5B',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 14,
    color: '#666',
  },
  summaryValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#012A5B',
  },
  section: {
    marginBottom: 16,
  },
  sectionTitle: {
    marginBottom: 16,
  },
  quickAmountsContainer: {
    marginBottom: 16,
  },
  quickAmountsLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  quickAmountsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  quickAmountButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
    backgroundColor: '#fff',
  },
  quickAmountButtonSelected: {
    backgroundColor: '#012A5B',
    borderColor: '#012A5B',
  },
  quickAmountText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  quickAmountTextSelected: {
    color: '#fff',
  },
  frequencyContainer: {
    gap: 12,
  },
  frequencyOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
    backgroundColor: '#fff',
  },
  frequencyOptionSelected: {
    backgroundColor: 'rgba(1, 42, 91, 0.05)',
    borderColor: '#012A5B',
  },
  frequencyRadio: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#ddd',
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  frequencyRadioSelected: {
    borderColor: '#012A5B',
  },
  frequencyRadioDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#012A5B',
  },
  frequencyLabel: {
    fontSize: 16,
    color: '#333',
  },
  frequencyLabelSelected: {
    color: '#012A5B',
    fontWeight: '500',
  },
  paymentMethodsContainer: {
    gap: 12,
  },
  paymentMethodOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
    backgroundColor: '#fff',
  },
  paymentMethodOptionSelected: {
    backgroundColor: 'rgba(1, 42, 91, 0.05)',
    borderColor: '#012A5B',
  },
  paymentMethodInfo: {
    flex: 1,
  },
  paymentMethodLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  paymentMethodLabelSelected: {
    color: '#012A5B',
  },
  paymentMethodDescription: {
    fontSize: 14,
    color: '#666',
  },
  paymentMethodRadio: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#ddd',
    justifyContent: 'center',
    alignItems: 'center',
  },
  paymentMethodRadioSelected: {
    borderColor: '#012A5B',
  },
  paymentMethodRadioDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#012A5B',
  },
  toggleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    marginBottom: 16,
  },
  toggleInfo: {
    flex: 1,
  },
  toggleLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  toggleDescription: {
    fontSize: 14,
    color: '#666',
  },
  toggle: {
    width: 50,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#ddd',
    justifyContent: 'center',
    paddingHorizontal: 2,
  },
  toggleActive: {
    backgroundColor: '#012A5B',
  },
  toggleThumb: {
    width: 26,
    height: 26,
    borderRadius: 13,
    backgroundColor: '#fff',
    alignSelf: 'flex-start',
  },
  toggleThumbActive: {
    alignSelf: 'flex-end',
  },
  dedicationInput: {
    height: 80,
    textAlignVertical: 'top',
  },
  submitButton: {
    marginTop: 8,
    marginBottom: 16,
  },
  bottomPadding: {
    height: 32,
  },
});
