import React from 'react';
import { StyleSheet, ScrollView, TouchableOpacity, View } from 'react-native';
import { router } from 'expo-router';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';

export default function DonationsScreen() {
  return (
    <ThemedView style={styles.container}>
      <ScrollView style={styles.content}>
        <ThemedView style={styles.header}>
          <ThemedText type="title">Support PROTEC</ThemedText>
          <ThemedText>
            Help us continue empowering the next generation of STEM leaders in South Africa.
          </ThemedText>
        </ThemedView>

        {/* Donation Options */}
        <ThemedView style={styles.section}>
          <ThemedText type="subtitle">Make a Donation</ThemedText>
          
          <View style={styles.donationGrid}>
            <TouchableOpacity style={styles.donationCard}>
              <ThemedText type="defaultSemiBold">R100</ThemedText>
              <ThemedText>Support a student for a day</ThemedText>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.donationCard}>
              <ThemedText type="defaultSemiBold">R500</ThemedText>
              <ThemedText>Fund a workshop</ThemedText>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.donationCard}>
              <ThemedText type="defaultSemiBold">R1,000</ThemedText>
              <ThemedText>Sponsor equipment</ThemedText>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.donationCard}>
              <ThemedText type="defaultSemiBold">Custom</ThemedText>
              <ThemedText>Choose your amount</ThemedText>
            </TouchableOpacity>
          </View>
        </ThemedView>

        {/* Impact Section */}
        <ThemedView style={styles.section}>
          <ThemedText type="subtitle">Your Impact</ThemedText>
          <ThemedView style={styles.impactCard}>
            <ThemedText type="defaultSemiBold">Since 1982</ThemedText>
            <ThemedText>• Over 10,000 students supported</ThemedText>
            <ThemedText>• 85% graduation rate in STEM fields</ThemedText>
            <ThemedText>• Alumni working in 50+ countries</ThemedText>
            <ThemedText>• R50M+ in scholarships awarded</ThemedText>
          </ThemedView>
        </ThemedView>

        {/* Donation History */}
        <ThemedView style={styles.section}>
          <ThemedText type="subtitle">Your Donations</ThemedText>
          <TouchableOpacity 
            style={styles.historyButton}
            onPress={() => router.push('/donations/history')}
          >
            <ThemedText>View Donation History</ThemedText>
          </TouchableOpacity>
        </ThemedView>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  header: {
    marginBottom: 24,
    alignItems: 'center',
  },
  section: {
    marginBottom: 24,
  },
  donationGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginTop: 12,
  },
  donationCard: {
    flex: 1,
    minWidth: '45%',
    padding: 16,
    borderRadius: 8,
    backgroundColor: 'rgba(1, 42, 91, 0.1)',
    alignItems: 'center',
  },
  impactCard: {
    padding: 16,
    borderRadius: 8,
    backgroundColor: 'rgba(0,0,0,0.05)',
    marginTop: 12,
  },
  historyButton: {
    padding: 16,
    borderRadius: 8,
    backgroundColor: 'rgba(0,0,0,0.05)',
    alignItems: 'center',
    marginTop: 12,
  },
});
