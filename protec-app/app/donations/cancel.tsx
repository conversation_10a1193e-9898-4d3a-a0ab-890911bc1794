import React from 'react';
import {
  StyleSheet,
  View,
  ScrollView,
} from 'react-native';
import { router } from 'expo-router';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';
import { DesignSystem } from '@/constants/DesignSystem';
import { Ionicons } from '@expo/vector-icons';

export default function PaymentCancelScreen() {
  const handleReturnHome = () => {
    router.replace('/(tabs)');
  };

  const handleTryAgain = () => {
    router.back();
  };

  const handleViewDonations = () => {
    router.push('/donations');
  };

  return (
    <ThemedView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <Card style={styles.card}>
          <View style={styles.iconContainer}>
            <Ionicons 
              name="ban" 
              size={64} 
              color={DesignSystem.Colors.warning} 
            />
          </View>
          
          <ThemedText style={styles.title}>
            Payment Cancelled
          </ThemedText>
          
          <ThemedText style={styles.message}>
            Your payment was cancelled and no charges were made to your account. 
            You can try again anytime or explore other ways to support the PROTEC Alumni community.
          </ThemedText>
          
          <ThemedText style={styles.supportMessage}>
            Your support means everything to us. Every contribution, no matter the size, 
            helps us build a stronger alumni network and create more opportunities for our community.
          </ThemedText>
          
          <View style={styles.buttonContainer}>
            <Button
              title="Try Again"
              onPress={handleTryAgain}
              style={[styles.button, styles.primaryButton]}
            />
            
            <Button
              title="View Other Ways to Help"
              onPress={handleViewDonations}
              style={styles.button}
              variant="outline"
            />
            
            <Button
              title="Return Home"
              onPress={handleReturnHome}
              style={styles.button}
              variant="ghost"
            />
          </View>
        </Card>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: DesignSystem.Spacing.lg,
  },
  card: {
    padding: DesignSystem.Spacing.xl,
    alignItems: 'center',
  },
  iconContainer: {
    marginBottom: DesignSystem.Spacing.lg,
  },
  title: {
    fontSize: DesignSystem.Typography.h2.fontSize,
    fontWeight: DesignSystem.Typography.h2.fontWeight as any,
    textAlign: 'center',
    marginBottom: DesignSystem.Spacing.md,
  },
  message: {
    fontSize: DesignSystem.Typography.body.fontSize,
    textAlign: 'center',
    marginBottom: DesignSystem.Spacing.lg,
    lineHeight: 24,
  },
  supportMessage: {
    fontSize: DesignSystem.Typography.caption.fontSize,
    textAlign: 'center',
    marginBottom: DesignSystem.Spacing.xl,
    lineHeight: 20,
    fontStyle: 'italic',
    opacity: 0.8,
  },
  buttonContainer: {
    width: '100%',
    gap: DesignSystem.Spacing.md,
  },
  button: {
    width: '100%',
  },
  primaryButton: {
    marginBottom: DesignSystem.Spacing.sm,
  },
});
