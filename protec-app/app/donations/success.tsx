import React, { useEffect, useState } from 'react';
import {
  StyleSheet,
  View,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';
import { PaymentService, PaymentStatus } from '@/lib/services/paymentService';
import { DesignSystem } from '@/constants/DesignSystem';
import { Ionicons } from '@expo/vector-icons';
import { useThemeColor } from '@/hooks/useThemeColor';

export default function PaymentSuccessScreen() {
  const { paymentId, donationId } = useLocalSearchParams<{
    paymentId?: string;
    donationId?: string;
  }>();

  const [paymentStatus, setPaymentStatus] = useState<PaymentStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const successColor = useThemeColor({}, 'success');
  const textColor = useThemeColor({}, 'text');

  useEffect(() => {
    if (paymentId || donationId) {
      checkPaymentStatus();
    } else {
      setError('No payment information provided');
      setIsLoading(false);
    }
  }, [paymentId, donationId]);

  const checkPaymentStatus = async () => {
    try {
      setIsLoading(true);
      const id = paymentId || donationId;
      
      if (!id) {
        setError('No payment ID provided');
        return;
      }

      const status = await PaymentService.getPaymentStatus(id);
      
      if (status) {
        setPaymentStatus(status);
        
        // If payment is still pending, check again after a delay
        if (status.status === 'pending') {
          setTimeout(() => {
            checkPaymentStatus();
          }, 3000);
        }
      } else {
        setError('Payment not found');
      }
    } catch (error) {
      console.error('Error checking payment status:', error);
      setError('Failed to check payment status');
    } finally {
      setIsLoading(false);
    }
  };

  const handleReturnHome = () => {
    router.replace('/(tabs)');
  };

  const handleViewDonations = () => {
    router.push('/donations');
  };

  if (isLoading) {
    return (
      <ThemedView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={successColor} />
          <ThemedText style={styles.loadingText}>
            Checking payment status...
          </ThemedText>
        </View>
      </ThemedView>
    );
  }

  if (error) {
    return (
      <ThemedView style={styles.container}>
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <Card style={styles.card}>
            <View style={styles.iconContainer}>
              <Ionicons 
                name="alert-circle" 
                size={64} 
                color={DesignSystem.Colors.error} 
              />
            </View>
            
            <ThemedText style={styles.title}>
              Payment Error
            </ThemedText>
            
            <ThemedText style={styles.message}>
              {error}
            </ThemedText>
            
            <View style={styles.buttonContainer}>
              <Button
                title="Return Home"
                onPress={handleReturnHome}
                style={styles.button}
              />
            </View>
          </Card>
        </ScrollView>
      </ThemedView>
    );
  }

  if (!paymentStatus) {
    return (
      <ThemedView style={styles.container}>
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <Card style={styles.card}>
            <View style={styles.iconContainer}>
              <Ionicons 
                name="help-circle" 
                size={64} 
                color={DesignSystem.Colors.warning} 
              />
            </View>
            
            <ThemedText style={styles.title}>
              Payment Status Unknown
            </ThemedText>
            
            <ThemedText style={styles.message}>
              We couldn't find information about your payment. Please contact support if you believe this is an error.
            </ThemedText>
            
            <View style={styles.buttonContainer}>
              <Button
                title="Return Home"
                onPress={handleReturnHome}
                style={styles.button}
              />
            </View>
          </Card>
        </ScrollView>
      </ThemedView>
    );
  }

  const getStatusIcon = () => {
    switch (paymentStatus.status) {
      case 'completed':
        return <Ionicons name="checkmark-circle" size={64} color={successColor} />;
      case 'pending':
        return <ActivityIndicator size={64} color={DesignSystem.Colors.warning} />;
      case 'failed':
        return <Ionicons name="close-circle" size={64} color={DesignSystem.Colors.error} />;
      case 'cancelled':
        return <Ionicons name="ban" size={64} color={DesignSystem.Colors.error} />;
      default:
        return <Ionicons name="help-circle" size={64} color={DesignSystem.Colors.warning} />;
    }
  };

  const getStatusTitle = () => {
    switch (paymentStatus.status) {
      case 'completed':
        return 'Payment Successful!';
      case 'pending':
        return 'Payment Processing';
      case 'failed':
        return 'Payment Failed';
      case 'cancelled':
        return 'Payment Cancelled';
      default:
        return 'Payment Status Unknown';
    }
  };

  const getStatusMessage = () => {
    switch (paymentStatus.status) {
      case 'completed':
        return 'Thank you for your generous donation! Your contribution will make a real difference in the PROTEC Alumni community.';
      case 'pending':
        return 'Your payment is being processed. You will receive a confirmation email once the payment is complete.';
      case 'failed':
        return 'Your payment could not be processed. Please try again or contact support if the problem persists.';
      case 'cancelled':
        return 'Your payment was cancelled. You can try again anytime.';
      default:
        return 'We are unable to determine the status of your payment at this time.';
    }
  };

  return (
    <ThemedView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <Card style={styles.card}>
          <View style={styles.iconContainer}>
            {getStatusIcon()}
          </View>
          
          <ThemedText style={styles.title}>
            {getStatusTitle()}
          </ThemedText>
          
          <ThemedText style={styles.message}>
            {getStatusMessage()}
          </ThemedText>
          
          {paymentStatus.status === 'completed' && (
            <View style={styles.detailsContainer}>
              <ThemedText style={styles.detailLabel}>Amount:</ThemedText>
              <ThemedText style={styles.detailValue}>
                {paymentStatus.currency} {paymentStatus.amount.toFixed(2)}
              </ThemedText>
              
              <ThemedText style={styles.detailLabel}>Payment Method:</ThemedText>
              <ThemedText style={styles.detailValue}>
                {paymentStatus.gateway.toUpperCase()}
              </ThemedText>
              
              <ThemedText style={styles.detailLabel}>Date:</ThemedText>
              <ThemedText style={styles.detailValue}>
                {paymentStatus.completedAt?.toLocaleDateString() || paymentStatus.createdAt.toLocaleDateString()}
              </ThemedText>
            </View>
          )}
          
          <View style={styles.buttonContainer}>
            {paymentStatus.status === 'completed' && (
              <Button
                title="View My Donations"
                onPress={handleViewDonations}
                style={[styles.button, styles.primaryButton]}
              />
            )}
            
            <Button
              title="Return Home"
              onPress={handleReturnHome}
              style={styles.button}
              variant={paymentStatus.status === 'completed' ? 'outline' : 'primary'}
            />
          </View>
        </Card>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: DesignSystem.Spacing.lg,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: DesignSystem.Spacing.md,
    fontSize: DesignSystem.Typography.body.fontSize,
  },
  card: {
    padding: DesignSystem.Spacing.xl,
    alignItems: 'center',
  },
  iconContainer: {
    marginBottom: DesignSystem.Spacing.lg,
  },
  title: {
    fontSize: DesignSystem.Typography.h2.fontSize,
    fontWeight: DesignSystem.Typography.h2.fontWeight as any,
    textAlign: 'center',
    marginBottom: DesignSystem.Spacing.md,
  },
  message: {
    fontSize: DesignSystem.Typography.body.fontSize,
    textAlign: 'center',
    marginBottom: DesignSystem.Spacing.lg,
    lineHeight: 24,
  },
  detailsContainer: {
    width: '100%',
    marginBottom: DesignSystem.Spacing.lg,
  },
  detailLabel: {
    fontSize: DesignSystem.Typography.caption.fontSize,
    fontWeight: '600',
    marginTop: DesignSystem.Spacing.sm,
    marginBottom: DesignSystem.Spacing.xs,
  },
  detailValue: {
    fontSize: DesignSystem.Typography.body.fontSize,
    marginBottom: DesignSystem.Spacing.sm,
  },
  buttonContainer: {
    width: '100%',
    gap: DesignSystem.Spacing.md,
  },
  button: {
    width: '100%',
  },
  primaryButton: {
    marginBottom: DesignSystem.Spacing.sm,
  },
});
