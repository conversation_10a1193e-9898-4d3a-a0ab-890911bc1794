import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { router } from 'expo-router';
import LoginScreen from '../login';
import { useAuthStore } from '@/lib/stores/authStore';

// Mock expo-router
jest.mock('expo-router');
const mockRouter = router as jest.Mocked<typeof router>;

// Mock auth store
jest.mock('@/lib/stores/authStore');
const mockUseAuthStore = useAuthStore as jest.MockedFunction<typeof useAuthStore>;

describe('LoginScreen', () => {
  const mockLogin = jest.fn();
  const mockUser = {
    id: 'test-user',
    email: '<EMAIL>',
    name: 'Test User',
    role: 'ALUMNI' as const,
    isActive: true,
    province: 'Gauteng',
    city: 'Johannesburg',
    country: 'South Africa',
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockUseAuthStore.mockReturnValue({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      login: mockLogin,
      logout: jest.fn(),
      register: jest.fn(),
      updateProfile: jest.fn(),
      loadAuthState: jest.fn(),
    });
  });

  it('renders login form correctly', () => {
    const { getByText, getByPlaceholderText } = render(<LoginScreen />);
    
    expect(getByText('Welcome Back')).toBeTruthy();
    expect(getByText('Sign in to your PROTEC Alumni account')).toBeTruthy();
    expect(getByPlaceholderText('Enter your email')).toBeTruthy();
    expect(getByPlaceholderText('Enter your password')).toBeTruthy();
    expect(getByText('Sign In')).toBeTruthy();
    expect(getByText('Create Account')).toBeTruthy();
  });

  it('validates email field', async () => {
    const { getByPlaceholderText, getByText, queryByText } = render(<LoginScreen />);
    
    const emailInput = getByPlaceholderText('Enter your email');
    const signInButton = getByText('Sign In');
    
    // Try to submit with invalid email
    fireEvent.changeText(emailInput, 'invalid-email');
    fireEvent.press(signInButton);
    
    await waitFor(() => {
      expect(queryByText('Please enter a valid email address')).toBeTruthy();
    });
  });

  it('validates password field', async () => {
    const { getByPlaceholderText, getByText, queryByText } = render(<LoginScreen />);
    
    const emailInput = getByPlaceholderText('Enter your email');
    const passwordInput = getByPlaceholderText('Enter your password');
    const signInButton = getByText('Sign In');
    
    // Enter valid email but no password
    fireEvent.changeText(emailInput, '<EMAIL>');
    fireEvent.changeText(passwordInput, '');
    fireEvent.press(signInButton);
    
    await waitFor(() => {
      expect(queryByText('Password is required')).toBeTruthy();
    });
  });

  it('submits form with valid credentials', async () => {
    mockLogin.mockResolvedValue(undefined);
    
    const { getByPlaceholderText, getByText } = render(<LoginScreen />);
    
    const emailInput = getByPlaceholderText('Enter your email');
    const passwordInput = getByPlaceholderText('Enter your password');
    const signInButton = getByText('Sign In');
    
    // Enter valid credentials
    fireEvent.changeText(emailInput, '<EMAIL>');
    fireEvent.changeText(passwordInput, 'password123');
    fireEvent.press(signInButton);
    
    await waitFor(() => {
      expect(mockLogin).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
      });
    });
  });

  it('handles login success', async () => {
    mockLogin.mockResolvedValue(undefined);
    mockUseAuthStore.mockReturnValue({
      user: mockUser,
      isAuthenticated: true,
      isLoading: false,
      login: mockLogin,
      logout: jest.fn(),
      register: jest.fn(),
      updateProfile: jest.fn(),
      loadAuthState: jest.fn(),
    });
    
    const { getByPlaceholderText, getByText } = render(<LoginScreen />);
    
    const emailInput = getByPlaceholderText('Enter your email');
    const passwordInput = getByPlaceholderText('Enter your password');
    const signInButton = getByText('Sign In');
    
    fireEvent.changeText(emailInput, '<EMAIL>');
    fireEvent.changeText(passwordInput, 'password123');
    fireEvent.press(signInButton);
    
    await waitFor(() => {
      expect(mockRouter.replace).toHaveBeenCalledWith('/(tabs)/');
    });
  });

  it('handles login failure', async () => {
    const errorMessage = 'Invalid credentials';
    mockLogin.mockRejectedValue(new Error(errorMessage));
    
    const { getByPlaceholderText, getByText, queryByText } = render(<LoginScreen />);
    
    const emailInput = getByPlaceholderText('Enter your email');
    const passwordInput = getByPlaceholderText('Enter your password');
    const signInButton = getByText('Sign In');
    
    fireEvent.changeText(emailInput, '<EMAIL>');
    fireEvent.changeText(passwordInput, 'wrongpassword');
    fireEvent.press(signInButton);
    
    await waitFor(() => {
      expect(queryByText(errorMessage)).toBeTruthy();
    });
  });

  it('shows loading state during login', async () => {
    mockUseAuthStore.mockReturnValue({
      user: null,
      isAuthenticated: false,
      isLoading: true,
      login: mockLogin,
      logout: jest.fn(),
      register: jest.fn(),
      updateProfile: jest.fn(),
      loadAuthState: jest.fn(),
    });
    
    const { getByText } = render(<LoginScreen />);
    
    expect(getByText('Signing In...')).toBeTruthy();
  });

  it('navigates to register screen', () => {
    const { getByText } = render(<LoginScreen />);
    
    const createAccountButton = getByText('Create Account');
    fireEvent.press(createAccountButton);
    
    expect(mockRouter.push).toHaveBeenCalledWith('/auth/register');
  });

  it('navigates to forgot password screen', () => {
    const { getByText } = render(<LoginScreen />);
    
    const forgotPasswordLink = getByText('Forgot Password?');
    fireEvent.press(forgotPasswordLink);
    
    expect(mockRouter.push).toHaveBeenCalledWith('/auth/forgot-password');
  });

  it('toggles password visibility', () => {
    const { getByPlaceholderText, getByTestId } = render(<LoginScreen />);
    
    const passwordInput = getByPlaceholderText('Enter your password');
    const toggleButton = getByTestId('password-toggle');
    
    // Initially password should be hidden
    expect(passwordInput.props.secureTextEntry).toBe(true);
    
    // Toggle to show password
    fireEvent.press(toggleButton);
    expect(passwordInput.props.secureTextEntry).toBe(false);
    
    // Toggle to hide password again
    fireEvent.press(toggleButton);
    expect(passwordInput.props.secureTextEntry).toBe(true);
  });

  it('handles remember me toggle', () => {
    const { getByTestId } = render(<LoginScreen />);
    
    const rememberMeToggle = getByTestId('remember-me-toggle');
    
    // Initially should be false
    expect(rememberMeToggle.props.value).toBe(false);
    
    // Toggle remember me
    fireEvent(rememberMeToggle, 'onValueChange', true);
    expect(rememberMeToggle.props.value).toBe(true);
  });

  describe('Form Validation', () => {
    it('validates email format', async () => {
      const { getByPlaceholderText, getByText, queryByText } = render(<LoginScreen />);
      
      const emailInput = getByPlaceholderText('Enter your email');
      const signInButton = getByText('Sign In');
      
      // Test various invalid email formats
      const invalidEmails = [
        'invalid',
        'invalid@',
        '@invalid.com',
        'invalid.com',
        'invalid@.com',
      ];
      
      for (const email of invalidEmails) {
        fireEvent.changeText(emailInput, email);
        fireEvent.press(signInButton);
        
        await waitFor(() => {
          expect(queryByText('Please enter a valid email address')).toBeTruthy();
        });
      }
    });

    it('validates password requirements', async () => {
      const { getByPlaceholderText, getByText, queryByText } = render(<LoginScreen />);
      
      const emailInput = getByPlaceholderText('Enter your email');
      const passwordInput = getByPlaceholderText('Enter your password');
      const signInButton = getByText('Sign In');
      
      fireEvent.changeText(emailInput, '<EMAIL>');
      
      // Test short password
      fireEvent.changeText(passwordInput, '123');
      fireEvent.press(signInButton);
      
      await waitFor(() => {
        expect(queryByText('Password must be at least 6 characters')).toBeTruthy();
      });
    });
  });

  describe('Accessibility', () => {
    it('has proper accessibility labels', () => {
      const { getByLabelText } = render(<LoginScreen />);
      
      expect(getByLabelText('Email address')).toBeTruthy();
      expect(getByLabelText('Password')).toBeTruthy();
      expect(getByLabelText('Sign in button')).toBeTruthy();
    });

    it('has proper accessibility hints', () => {
      const { getByPlaceholderText } = render(<LoginScreen />);
      
      const emailInput = getByPlaceholderText('Enter your email');
      const passwordInput = getByPlaceholderText('Enter your password');
      
      expect(emailInput.props.accessibilityHint).toBe('Enter your email address to sign in');
      expect(passwordInput.props.accessibilityHint).toBe('Enter your password to sign in');
    });
  });
});
