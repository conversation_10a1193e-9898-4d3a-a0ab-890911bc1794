import React, { useEffect, useState } from 'react';
import {
  StyleSheet,
  View,
  Alert,
  ActivityIndicator,
  TouchableOpacity,
} from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useAuth } from '@/lib/providers/AuthProvider';
import MagicLinkService from '@/services/magicLinkService';
import { Ionicons } from '@expo/vector-icons';

export default function VerifyScreen() {
  const { token, email, redirect } = useLocalSearchParams<{
    token: string;
    email: string;
    redirect?: string;
  }>();

  const [isVerifying, setIsVerifying] = useState(true);
  const [verificationStatus, setVerificationStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [errorMessage, setErrorMessage] = useState<string>('');
  
  const { verifyMagicLink: authVerifyMagicLink } = useAuth();

  useEffect(() => {
    if (token && email) {
      verifyMagicLink();
    } else {
      setVerificationStatus('error');
      setErrorMessage('Invalid verification link. Missing required parameters.');
      setIsVerifying(false);
    }
  }, [token, email]);

  const verifyMagicLink = async () => {
    try {
      setIsVerifying(true);
      setVerificationStatus('loading');

      console.log('Verifying magic link:', { token, email });

      const result = await authVerifyMagicLink(token!, email!);

      if (result.success) {
        setVerificationStatus('success');

        // Navigation is handled by the auth provider
        // Just show success message briefly
        setTimeout(() => {
          // Auth provider will handle navigation
        }, 1500);
      } else {
        setVerificationStatus('error');
        setErrorMessage(result.error || 'Verification failed');
      }
    } catch (error) {
      console.error('Magic link verification error:', error);
      setVerificationStatus('error');
      setErrorMessage('Network error during verification. Please try again.');
    } finally {
      setIsVerifying(false);
    }
  };

  const handleRetryVerification = () => {
    if (token && email) {
      verifyMagicLink();
    }
  };

  const handleBackToLogin = () => {
    router.replace('/auth/login');
  };

  const handleRequestNewLink = () => {
    router.replace({
      pathname: '/auth/login',
      params: { email: email || '' },
    });
  };

  const renderContent = () => {
    switch (verificationStatus) {
      case 'loading':
        return (
          <View style={styles.contentContainer}>
            <View style={styles.iconContainer}>
              <ActivityIndicator size="large" color="#012A5B" />
            </View>
            <ThemedText style={styles.title}>Verifying your magic link...</ThemedText>
            <ThemedText style={styles.description}>
              Please wait while we verify your authentication.
            </ThemedText>
          </View>
        );

      case 'success':
        return (
          <View style={styles.contentContainer}>
            <View style={[styles.iconContainer, styles.successIcon]}>
              <Ionicons name="checkmark-circle" size={64} color="#22C55E" />
            </View>
            <ThemedText style={styles.title}>Verification Successful!</ThemedText>
            <ThemedText style={styles.description}>
              You have been successfully authenticated. Redirecting you now...
            </ThemedText>
          </View>
        );

      case 'error':
        return (
          <View style={styles.contentContainer}>
            <View style={[styles.iconContainer, styles.errorIcon]}>
              <Ionicons name="close-circle" size={64} color="#EF4444" />
            </View>
            <ThemedText style={styles.title}>Verification Failed</ThemedText>
            <ThemedText style={styles.description}>
              {errorMessage}
            </ThemedText>
            
            <View style={styles.buttonContainer}>
              {token && email && (
                <TouchableOpacity
                  style={[styles.button, styles.primaryButton]}
                  onPress={handleRetryVerification}
                  disabled={isVerifying}
                >
                  <ThemedText style={styles.primaryButtonText}>
                    Try Again
                  </ThemedText>
                </TouchableOpacity>
              )}
              
              <TouchableOpacity
                style={[styles.button, styles.secondaryButton]}
                onPress={handleRequestNewLink}
              >
                <ThemedText style={styles.secondaryButtonText}>
                  Request New Link
                </ThemedText>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.button, styles.tertiaryButton]}
                onPress={handleBackToLogin}
              >
                <ThemedText style={styles.tertiaryButtonText}>
                  Back to Login
                </ThemedText>
              </TouchableOpacity>
            </View>
          </View>
        );

      default:
        return null;
    }
  };

  return (
    <ThemedView style={styles.container}>
      <View style={styles.header}>
        <ThemedText style={styles.headerTitle}>PROTEC Alumni</ThemedText>
        <ThemedText style={styles.headerSubtitle}>Magic Link Verification</ThemedText>
      </View>
      
      {renderContent()}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    paddingTop: 60,
    paddingHorizontal: 24,
    paddingBottom: 40,
    backgroundColor: '#012A5B',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#94A3B8',
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 60,
    alignItems: 'center',
  },
  iconContainer: {
    marginBottom: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  successIcon: {
    backgroundColor: '#F0FDF4',
    borderRadius: 50,
    padding: 16,
  },
  errorIcon: {
    backgroundColor: '#FEF2F2',
    borderRadius: 50,
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1E293B',
    textAlign: 'center',
    marginBottom: 16,
  },
  description: {
    fontSize: 16,
    color: '#64748B',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
  },
  buttonContainer: {
    width: '100%',
    gap: 12,
  },
  button: {
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: 'center',
  },
  primaryButton: {
    backgroundColor: '#012A5B',
  },
  primaryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButton: {
    backgroundColor: '#FFFFFF',
    borderWidth: 2,
    borderColor: '#012A5B',
  },
  secondaryButtonText: {
    color: '#012A5B',
    fontSize: 16,
    fontWeight: '600',
  },
  tertiaryButton: {
    backgroundColor: 'transparent',
  },
  tertiaryButtonText: {
    color: '#64748B',
    fontSize: 16,
    fontWeight: '500',
  },
});
