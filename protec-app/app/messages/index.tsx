import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Button, Card, Input, LoadingSpinner } from '@/components/ui';
import { useAuthStore } from '@/lib/stores/authStore';
import { useMessagesStore } from '@/lib/stores/messagesStore';
import { Conversation } from '@/lib/types';
import { format } from 'date-fns';
import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
    Alert,
    FlatList,
    RefreshControl,
    StyleSheet,
    TouchableOpacity,
    View,
} from 'react-native';

export default function MessagesScreen() {
  const [searchQuery, setSearchQuery] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const { user, isAuthenticated } = useAuthStore();
  const { conversations, isLoadingConversations, fetchConversations } = useMessagesStore();

  useEffect(() => {
    if (isAuthenticated) {
      fetchConversations();
    }
  }, [isAuthenticated, fetchConversations]);

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await fetchConversations();
    } finally {
      setRefreshing(false);
    }
  };

  const handleNewMessage = () => {
    if (!isAuthenticated) {
      Alert.alert(
        'Login Required',
        'Please log in to send messages.',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Login', onPress: () => router.push('/auth/login') },
        ]
      );
      return;
    }

    router.push('/messages/new');
  };

  const handleConversationPress = (conversation: Conversation) => {
    router.push(`/messages/thread/${conversation.id}`);
  };

  const formatLastMessageTime = (date: Date) => {
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`;
    return format(date, 'MMM dd');
  };

  const getOtherParticipant = (conversation: Conversation) => {
    return conversation.participants.find(p => p.id !== user?.id);
  };

  const filteredConversations = conversations.filter(conversation => {
    if (!searchQuery) return true;
    const otherParticipant = getOtherParticipant(conversation);
    return otherParticipant?.name.toLowerCase().includes(searchQuery.toLowerCase());
  });

  const renderConversationItem = ({ item }: { item: Conversation }) => {
    const otherParticipant = getOtherParticipant(item);
    const lastMessage = item.lastMessage;
    const isUnread = item.unreadCount > 0;

    return (
      <TouchableOpacity
        style={styles.conversationItem}
        onPress={() => handleConversationPress(item)}
      >
        <Card variant="outlined" style={[styles.conversationCard, isUnread && styles.unreadCard]}>
          <View style={styles.conversationHeader}>
            <View style={styles.participantInfo}>
              <View style={styles.avatar}>
                <ThemedText style={styles.avatarText}>
                  {otherParticipant?.name.charAt(0).toUpperCase()}
                </ThemedText>
              </View>

              <View style={styles.conversationDetails}>
                <View style={styles.nameRow}>
                  <ThemedText
                    type={isUnread ? 'defaultSemiBold' : 'default'}
                    style={styles.participantName}
                  >
                    {otherParticipant?.name}
                  </ThemedText>
                  {isUnread && (
                    <View style={styles.unreadBadge}>
                      <ThemedText style={styles.unreadCount}>
                        {item.unreadCount}
                      </ThemedText>
                    </View>
                  )}
                </View>

                <ThemedText style={styles.participantRole}>
                  {otherParticipant?.currentRole || 'Alumni'}
                </ThemedText>
              </View>
            </View>

            <ThemedText style={styles.timestamp}>
              {lastMessage && formatLastMessageTime(lastMessage.createdAt)}
            </ThemedText>
          </View>

          {lastMessage && (
            <View style={styles.lastMessageContainer}>
              <ThemedText
                style={[styles.lastMessage, isUnread && styles.unreadMessage]}
                numberOfLines={2}
              >
                {lastMessage.senderId === user?.id ? 'You: ' : ''}
                {lastMessage.content}
              </ThemedText>
            </View>
          )}
        </Card>
      </TouchableOpacity>
    );
  };

  const renderEmptyState = () => (
    <Card variant="outlined" style={styles.emptyState}>
      <ThemedText style={styles.emptyTitle}>No conversations yet</ThemedText>
      <ThemedText style={styles.emptyDescription}>
        Start connecting with fellow alumni by sending your first message.
      </ThemedText>
      <Button
        title="Start New Conversation"
        onPress={handleNewMessage}
        style={styles.emptyButton}
      />
    </Card>
  );

  if (!isAuthenticated) {
    return (
      <ThemedView style={styles.container}>
        <View style={styles.unauthenticatedState}>
          <ThemedText type="subtitle">Login Required</ThemedText>
          <ThemedText style={styles.unauthenticatedText}>
            Please log in to access your messages.
          </ThemedText>
          <Button
            title="Login"
            onPress={() => router.push('/auth/login')}
            style={styles.loginButton}
          />
        </View>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <ThemedText type="title">Messages</ThemedText>
          <ThemedText style={styles.subtitle}>
            Connect with fellow alumni
          </ThemedText>
        </View>

        <TouchableOpacity
          style={styles.newMessageButton}
          onPress={handleNewMessage}
        >
          <ThemedText style={styles.newMessageText}>✉️ New</ThemedText>
        </TouchableOpacity>
      </View>

      {/* Search */}
      <View style={styles.searchContainer}>
        <Input
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholder="Search conversations..."
          containerStyle={styles.searchInput}
        />
      </View>

      {/* Conversations List */}
      {isLoadingConversations ? (
        <LoadingSpinner text="Loading conversations..." />
      ) : (
        <FlatList
          data={filteredConversations}
          renderItem={renderConversationItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
          }
          ListEmptyComponent={renderEmptyState}
        />
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    padding: 16,
    paddingBottom: 8,
  },
  headerContent: {
    flex: 1,
  },
  subtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  newMessageButton: {
    backgroundColor: '#012A5B',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  newMessageText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingBottom: 8,
  },
  searchInput: {
    marginBottom: 0,
  },
  listContent: {
    padding: 16,
    paddingTop: 8,
  },
  conversationItem: {
    marginBottom: 8,
  },
  conversationCard: {
    marginBottom: 0,
  },
  unreadCard: {
    borderLeftWidth: 4,
    borderLeftColor: '#012A5B',
  },
  conversationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  participantInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#012A5B',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  avatarText: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  conversationDetails: {
    flex: 1,
  },
  nameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  participantName: {
    fontSize: 16,
    flex: 1,
  },
  unreadBadge: {
    backgroundColor: '#e74c3c',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 6,
  },
  unreadCount: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  participantRole: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  timestamp: {
    fontSize: 12,
    color: '#888',
    marginLeft: 8,
  },
  lastMessageContainer: {
    marginTop: 4,
  },
  lastMessage: {
    fontSize: 14,
    color: '#666',
    lineHeight: 18,
  },
  unreadMessage: {
    color: '#333',
    fontWeight: '500',
  },
  emptyState: {
    alignItems: 'center',
    padding: 32,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyDescription: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 16,
  },
  emptyButton: {
    marginTop: 8,
  },
  unauthenticatedState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  unauthenticatedText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginTop: 8,
    marginBottom: 16,
  },
  loginButton: {
    marginTop: 8,
  },
});
