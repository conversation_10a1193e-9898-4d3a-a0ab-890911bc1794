import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  TouchableOpacity,
  FlatList,
  TextInput,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { router } from 'expo-router';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Button, Card, LoadingSpinner, Input } from '@/components/ui';
import { useAuthStore } from '@/lib/stores/authStore';
import { useMessagesStore } from '@/lib/stores/messagesStore';
import { Alumni } from '@/lib/types';

export default function NewMessageScreen() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedRecipient, setSelectedRecipient] = useState<Alumni | null>(null);
  const [messageText, setMessageText] = useState('');
  const [isSending, setIsSending] = useState(false);
  const [isLoading<PERSON>lum<PERSON>, setIsLoadingAlumni] = useState(false);
  
  const { user } = useAuthStore();
  const { createConversation } = useMessagesStore();

  // Mock alumni data - replace with actual API call
  const [alumni, setAlumni] = useState<Alumni[]>([
    {
      id: '1',
      email: '<EMAIL>',
      name: 'John Doe',
      graduationYear: 2020,
      programmes: ['Computer Science'],
      currentRole: 'Software Engineer',
      company: 'Tech Corp',
      industry: 'Technology',
      skills: ['JavaScript', 'React'],
      interests: ['AI'],
      province: 'Gauteng',
      city: 'Johannesburg',
      country: 'South Africa',
      role: 'ALUMNI' as const,
      isActive: true,
    },
    {
      id: '2',
      email: '<EMAIL>',
      name: 'Jane Smith',
      graduationYear: 2019,
      programmes: ['Engineering'],
      currentRole: 'Project Manager',
      company: 'Build Corp',
      industry: 'Construction',
      skills: ['Project Management'],
      interests: ['Sustainability'],
      province: 'Western Cape',
      city: 'Cape Town',
      country: 'South Africa',
      role: 'ALUMNI' as const,
      isActive: true,
    },
  ]);

  useEffect(() => {
    // TODO: Fetch alumni list from API
    setIsLoadingAlumni(false);
  }, []);

  const handleCancel = () => {
    if (messageText.trim() || selectedRecipient) {
      Alert.alert(
        'Cancel Message',
        'Are you sure you want to cancel? Your message will be lost.',
        [
          { text: 'Continue Writing', style: 'cancel' },
          { text: 'Cancel', style: 'destructive', onPress: () => router.back() },
        ]
      );
    } else {
      router.back();
    }
  };

  const handleSelectRecipient = (recipient: Alumni) => {
    setSelectedRecipient(recipient);
    setSearchQuery('');
  };

  const handleSendMessage = async () => {
    if (!selectedRecipient || !messageText.trim() || isSending) return;

    setIsSending(true);
    try {
      const conversation = await createConversation(selectedRecipient.id, messageText.trim());
      
      Alert.alert(
        'Message Sent',
        'Your message has been sent successfully!',
        [
          { 
            text: 'OK', 
            onPress: () => {
              router.back();
              router.push(`/messages/thread/${conversation.id}`);
            }
          }
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to send message. Please try again.');
    } finally {
      setIsSending(false);
    }
  };

  const filteredAlumni = alumni.filter(alumnus => {
    if (alumnus.id === user?.id) return false; // Don't show current user
    if (!searchQuery) return true;
    return alumnus.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
           alumnus.currentRole?.toLowerCase().includes(searchQuery.toLowerCase()) ||
           alumnus.company?.toLowerCase().includes(searchQuery.toLowerCase());
  });

  const renderAlumniItem = ({ item }: { item: Alumni }) => (
    <TouchableOpacity
      style={styles.alumniItem}
      onPress={() => handleSelectRecipient(item)}
    >
      <Card variant="outlined" style={styles.alumniCard}>
        <View style={styles.alumniInfo}>
          <View style={styles.avatar}>
            <ThemedText style={styles.avatarText}>
              {item.name.charAt(0).toUpperCase()}
            </ThemedText>
          </View>
          
          <View style={styles.alumniDetails}>
            <ThemedText type="defaultSemiBold" style={styles.alumniName}>
              {item.name}
            </ThemedText>
            <ThemedText style={styles.alumniRole}>
              {item.currentRole} {item.company && `at ${item.company}`}
            </ThemedText>
            <ThemedText style={styles.alumniYear}>
              Class of {item.graduationYear}
            </ThemedText>
          </View>
        </View>
      </Card>
    </TouchableOpacity>
  );

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ThemedView style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={handleCancel}>
            <ThemedText style={styles.cancelButton}>Cancel</ThemedText>
          </TouchableOpacity>
          <ThemedText type="subtitle">New Message</ThemedText>
          <TouchableOpacity 
            onPress={handleSendMessage} 
            disabled={!selectedRecipient || !messageText.trim() || isSending}
          >
            <ThemedText style={[
              styles.sendButton,
              (!selectedRecipient || !messageText.trim() || isSending) && styles.sendButtonDisabled
            ]}>
              {isSending ? 'Sending...' : 'Send'}
            </ThemedText>
          </TouchableOpacity>
        </View>

        {/* Recipient Selection */}
        {!selectedRecipient ? (
          <View style={styles.recipientSelection}>
            <View style={styles.searchContainer}>
              <Input
                value={searchQuery}
                onChangeText={setSearchQuery}
                placeholder="Search alumni to message..."
                containerStyle={styles.searchInput}
              />
            </View>

            {isLoadingAlumni ? (
              <LoadingSpinner text="Loading alumni..." />
            ) : (
              <FlatList
                data={filteredAlumni}
                renderItem={renderAlumniItem}
                keyExtractor={(item) => item.id}
                contentContainerStyle={styles.alumniList}
                showsVerticalScrollIndicator={false}
                ListEmptyComponent={() => (
                  <Card variant="outlined" style={styles.emptyState}>
                    <ThemedText style={styles.emptyTitle}>No alumni found</ThemedText>
                    <ThemedText style={styles.emptyDescription}>
                      Try adjusting your search terms or browse the alumni directory.
                    </ThemedText>
                    <Button
                      title="Browse Directory"
                      onPress={() => router.push('/(tabs)/directory')}
                      variant="outline"
                      style={styles.emptyButton}
                    />
                  </Card>
                )}
              />
            )}
          </View>
        ) : (
          <View style={styles.messageComposer}>
            {/* Selected Recipient */}
            <Card variant="outlined" style={styles.selectedRecipientCard}>
              <View style={styles.selectedRecipientHeader}>
                <ThemedText style={styles.toLabel}>To:</ThemedText>
                <View style={styles.selectedRecipientInfo}>
                  <ThemedText type="defaultSemiBold" style={styles.selectedRecipientName}>
                    {selectedRecipient.name}
                  </ThemedText>
                  <TouchableOpacity
                    style={styles.changeRecipientButton}
                    onPress={() => setSelectedRecipient(null)}
                  >
                    <ThemedText style={styles.changeRecipientText}>Change</ThemedText>
                  </TouchableOpacity>
                </View>
              </View>
            </Card>

            {/* Message Input */}
            <Card variant="outlined" style={styles.messageCard}>
              <ThemedText style={styles.messageLabel}>Message:</ThemedText>
              <TextInput
                style={styles.messageInput}
                value={messageText}
                onChangeText={setMessageText}
                placeholder={`Write a message to ${selectedRecipient.name}...`}
                multiline
                maxLength={1000}
                editable={!isSending}
              />
              <View style={styles.characterCount}>
                <ThemedText style={styles.characterCountText}>
                  {messageText.length}/1000
                </ThemedText>
              </View>
            </Card>

            {/* Message Tips */}
            <Card variant="outlined" style={styles.tipsCard}>
              <ThemedText type="defaultSemiBold" style={styles.tipsTitle}>
                💡 Message Tips
              </ThemedText>
              <ThemedText style={styles.tipsText}>
                • Introduce yourself and mention your PROTEC connection{'\n'}
                • Be specific about why you're reaching out{'\n'}
                • Keep your message friendly and professional{'\n'}
                • Ask questions to start a meaningful conversation
              </ThemedText>
            </Card>
          </View>
        )}
      </ThemedView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  cancelButton: {
    color: '#666',
    fontSize: 16,
  },
  sendButton: {
    color: '#012A5B',
    fontSize: 16,
    fontWeight: '600',
  },
  sendButtonDisabled: {
    opacity: 0.5,
  },
  recipientSelection: {
    flex: 1,
  },
  searchContainer: {
    padding: 16,
    paddingBottom: 8,
  },
  searchInput: {
    marginBottom: 0,
  },
  alumniList: {
    padding: 16,
    paddingTop: 8,
  },
  alumniItem: {
    marginBottom: 8,
  },
  alumniCard: {
    marginBottom: 0,
  },
  alumniInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#012A5B',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  avatarText: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  alumniDetails: {
    flex: 1,
  },
  alumniName: {
    fontSize: 16,
    marginBottom: 2,
  },
  alumniRole: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  alumniYear: {
    fontSize: 12,
    color: '#888',
  },
  messageComposer: {
    flex: 1,
    padding: 16,
  },
  selectedRecipientCard: {
    marginBottom: 16,
  },
  selectedRecipientHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  toLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginRight: 12,
    color: '#666',
  },
  selectedRecipientInfo: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  selectedRecipientName: {
    fontSize: 16,
  },
  changeRecipientButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    backgroundColor: 'rgba(1, 42, 91, 0.1)',
  },
  changeRecipientText: {
    fontSize: 12,
    color: '#012A5B',
    fontWeight: '500',
  },
  messageCard: {
    marginBottom: 16,
  },
  messageLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 12,
    color: '#666',
  },
  messageInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    minHeight: 120,
    textAlignVertical: 'top',
  },
  characterCount: {
    alignItems: 'flex-end',
    marginTop: 8,
  },
  characterCountText: {
    fontSize: 12,
    color: '#666',
  },
  tipsCard: {
    backgroundColor: 'rgba(1, 42, 91, 0.05)',
  },
  tipsTitle: {
    fontSize: 16,
    marginBottom: 8,
    color: '#012A5B',
  },
  tipsText: {
    fontSize: 14,
    lineHeight: 20,
    color: '#666',
  },
  emptyState: {
    alignItems: 'center',
    padding: 32,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyDescription: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 16,
  },
  emptyButton: {
    marginTop: 8,
  },
});
