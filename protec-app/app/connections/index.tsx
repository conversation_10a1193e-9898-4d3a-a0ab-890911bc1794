import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Alumni<PERSON>ard, Button, Card, LoadingSpinner } from '@/components/ui';
import { Alumni } from '@/lib/types';
import { router } from 'expo-router';
import React, { useState } from 'react';
import {
    Alert,
    FlatList,
    StyleSheet,
    TouchableOpacity,
    View
} from 'react-native';

interface ConnectionRequest {
  id: string;
  from: Alumni;
  to: Alumni;
  message?: string;
  createdAt: Date;
  status: 'pending' | 'accepted' | 'declined';
}

export default function ConnectionsScreen() {
  const [activeTab, setActiveTab] = useState<'connections' | 'requests' | 'sent'>('connections');
  const [isLoading, setIsLoading] = useState(false);

  // Mock data - replace with actual API calls
  const [connections] = useState<Alumni[]>([
    {
      id: '1',
      email: '<EMAIL>',
      name: '<PERSON>',
      graduationYear: 2020,
      programmes: ['Computer Science'],
      currentRole: 'Software Engineer',
      company: 'Tech Corp',
      industry: 'Technology',
      skills: ['JavaScript', 'React', 'Node.js'],
      interests: ['AI', 'Web Development'],
      province: 'Gauteng',
      city: 'Johannesburg',
      country: 'South Africa',
      role: 'ALUMNI' as const,
      isActive: true,
    },
  ]);

  const [pendingRequests] = useState<ConnectionRequest[]>([
    {
      id: '1',
      from: {
        id: '2',
        email: '<EMAIL>',
        name: 'Jane Smith',
        graduationYear: 2019,
        programmes: ['Engineering'],
        currentRole: 'Project Manager',
        company: 'Build Corp',
        industry: 'Construction',
        skills: ['Project Management', 'Leadership'],
        interests: ['Sustainability', 'Innovation'],
        province: 'Western Cape',
        city: 'Cape Town',
        country: 'South Africa',
        role: 'ALUMNI' as const,
        isActive: true,
      },
      to: {
        id: 'current-user',
        email: '<EMAIL>',
        name: 'Current User',
        graduationYear: 2021,
        programmes: ['Computer Science'],
        role: 'ALUMNI' as const,
        isActive: true,
        province: 'Gauteng',
        city: 'Johannesburg',
        country: 'South Africa',
      },
      message: 'Hi! I saw your profile and would love to connect. We both work in tech and I think we could share some great insights.',
      createdAt: new Date(),
      status: 'pending',
    },
  ]);

  const [sentRequests] = useState<ConnectionRequest[]>([]);

  const handleAcceptRequest = (requestId: string) => {
    Alert.alert(
      'Accept Connection',
      'Accept this connection request?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Accept',
          onPress: () => {
            // TODO: Implement accept connection API
            Alert.alert('Success', 'Connection request accepted!');
          },
        },
      ]
    );
  };

  const handleDeclineRequest = (requestId: string) => {
    Alert.alert(
      'Decline Connection',
      'Decline this connection request?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Decline',
          style: 'destructive',
          onPress: () => {
            // TODO: Implement decline connection API
            Alert.alert('Request declined');
          },
        },
      ]
    );
  };

  const handleRemoveConnection = (connectionId: string) => {
    Alert.alert(
      'Remove Connection',
      'Are you sure you want to remove this connection?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: () => {
            // TODO: Implement remove connection API
            Alert.alert('Connection removed');
          },
        },
      ]
    );
  };

  const renderConnectionItem = ({ item }: { item: Alumni }) => (
    <View style={styles.connectionItem}>
      <AlumniCard alumni={item} showConnectButton={false} />
      <View style={styles.connectionActions}>
        <Button
          title="Message"
          onPress={() => router.push(`/messages/thread/${item.id}`)}
          variant="outline"
          size="sm"
          style={styles.connectionButton}
        />
        <Button
          title="Remove"
          onPress={() => handleRemoveConnection(item.id)}
          variant="ghost"
          size="sm"
          style={styles.connectionButton}
        />
      </View>
    </View>
  );

  const renderRequestItem = ({ item }: { item: ConnectionRequest }) => (
    <Card variant="elevated" style={styles.requestCard}>
      <View style={styles.requestHeader}>
        <View style={styles.requestInfo}>
          <ThemedText type="defaultSemiBold" style={styles.requestName}>
            {item.from.name}
          </ThemedText>
          <ThemedText style={styles.requestRole}>
            {item.from.currentRole} at {item.from.company}
          </ThemedText>
          <ThemedText style={styles.requestDate}>
            {item.createdAt.toLocaleDateString()}
          </ThemedText>
        </View>
        
        <TouchableOpacity
          onPress={() => router.push(`/alumni/${item.from.id}`)}
          style={styles.viewProfileButton}
        >
          <ThemedText style={styles.viewProfileText}>View Profile</ThemedText>
        </TouchableOpacity>
      </View>
      
      {item.message && (
        <ThemedText style={styles.requestMessage}>
          "{item.message}"
        </ThemedText>
      )}
      
      <View style={styles.requestActions}>
        <Button
          title="Accept"
          onPress={() => handleAcceptRequest(item.id)}
          style={styles.requestButton}
        />
        <Button
          title="Decline"
          onPress={() => handleDeclineRequest(item.id)}
          variant="outline"
          style={styles.requestButton}
        />
      </View>
    </Card>
  );

  const renderSentRequestItem = ({ item }: { item: ConnectionRequest }) => (
    <Card variant="elevated" style={styles.requestCard}>
      <View style={styles.requestHeader}>
        <View style={styles.requestInfo}>
          <ThemedText type="defaultSemiBold" style={styles.requestName}>
            {item.to.name}
          </ThemedText>
          <ThemedText style={styles.requestRole}>
            Request sent on {item.createdAt.toLocaleDateString()}
          </ThemedText>
          <ThemedText style={styles.requestStatus}>
            Status: {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
          </ThemedText>
        </View>
      </View>
    </Card>
  );

  const renderEmptyState = (type: string) => (
    <Card variant="outlined" style={styles.emptyState}>
      <ThemedText style={styles.emptyTitle}>
        No {type} yet
      </ThemedText>
      <ThemedText style={styles.emptyDescription}>
        {type === 'connections' && 'Start connecting with fellow alumni to build your network.'}
        {type === 'requests' && 'Connection requests from other alumni will appear here.'}
        {type === 'sent requests' && 'Your sent connection requests will appear here.'}
      </ThemedText>
      {type === 'connections' && (
        <Button
          title="Browse Alumni Directory"
          onPress={() => router.push('/(tabs)/directory')}
          style={styles.emptyButton}
        />
      )}
    </Card>
  );

  return (
    <ThemedView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <ThemedText style={styles.backButton}>‹ Back</ThemedText>
        </TouchableOpacity>
        <ThemedText type="subtitle">Connections</ThemedText>
        <View style={styles.headerSpacer} />
      </View>

      {/* Tabs */}
      <View style={styles.tabs}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'connections' && styles.activeTab]}
          onPress={() => setActiveTab('connections')}
        >
          <ThemedText style={[
            styles.tabText,
            activeTab === 'connections' && styles.activeTabText
          ]}>
            Connections ({connections.length})
          </ThemedText>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.tab, activeTab === 'requests' && styles.activeTab]}
          onPress={() => setActiveTab('requests')}
        >
          <ThemedText style={[
            styles.tabText,
            activeTab === 'requests' && styles.activeTabText
          ]}>
            Requests ({pendingRequests.length})
          </ThemedText>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.tab, activeTab === 'sent' && styles.activeTab]}
          onPress={() => setActiveTab('sent')}
        >
          <ThemedText style={[
            styles.tabText,
            activeTab === 'sent' && styles.activeTabText
          ]}>
            Sent ({sentRequests.length})
          </ThemedText>
        </TouchableOpacity>
      </View>

      {/* Content */}
      {isLoading ? (
        <LoadingSpinner text="Loading connections..." />
      ) : (
        <View style={styles.content}>
          {activeTab === 'connections' && (
            <FlatList
              data={connections}
              renderItem={renderConnectionItem}
              keyExtractor={(item) => item.id}
              showsVerticalScrollIndicator={false}
              ListEmptyComponent={() => renderEmptyState('connections')}
              contentContainerStyle={styles.listContent}
            />
          )}
          
          {activeTab === 'requests' && (
            <FlatList
              data={pendingRequests}
              renderItem={renderRequestItem}
              keyExtractor={(item) => item.id}
              showsVerticalScrollIndicator={false}
              ListEmptyComponent={() => renderEmptyState('requests')}
              contentContainerStyle={styles.listContent}
            />
          )}
          
          {activeTab === 'sent' && (
            <FlatList
              data={sentRequests}
              renderItem={renderSentRequestItem}
              keyExtractor={(item) => item.id}
              showsVerticalScrollIndicator={false}
              ListEmptyComponent={() => renderEmptyState('sent requests')}
              contentContainerStyle={styles.listContent}
            />
          )}
        </View>
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  backButton: {
    color: '#012A5B',
    fontSize: 18,
    fontWeight: '600',
  },
  headerSpacer: {
    width: 50,
  },
  tabs: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  tab: {
    flex: 1,
    paddingVertical: 16,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#012A5B',
  },
  tabText: {
    fontSize: 14,
    color: '#666',
  },
  activeTabText: {
    color: '#012A5B',
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  listContent: {
    padding: 16,
  },
  connectionItem: {
    marginBottom: 16,
  },
  connectionActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 8,
    marginTop: 8,
  },
  connectionButton: {
    minWidth: 80,
  },
  requestCard: {
    marginBottom: 16,
  },
  requestHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  requestInfo: {
    flex: 1,
  },
  requestName: {
    fontSize: 16,
    marginBottom: 4,
  },
  requestRole: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  requestDate: {
    fontSize: 12,
    color: '#888',
  },
  requestStatus: {
    fontSize: 12,
    color: '#888',
  },
  viewProfileButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    backgroundColor: 'rgba(1, 42, 91, 0.1)',
  },
  viewProfileText: {
    fontSize: 12,
    color: '#012A5B',
    fontWeight: '500',
  },
  requestMessage: {
    fontSize: 14,
    fontStyle: 'italic',
    color: '#666',
    marginBottom: 16,
    paddingLeft: 16,
    borderLeftWidth: 3,
    borderLeftColor: 'rgba(1, 42, 91, 0.2)',
  },
  requestActions: {
    flexDirection: 'row',
    gap: 12,
  },
  requestButton: {
    flex: 1,
  },
  emptyState: {
    alignItems: 'center',
    padding: 32,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyDescription: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 16,
  },
  emptyButton: {
    marginTop: 8,
  },
});
