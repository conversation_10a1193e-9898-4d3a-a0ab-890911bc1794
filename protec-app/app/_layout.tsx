import { DarkTheme, DefaultTheme, ThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useEffect } from 'react';
import 'react-native-reanimated';

import { AuthGuard } from '@/components/AuthGuard';
import { SplashScreenManager } from '@/components/SplashScreenManager';
import { useColorScheme } from '@/hooks/useColorScheme';
import { TRPCProvider } from '@/lib/providers/TRPCProvider';
import { AuthProvider } from '@/lib/providers/AuthProvider';
import { useNotificationsStore } from '@/lib/stores/notificationsStore';
import { useDeepLinking } from '@/lib/hooks/useDeepLinking';

export default function RootLayout() {
  const colorScheme = useColorScheme();
  const initializeNotifications = useNotificationsStore((state) => state.initializeNotifications);

  // Initialize deep linking
  useDeepLinking();

  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  useEffect(() => {
    initializeNotifications();
  }, [initializeNotifications]);

  if (!loaded) {
    // Async font loading only occurs in development.
    return null;
  }

  return (
    <AuthProvider>
      <TRPCProvider>
        <ThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
          <SplashScreenManager isAppReady={loaded}>
            <AuthGuard>
              <Stack>
                <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
                <Stack.Screen name="auth/login" options={{ headerShown: false }} />
                <Stack.Screen name="auth/verify" options={{ headerShown: false }} />
                <Stack.Screen name="auth/check-email" options={{ headerShown: false }} />
                <Stack.Screen name="+not-found" />
              </Stack>
            </AuthGuard>
          </SplashScreenManager>
          <StatusBar style="auto" />
        </ThemeProvider>
      </TRPCProvider>
    </AuthProvider>
  );
}
