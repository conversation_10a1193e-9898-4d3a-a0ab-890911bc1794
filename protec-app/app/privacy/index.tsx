import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { <PERSON>ton, Card } from '@/components/ui';
import { router } from 'expo-router';
import React, { useState } from 'react';
import {
    Alert,
    ScrollView,
    StyleSheet,
    Switch,
    TouchableOpacity,
    View,
} from 'react-native';

interface PrivacySettings {
  profileVisibility: 'public' | 'alumni_only' | 'private';
  showEmail: boolean;
  showPhone: boolean;
  showLocation: boolean;
  showCareerHistory: boolean;
  showEducation: boolean;
  allowMessages: boolean;
  allowConnectionRequests: boolean;
  showInDirectory: boolean;
  emailNotifications: boolean;
  pushNotifications: boolean;
}

export default function PrivacySettingsScreen() {
  const [settings, setSettings] = useState<PrivacySettings>({
    profileVisibility: 'alumni_only',
    showEmail: false,
    showPhone: false,
    showLocation: true,
    showCareerHistory: true,
    showEducation: true,
    allowMessages: true,
    allowConnectionRequests: true,
    showInDirectory: true,
    emailNotifications: true,
    pushNotifications: true,
  });

  const [hasChanges, setHasChanges] = useState(false);

  const updateSetting = <K extends keyof PrivacySettings>(
    key: K,
    value: PrivacySettings[K]
  ) => {
    setSettings(prev => ({ ...prev, [key]: value }));
    setHasChanges(true);
  };

  const handleSave = async () => {
    try {
      // TODO: Implement API call to save privacy settings
      console.log('Saving privacy settings:', settings);
      
      Alert.alert('Success', 'Privacy settings updated successfully');
      setHasChanges(false);
    } catch (error) {
      Alert.alert('Error', 'Failed to update privacy settings');
    }
  };

  const handleReset = () => {
    Alert.alert(
      'Reset Settings',
      'Are you sure you want to reset all privacy settings to default?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Reset',
          style: 'destructive',
          onPress: () => {
            setSettings({
              profileVisibility: 'alumni_only',
              showEmail: false,
              showPhone: false,
              showLocation: true,
              showCareerHistory: true,
              showEducation: true,
              allowMessages: true,
              allowConnectionRequests: true,
              showInDirectory: true,
              emailNotifications: true,
              pushNotifications: true,
            });
            setHasChanges(true);
          },
        },
      ]
    );
  };

  const SettingRow = ({
    title,
    description,
    value,
    onValueChange,
  }: {
    title: string;
    description?: string;
    value: boolean;
    onValueChange: (value: boolean) => void;
  }) => (
    <View style={styles.settingRow}>
      <View style={styles.settingInfo}>
        <ThemedText style={styles.settingTitle}>{title}</ThemedText>
        {description && (
          <ThemedText style={styles.settingDescription}>{description}</ThemedText>
        )}
      </View>
      <Switch
        value={value}
        onValueChange={onValueChange}
        trackColor={{ false: '#ddd', true: '#012A5B' }}
        thumbColor="#fff"
      />
    </View>
  );

  const VisibilitySelector = () => (
    <View style={styles.visibilityContainer}>
      <ThemedText style={styles.settingTitle}>Profile Visibility</ThemedText>
      <ThemedText style={styles.settingDescription}>
        Who can see your profile information
      </ThemedText>
      
      <View style={styles.visibilityOptions}>
        {[
          { value: 'public', label: 'Public', description: 'Anyone can see your profile' },
          { value: 'alumni_only', label: 'Alumni Only', description: 'Only PROTEC alumni can see your profile' },
          { value: 'private', label: 'Private', description: 'Only you can see your profile' },
        ].map((option) => (
          <TouchableOpacity
            key={option.value}
            style={[
              styles.visibilityOption,
              settings.profileVisibility === option.value && styles.visibilityOptionSelected,
            ]}
            onPress={() => updateSetting('profileVisibility', option.value as any)}
          >
            <View style={styles.visibilityOptionContent}>
              <ThemedText style={[
                styles.visibilityOptionLabel,
                settings.profileVisibility === option.value && styles.visibilityOptionLabelSelected,
              ]}>
                {option.label}
              </ThemedText>
              <ThemedText style={[
                styles.visibilityOptionDescription,
                settings.profileVisibility === option.value && styles.visibilityOptionDescriptionSelected,
              ]}>
                {option.description}
              </ThemedText>
            </View>
            <View style={[
              styles.radioButton,
              settings.profileVisibility === option.value && styles.radioButtonSelected,
            ]}>
              {settings.profileVisibility === option.value && (
                <View style={styles.radioButtonInner} />
              )}
            </View>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  return (
    <ThemedView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <ThemedText style={styles.backButton}>‹ Back</ThemedText>
        </TouchableOpacity>
        <ThemedText type="subtitle">Privacy Settings</ThemedText>
        <View style={styles.headerSpacer} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Profile Visibility */}
        <Card variant="outlined" style={styles.section}>
          <VisibilitySelector />
        </Card>

        {/* Contact Information */}
        <Card variant="outlined" style={styles.section}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>
            Contact Information
          </ThemedText>
          
          <SettingRow
            title="Show Email Address"
            description="Allow others to see your email address"
            value={settings.showEmail}
            onValueChange={(value) => updateSetting('showEmail', value)}
          />
          
          <SettingRow
            title="Show Phone Number"
            description="Allow others to see your phone number"
            value={settings.showPhone}
            onValueChange={(value) => updateSetting('showPhone', value)}
          />
          
          <SettingRow
            title="Show Location"
            description="Allow others to see your city and province"
            value={settings.showLocation}
            onValueChange={(value) => updateSetting('showLocation', value)}
          />
        </Card>

        {/* Professional Information */}
        <Card variant="outlined" style={styles.section}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>
            Professional Information
          </ThemedText>
          
          <SettingRow
            title="Show Career History"
            description="Allow others to see your work experience"
            value={settings.showCareerHistory}
            onValueChange={(value) => updateSetting('showCareerHistory', value)}
          />
          
          <SettingRow
            title="Show Education"
            description="Allow others to see your educational background"
            value={settings.showEducation}
            onValueChange={(value) => updateSetting('showEducation', value)}
          />
        </Card>

        {/* Communication */}
        <Card variant="outlined" style={styles.section}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>
            Communication
          </ThemedText>
          
          <SettingRow
            title="Allow Messages"
            description="Allow other alumni to send you messages"
            value={settings.allowMessages}
            onValueChange={(value) => updateSetting('allowMessages', value)}
          />
          
          <SettingRow
            title="Allow Connection Requests"
            description="Allow others to send you connection requests"
            value={settings.allowConnectionRequests}
            onValueChange={(value) => updateSetting('allowConnectionRequests', value)}
          />
        </Card>

        {/* Directory */}
        <Card variant="outlined" style={styles.section}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>
            Alumni Directory
          </ThemedText>
          
          <SettingRow
            title="Show in Directory"
            description="Include your profile in the alumni directory"
            value={settings.showInDirectory}
            onValueChange={(value) => updateSetting('showInDirectory', value)}
          />
        </Card>

        {/* Notifications */}
        <Card variant="outlined" style={styles.section}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>
            Notifications
          </ThemedText>
          
          <SettingRow
            title="Email Notifications"
            description="Receive notifications via email"
            value={settings.emailNotifications}
            onValueChange={(value) => updateSetting('emailNotifications', value)}
          />
          
          <SettingRow
            title="Push Notifications"
            description="Receive push notifications on your device"
            value={settings.pushNotifications}
            onValueChange={(value) => updateSetting('pushNotifications', value)}
          />
        </Card>

        {/* Actions */}
        <View style={styles.actions}>
          <Button
            title="Reset to Default"
            onPress={handleReset}
            variant="outline"
            style={styles.actionButton}
          />
          
          <Button
            title="Save Changes"
            onPress={handleSave}
            disabled={!hasChanges}
            style={styles.actionButton}
          />
        </View>

        <View style={styles.bottomPadding} />
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  backButton: {
    color: '#012A5B',
    fontSize: 18,
    fontWeight: '600',
  },
  headerSpacer: {
    width: 50,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 16,
  },
  sectionTitle: {
    marginBottom: 16,
  },
  settingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.05)',
  },
  settingInfo: {
    flex: 1,
    marginRight: 16,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  settingDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 18,
  },
  visibilityContainer: {
    marginBottom: 8,
  },
  visibilityOptions: {
    marginTop: 12,
  },
  visibilityOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.1)',
    marginBottom: 8,
  },
  visibilityOptionSelected: {
    borderColor: '#012A5B',
    backgroundColor: 'rgba(1, 42, 91, 0.05)',
  },
  visibilityOptionContent: {
    flex: 1,
  },
  visibilityOptionLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 2,
  },
  visibilityOptionLabelSelected: {
    color: '#012A5B',
  },
  visibilityOptionDescription: {
    fontSize: 14,
    color: '#666',
  },
  visibilityOptionDescriptionSelected: {
    color: '#012A5B',
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#ddd',
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioButtonSelected: {
    borderColor: '#012A5B',
  },
  radioButtonInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#012A5B',
  },
  actions: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 16,
  },
  actionButton: {
    flex: 1,
  },
  bottomPadding: {
    height: 32,
  },
});
