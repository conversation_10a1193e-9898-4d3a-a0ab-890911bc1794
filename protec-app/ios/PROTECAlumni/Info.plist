<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
  <dict>
    <key>CADisableMinimumFrameDurationOnPhone</key>
    <true/>
    <key>CFBundleDevelopmentRegion</key>
    <string>$(DEVELOPMENT_LANGUAGE)</string>
    <key>CFBundleDisplayName</key>
    <string>PROTEC Alumni</string>
    <key>CFBundleExecutable</key>
    <string>$(EXECUTABLE_NAME)</string>
    <key>CFBundleIdentifier</key>
    <string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
    <key>CFBundleInfoDictionaryVersion</key>
    <string>6.0</string>
    <key>CFBundleName</key>
    <string>$(PRODUCT_NAME)</string>
    <key>CFBundlePackageType</key>
    <string>$(PRODUCT_BUNDLE_PACKAGE_TYPE)</string>
    <key>CFBundleShortVersionString</key>
    <string>1.0.0</string>
    <key>CFBundleSignature</key>
    <string>????</string>
    <key>CFBundleURLTypes</key>
    <array>
      <dict>
        <key>CFBundleURLSchemes</key>
        <array>
          <string>protec-alumni</string>
          <string>com.protec.alumni</string>
        </array>
      </dict>
      <dict>
        <key>CFBundleURLSchemes</key>
        <array>
          <string>exp+protec-alumni-app</string>
        </array>
      </dict>
    </array>
    <key>CFBundleVersion</key>
    <string>1</string>
    <key>LSMinimumSystemVersion</key>
    <string>12.0</string>
    <key>LSRequiresIPhoneOS</key>
    <true/>
    <key>NSAppTransportSecurity</key>
    <dict>
      <key>NSAllowsArbitraryLoads</key>
      <false/>
      <key>NSAllowsLocalNetworking</key>
      <true/>
    </dict>
    <key>NSCalendarsFullAccessUsageDescription</key>
    <string>Allow PROTEC Alumni to access your calendar to help you save alumni events and reminders.</string>
    <key>NSCalendarsUsageDescription</key>
    <string>Allow PROTEC Alumni to access your calendar to help you save alumni events and reminders.</string>
    <key>NSCameraUsageDescription</key>
    <string>The app accesses your camera to let you take photos to share with the alumni community.</string>
    <key>NSContactsUsageDescription</key>
    <string>Allow PROTEC Alumni to access your contacts to help you connect with fellow alumni.</string>
    <key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
    <string>Allow PROTEC Alumni to use your location to show nearby alumni events and meetups.</string>
    <key>NSLocationAlwaysUsageDescription</key>
    <string>Allow PROTEC Alumni to use your location to show nearby alumni events and meetups.</string>
    <key>NSLocationWhenInUseUsageDescription</key>
    <string>Allow PROTEC Alumni to use your location to show nearby alumni events and meetups.</string>
    <key>NSMicrophoneUsageDescription</key>
    <string>This app may use the microphone for voice messages in the future.</string>
    <key>NSPhotoLibraryUsageDescription</key>
    <string>The app accesses your photos to let you share them with the alumni community.</string>
    <key>NSRemindersFullAccessUsageDescription</key>
    <string>Allow $(PRODUCT_NAME) to access your reminders</string>
    <key>NSRemindersUsageDescription</key>
    <string>Allow $(PRODUCT_NAME) to access your reminders</string>
    <key>NSUserActivityTypes</key>
    <array>
      <string>$(PRODUCT_BUNDLE_IDENTIFIER).expo.index_route</string>
    </array>
    <key>UILaunchStoryboardName</key>
    <string>SplashScreen</string>
    <key>UIRequiredDeviceCapabilities</key>
    <array>
      <string>arm64</string>
    </array>
    <key>UIRequiresFullScreen</key>
    <false/>
    <key>UIStatusBarStyle</key>
    <string>UIStatusBarStyleDefault</string>
    <key>UISupportedInterfaceOrientations</key>
    <array>
      <string>UIInterfaceOrientationPortrait</string>
      <string>UIInterfaceOrientationPortraitUpsideDown</string>
    </array>
    <key>UISupportedInterfaceOrientations~ipad</key>
    <array>
      <string>UIInterfaceOrientationPortrait</string>
      <string>UIInterfaceOrientationPortraitUpsideDown</string>
      <string>UIInterfaceOrientationLandscapeLeft</string>
      <string>UIInterfaceOrientationLandscapeRight</string>
    </array>
    <key>UIUserInterfaceStyle</key>
    <string>Automatic</string>
    <key>UIViewControllerBasedStatusBarAppearance</key>
    <false/>
  </dict>
</plist>